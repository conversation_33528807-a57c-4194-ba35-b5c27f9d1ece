global:
  chartVersion: v0.0.0-beta.1.gcc58491d
  registry:
    address: build-harbor.alauda.cn
    imagePullSecrets:
    # - harbor-secret
    # - docker-registry-secret
  images:
    content:
      code: gitlab-ce.alauda.cn/ait/compliance-operator
      repository: ait/compliance-content
      support_arm: true
      tag: v0.0.0-beta.1.gcc58491d
      thirdparty: false
    scanner:
      code: gitlab-ce.alauda.cn/ait/compliance-operator
      repository: ait/compliance-unified-scanner
      support_arm: true
      tag: v0.0.0-beta.1.gcc58491d
      thirdparty: false
    extractor:
      code: gitlab-ce.alauda.cn/ait/compliance-operator
      repository: ait/compliance-content-extractor
      support_arm: true
      tag: v0.0.0-beta.1.gcc58491d
      thirdparty: false
    report:
      code: gitlab-ce.alauda.cn/ait/compliance-operator
      repository: ait/compliance-report-service
      support_arm: true
      tag: v0.0.0-beta.1.gcc58491d
      thirdparty: false
    openscapScanner:
      code: gitlab-ce.alauda.cn/ait/compliance-operator
      repository: ait/compliance-openscap-scanner
      support_arm: true
      tag: v0.0.0-beta.1.gcc58491d
      thirdparty: false
    operator:
      code: gitlab-ce.alauda.cn/ait/compliance-operator
      repository: ait/compliance-operator
      support_arm: true
      tag: v0.0.0-beta.1.gcc58491d
      thirdparty: false
  complianceNamespace: compliance-system
