---
apiVersion: apiextensions.k8s.io/v1
kind: CustomResourceDefinition
metadata:
  annotations:
    controller-gen.kubebuilder.io/version: v0.14.0
  name: checkresults.compliance-operator.alauda.io
spec:
  group: compliance-operator.alauda.io
  names:
    kind: CheckResult
    listKind: CheckResultList
    plural: checkresults
    singular: checkresult
  scope: Namespaced
  versions:
  - name: v1alpha1
    schema:
      openAPIV3Schema:
        description: CheckResult represents the result of a compliance check
        properties:
          apiVersion:
            description: |-
              APIVersion defines the versioned schema of this representation of an object.
              Servers should convert recognized schemas to the latest internal value, and
              may reject unrecognized values.
              More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#resources
            type: string
          kind:
            description: |-
              Kind is a string value representing the REST resource this object represents.
              Servers may infer this from the endpoint the client submits requests to.
              Cannot be updated.
              In CamelCase.
              More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds
            type: string
          metadata:
            type: object
          spec:
            description: CheckResultSpec defines the result of a compliance check
            properties:
              profileName:
                description: ProfileName is the name of the profile used for the scan
                type: string
              ruleResults:
                description: RuleResults contains the results of individual rule checks
                items:
                  description: RuleResult represents the result of a single rule check
                  properties:
                    checkType:
                      description: CheckType indicates whether this is a platform
                        or node check
                      type: string
                    message:
                      description: Message provides additional information
                      type: string
                    nodeResults:
                      description: |-
                        NodeResults contains results for node-specific checks across multiple nodes
                        Only populated for node-type checks
                      items:
                        description: NodeResult represents a rule check result on
                          a specific node
                        properties:
                          evidence:
                            description: Evidence contains proof of the check result
                            type: string
                          message:
                            description: Message provides additional information
                            type: string
                          nodeName:
                            description: NodeName is the node where the check was
                              performed
                            type: string
                          status:
                            description: Status is the result of the check on this
                              specific node
                            enum:
                            - PASS
                            - FAIL
                            - MANUAL
                            - ERROR
                            - INCONSISTENT
                            - NOT-APPLICABLE
                            type: string
                        required:
                        - nodeName
                        - status
                        type: object
                      type: array
                    ruleId:
                      description: RuleID is the ID of the rule that was checked
                      type: string
                    ruleName:
                      description: RuleName is the name of the rule
                      type: string
                    severity:
                      description: Severity is the severity of the rule
                      type: string
                    status:
                      description: Status is the overall result of the rule check
                      enum:
                      - PASS
                      - FAIL
                      - MANUAL
                      - ERROR
                      - INCONSISTENT
                      - NOT-APPLICABLE
                      type: string
                  required:
                  - checkType
                  - ruleId
                  - ruleName
                  - severity
                  - status
                  type: object
                type: array
              scanName:
                description: ScanName is the name of the scan that generated this
                  result
                type: string
              timestamp:
                description: Timestamp is when the check was performed
                format: date-time
                type: string
            required:
            - profileName
            - ruleResults
            - scanName
            - timestamp
            type: object
        type: object
    served: true
    storage: true
