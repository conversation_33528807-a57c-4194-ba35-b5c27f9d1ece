---
apiVersion: apiextensions.k8s.io/v1
kind: CustomResourceDefinition
metadata:
  annotations:
    controller-gen.kubebuilder.io/version: v0.14.0
  name: rules.compliance-operator.alauda.io
spec:
  group: compliance-operator.alauda.io
  names:
    kind: Rule
    listKind: RuleList
    plural: rules
    singular: rule
  scope: Namespaced
  versions:
  - name: v1alpha1
    schema:
      openAPIV3Schema:
        description: Rule represents a compliance rule
        properties:
          apiVersion:
            description: |-
              APIVersion defines the versioned schema of this representation of an object.
              Servers should convert recognized schemas to the latest internal value, and
              may reject unrecognized values.
              More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#resources
            type: string
          kind:
            description: |-
              Kind is a string value representing the REST resource this object represents.
              Servers may infer this from the endpoint the client submits requests to.
              Cannot be updated.
              In CamelCase.
              More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds
            type: string
          metadata:
            type: object
          spec:
            description: RuleSpec defines the desired state of Rule
            properties:
              availableFixes:
                description: AvailableFixes lists available automated fixes
                items:
                  description: Fix represents an available automated fix
                  properties:
                    disruption:
                      type: string
                    platform:
                      type: string
                  required:
                  - disruption
                  - platform
                  type: object
                type: array
              checkScript:
                description: CheckScript is the script to execute for checking compliance
                type: string
              checkText:
                description: CheckText provides the specific check instructions
                type: string
              checkType:
                description: CheckType indicates whether this is a platform or node
                  check
                type: string
              description:
                description: Description provides detailed information about the rule
                type: string
              fixText:
                description: FixText provides the remediation instructions
                type: string
              id:
                description: ID is the STIG vulnerability ID
                type: string
              instructions:
                description: Instructions provide manual remediation steps
                type: string
              severity:
                description: Severity indicates the severity level of the rule
                type: string
              stig:
                description: STIG information
                properties:
                  cat:
                    type: string
                  id:
                    type: string
                type: object
              title:
                description: Title is the human-readable title of the rule
                type: string
            required:
            - checkText
            - checkType
            - description
            - fixText
            - severity
            - title
            type: object
        type: object
    served: true
    storage: true
