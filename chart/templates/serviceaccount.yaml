apiVersion: v1
kind: ServiceAccount
metadata:
  name: compliance-operator
  namespace: {{ .Values.global.complianceNamespace }}
{{- if .Values.global.registry.imagePullSecrets }}
imagePullSecrets:
{{- range $secret := .Values.global.registry.imagePullSecrets }}
  - name: {{ $secret }}
{{- end }}
{{- end }}
---
apiVersion: v1
kind: ServiceAccount
metadata:
  name: compliance-scanner
  namespace: {{ .Values.global.complianceNamespace }}
{{- if .Values.global.registry.imagePullSecrets }}
imagePullSecrets:
{{- range $secret := .Values.global.registry.imagePullSecrets }}
  - name: {{ $secret }}
{{- end }}
{{- end }}