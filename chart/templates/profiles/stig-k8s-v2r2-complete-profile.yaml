---
# STIG Kubernetes V2R2 Platform Profile
# Contains all platform-level security checks
apiVersion: compliance-operator.alauda.io/v1alpha1
kind: Profile
metadata:
  name: stig-k8s-v2r2-platform
  namespace: {{ .Values.global.complianceNamespace }}
  labels:
    compliance-operator.alauda.io/profile-type: platform
    compliance-operator.alauda.io/standard: stig
    compliance-operator.alauda.io/version: v2r2
spec:
  title: "STIG Kubernetes V2R2 Platform Security Profile"
  description: |
    This profile contains platform-level security checks based on the 
    Security Technical Implementation Guide (STIG) for Kubernetes V2R2.
    It focuses on API server, controller manager, scheduler, and etcd configurations.
  rules:
    # API Server Rules
    - name: stig-k8s-api-server-alpha-apis-disabled
    - name: stig-k8s-api-server-anonymous-auth-disabled
    - name: stig-k8s-api-server-audit-log-enabled
    - name: stig-k8s-api-server-audit-log-maxage
    - name: stig-k8s-api-server-audit-log-maxbackup
    - name: stig-k8s-api-server-audit-log-maxsize
    - name: stig-k8s-api-server-audit-policy-configured
    - name: stig-k8s-api-server-authorization-mode
    - name: stig-k8s-api-server-cipher-suites
    - name: stig-k8s-api-server-client-ca-file
    - name: stig-k8s-api-server-etcd-cafile
    - name: stig-k8s-api-server-etcd-certfile
    - name: stig-k8s-api-server-etcd-keyfile
    - name: stig-k8s-api-server-insecure-bind-address-not-set
    - name: stig-k8s-api-server-insecure-port-disabled
    - name: stig-k8s-api-server-kubelet-client-certificate
    - name: stig-k8s-api-server-secure-port-set
    - name: stig-k8s-api-server-tls-cert-file
    - name: stig-k8s-api-server-tls-min-version
    
    # Controller Manager Rules
    - name: stig-k8s-controller-manager-profiling-disabled
    - name: stig-k8s-controller-manager-root-ca-file
    - name: stig-k8s-controller-manager-secure-binding
    - name: stig-k8s-controller-manager-service-account-private-key
    - name: stig-k8s-controller-manager-tls-min-version
    - name: stig-k8s-controller-manager-use-service-account-credentials
    
    # Scheduler Rules
    - name: stig-k8s-scheduler-secure-binding
    - name: stig-k8s-scheduler-tls-min-version
    
    # ETCD Rules
    - name: stig-k8s-etcd-auto-tls-disabled
    - name: stig-k8s-etcd-cert-file
    - name: stig-k8s-etcd-client-cert-auth
    - name: stig-k8s-etcd-key-file
    - name: stig-k8s-etcd-peer-auto-tls-disabled
    - name: stig-k8s-etcd-peer-client-cert-auth
    
    # Platform Security Rules
    - name: stig-k8s-network-policies-configured
    - name: stig-k8s-pod-security-admission-control-file
    - name: stig-k8s-pod-security-standards-enabled
    - name: stig-k8s-secrets-not-environment-variables
    - name: stig-k8s-user-resources-dedicated-namespaces
    - name: stig-k8s-validating-admission-webhook
    - name: stig-k8s-version-up-to-date

---
# STIG Kubernetes V2R2 Node Profile
# Contains all node-level security checks
apiVersion: compliance-operator.alauda.io/v1alpha1
kind: Profile
metadata:
  name: stig-k8s-v2r2-node
  namespace: {{ .Values.global.complianceNamespace }}
  labels:
    compliance-operator.alauda.io/profile-type: node
    compliance-operator.alauda.io/standard: stig
    compliance-operator.alauda.io/version: v2r2
spec:
  title: "STIG Kubernetes V2R2 Node Security Profile"
  description: |
    This profile contains node-level security checks based on the 
    Security Technical Implementation Guide (STIG) for Kubernetes V2R2.
    It focuses on file permissions, ownership, and kubelet configurations.
  rules:
    # Configuration File Ownership Rules
    - name: stig-k8s-admin-conf-file-ownership
    - name: stig-k8s-controller-manager-conf-file-ownership
    - name: stig-k8s-kubelet-config-file-ownership
    - name: stig-k8s-scheduler-conf-file-ownership
    
    # Configuration File Permissions Rules
    - name: stig-k8s-admin-conf-file-permissions
    - name: stig-k8s-controller-manager-conf-file-permissions
    - name: stig-k8s-kubelet-config-file-permissions
    - name: stig-k8s-scheduler-conf-file-permissions
    
    # PKI and Certificate Rules
    - name: stig-k8s-pki-certificate-file-permissions
    - name: stig-k8s-pki-directory-ownership
    - name: stig-k8s-pki-file-permissions
    - name: stig-k8s-pki-key-file-permissions
    
    # ETCD Data Directory Rules
    - name: stig-k8s-etcd-data-directory-ownership
    - name: stig-k8s-etcd-data-directory-permissions
    
    # Manifest File Rules
    - name: stig-k8s-manifest-file-permissions
    - name: stig-k8s-manifests-owned-by-root
    
    # Kubelet Rules
    - name: stig-k8s-kubelet-anonymous-auth-disabled
    - name: stig-k8s-kubelet-authorization-mode
    - name: stig-k8s-kubelet-client-ca-file
    - name: stig-k8s-kubelet-hostname-override-disabled
    - name: stig-k8s-kubelet-readonly-port-disabled
    - name: stig-k8s-kubelet-streaming-connection-timeout
    - name: stig-k8s-kubelet-tls-cert-file
    - name: stig-k8s-kubelet-tls-private-key-file
    
    # System Security Rules
    - name: stig-k8s-old-components-removed 