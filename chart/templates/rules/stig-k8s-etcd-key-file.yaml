apiVersion: compliance-operator.alauda.io/v1alpha1
kind: Rule
metadata:
  name: stig-k8s-etcd-key-file
  namespace: compliance-system
spec:
  id: "V-242427"
  title: "Kubernetes etcd must have a key file for secure communication."
  description: |
    Kubernetes stores configuration and state information in a distributed key-value store called etcd. Anyone who can write to etcd can effectively control the Kubernetes cluster. Even just reading the contents of etcd could easily provide helpful hints to a would-be attacker. Using authenticity protection, the communication can be protected against man-in-the-middle attacks/session hijacking and the insertion of false information into sessions.

    The communication session is protected by utilizing transport encryption protocols, such as TLS. TLS provides the Kubernetes API Server and etcd with a means to be able to authenticate sessions and encrypt traffic. 

    To enable encrypted communication for etcd, the parameter key-file must be set. This parameter gives the location of the key file used to secure etcd communication.
  checkText: |
    Change to the /etc/kubernetes/manifests directory on the Kubernetes Control Plane. 

    Run the command:
        grep -i key-file *

    If the setting "key-file" is not configured in the etcd manifest  file, this is a finding.
  fixText: |
    Edit the Kubernetes etcd manifest file in the /etc/kubernetes/manifests directory on the Kubernetes Control Plane. 

    Set the value of "--key-file" to the Approved Organizational Certificate.
  checkType: "platform"
  severity: "medium"
  checkScript: |
    #!/bin/bash
    # Check if etcd key file is configured
    # STIG requirement: --key-file must be set
    
    echo "Checking etcd key file configuration..."
    
    key_file_found=false
    
    # Method 1: Check via kubectl for etcd pods
    if command -v kubectl >/dev/null 2>&1; then
      if kubectl get pod -n kube-system -l component=etcd -o yaml 2>/dev/null | grep -q "key-file"; then
        key_path=$(kubectl get pod -n kube-system -l component=etcd -o yaml 2>/dev/null | grep "key-file" | head -1 | awk '{print $2}')
        if [ -n "$key_path" ]; then
          echo "PASS: etcd key-file is configured: $key_path (kubectl)"
          key_file_found=true
        fi
      fi
    fi
    
    # Method 2: Check etcd manifest file
    if [ -f /host/etc/kubernetes/manifests/etcd.yaml ]; then
      if grep -q "key-file" /host/etc/kubernetes/manifests/etcd.yaml; then
        key_path=$(grep "key-file" /host/etc/kubernetes/manifests/etcd.yaml | head -1 | awk '{print $2}')
        if [ -n "$key_path" ]; then
          echo "PASS: etcd key-file is configured: $key_path (manifest)"
          key_file_found=true
        fi
      fi
    fi
    
    # Method 3: Check etcd process
    if pgrep etcd >/dev/null 2>&1; then
      if ps aux | grep etcd | grep -q "key-file"; then
        echo "PASS: etcd key-file is configured (process)"
        key_file_found=true
      fi
    fi
    
    if [ "$key_file_found" = true ]; then
      exit 0
    else
      echo "FAIL: etcd key-file is not configured"
      exit 1
    fi