apiVersion: compliance-operator.alauda.io/v1alpha1
kind: Rule
metadata:
  name: stig-k8s-etcd-auto-tls-disabled
  namespace: compliance-system
spec:
  id: "V-242379"
  title: "The Kubernetes etcd must use TLS to protect the confidentiality of sensitive data during electronic dissemination."
  description: |
    Kubernetes etcd will prohibit the use of SSL and unauthorized versions of TLS protocols to properly secure communication.

    The use of unsupported protocol exposes vulnerabilities to the Kubernetes by rogue traffic interceptions, man-in-the-middle attacks, and impersonation of users or services from the container platform runtime, registry, and keystore. To enable the minimum version of TLS to be used by the Kubernetes API Server, the setting "--auto-tls" must be set.
  checkText: |
    Change to the /etc/kubernetes/manifests directory on the Kubernetes Control Plane. Run the command:

    grep -i  auto-tls * 

    If the setting "--auto-tls" is not configured in the Kubernetes etcd manifest file or it is set to true, this is a finding.
  fixText: |
    Edit the Kubernetes etcd manifest file in the /etc/kubernetes/manifests directory on the Kubernetes Control Plane. 

    Set the value of "--auto-tls" to "false".
  checkType: "platform"
  severity: "medium"
  checkScript: |
    #!/bin/bash
    # Check if etcd auto-tls is disabled
    # STIG requirement: --auto-tls should be set to false or not present
    
    echo "Checking etcd auto-tls configuration..."
    
    # Method 1: Check via kubectl for etcd pods
    if command -v kubectl >/dev/null 2>&1; then
      if kubectl get pod -n kube-system -l component=etcd -o yaml 2>/dev/null | grep -q "auto-tls.*true"; then
        echo "FAIL: etcd auto-tls is enabled (kubectl)"
        exit 1
      fi
    fi
    
    # Method 2: Check etcd manifest file
    if [ -f /host/etc/kubernetes/manifests/etcd.yaml ]; then
      if grep -q "auto-tls.*true" /host/etc/kubernetes/manifests/etcd.yaml; then
        echo "FAIL: etcd auto-tls is enabled (manifest)"
        exit 1
      fi
    fi
    
    # Method 3: Check etcd process
    if pgrep etcd >/dev/null 2>&1; then
      if ps aux | grep etcd | grep -q "auto-tls.*true"; then
        echo "FAIL: etcd auto-tls is enabled (process)"
        exit 1
      fi
    fi
    
    echo "PASS: etcd auto-tls is properly configured (disabled or not present)"
    exit 0