apiVersion: compliance-operator.alauda.io/v1alpha1
kind: Rule
metadata:
  name: stig-k8s-api-server-audit-log-maxage
  namespace: compliance-system
spec:
  id: "V-242462"
  title: "Kubernetes API Server audit log retention must be set."
  description: |
    Kubernetes API Server validates and configures pods and services for the API object. The REST operation provides frontend functionality to the cluster share state. Audit logs capture individual user, administrator, and system accesses to the API Server. The retention of the audit logs is critical to forensic investigations and the ability to troubleshoot system issues. The audit log retention must be configured to prevent the loss of audit data.
  checkText: |
    Change to the /etc/kubernetes/manifests directory on the Kubernetes Control Plane. Run the command:
    grep -i audit-log-maxage *

    If the setting audit-log-maxage is not set or is set to less than 30, this is a finding.
  fixText: |
    Edit the Kubernetes API Server manifest file in the /etc/kubernetes/manifests directory on the Kubernetes Control Plane. Set the argument "--audit-log-maxage" to "30" or more.
  severity: "medium"
  checkType: "platform"
  checkScript: |
    #!/bin/bash
    # Check API Server audit log retention (maxage)
    # STIG requirement: --audit-log-maxage must be set to 30 or more
    
    echo "Checking API Server audit log retention configuration..."
    
    AUDIT_MAXAGE_CONFIGURED=false
    AUDIT_MAXAGE_VALUE=""
    
    # Method 1: Check via kubectl for API server pods
    if command -v kubectl >/dev/null 2>&1; then
      if kubectl get pod -n kube-system -l component=kube-apiserver -o yaml 2>/dev/null | grep -q "audit-log-maxage"; then
        maxage_value=$(kubectl get pod -n kube-system -l component=kube-apiserver -o yaml 2>/dev/null | grep "audit-log-maxage" | head -1 | sed 's/.*--audit-log-maxage[= ]\([^ ]*\).*/\1/')
        if [ -n "$maxage_value" ]; then
          echo "✓ Found audit-log-maxage in API server: $maxage_value (kubectl)"
          AUDIT_MAXAGE_CONFIGURED=true
          AUDIT_MAXAGE_VALUE="$maxage_value"
        fi
      fi
    fi
    
    # Method 2: Check API server manifest file
    if [ -f "/host/etc/kubernetes/manifests/kube-apiserver.yaml" ]; then
      echo "✓ Checking API server manifest: /host/etc/kubernetes/manifests/kube-apiserver.yaml"
      
      if grep -q "audit-log-maxage" "/host/etc/kubernetes/manifests/kube-apiserver.yaml"; then
        maxage_value=$(grep "audit-log-maxage" "/host/etc/kubernetes/manifests/kube-apiserver.yaml" | head -1 | sed 's/.*--audit-log-maxage[= ]\([^ ]*\).*/\1/')
        if [ -n "$maxage_value" ]; then
          echo "  ✓ Found audit-log-maxage in manifest: $maxage_value"
          AUDIT_MAXAGE_CONFIGURED=true
          AUDIT_MAXAGE_VALUE="$maxage_value"
        fi
      else
        echo "  ✗ No audit-log-maxage found in manifest"
      fi
    fi
    
    # Method 3: Check running API server process
    if [ -d "/host/proc" ]; then
      for pid in $(ls /host/proc | grep '^[0-9]*$'); do
        if [ -f "/host/proc/$pid/cmdline" ] && grep -q kube-apiserver "/host/proc/$pid/cmdline" 2>/dev/null; then
          cmdline=$(tr '\0' ' ' < "/host/proc/$pid/cmdline")
          if echo "$cmdline" | grep -q "audit-log-maxage"; then
            maxage_value=$(echo "$cmdline" | sed 's/.*--audit-log-maxage[= ]\([^ ]*\).*/\1/')
            echo "✓ Found audit-log-maxage in API server process: $maxage_value"
            AUDIT_MAXAGE_CONFIGURED=true
            AUDIT_MAXAGE_VALUE="$maxage_value"
          fi
          break
        fi
      done
    fi
    
    if [ "$AUDIT_MAXAGE_CONFIGURED" = false ]; then
      echo "FAIL: API Server audit-log-maxage is not configured"
      exit 1
    fi
    
    # Check if the value is numeric and >= 30
    if ! [[ "$AUDIT_MAXAGE_VALUE" =~ ^[0-9]+$ ]]; then
      echo "FAIL: audit-log-maxage value is not numeric: $AUDIT_MAXAGE_VALUE"
      exit 1
    fi
    
    if [ "$AUDIT_MAXAGE_VALUE" -lt 30 ]; then
      echo "FAIL: audit-log-maxage is set to $AUDIT_MAXAGE_VALUE (must be 30 or more)"
      exit 1
    fi
    
    echo "PASS: API Server audit-log-maxage is properly configured: $AUDIT_MAXAGE_VALUE days"
    exit 0