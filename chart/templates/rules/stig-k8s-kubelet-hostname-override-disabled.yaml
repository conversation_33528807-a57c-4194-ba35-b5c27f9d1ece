apiVersion: compliance-operator.alauda.io/v1alpha1
kind: Rule
metadata:
  name: stig-k8s-kubelet-hostname-override-disabled
  namespace: compliance-system
spec:
  id: "V-242404"
  title: "Kubernetes Kubelet must deny hostname override."
  description: |
    Kubernetes allows for the overriding of hostnames. Allowing this feature to be implemented within the kubelets may break the TLS setup between the kubelet service and the API server. This setting also can make it difficult to associate logs with nodes if security analytics needs to take place. The better practice is to setup nodes with resolvable FQDNs and avoid overriding the hostnames.
  checkText: |
    On the Control Plane and Worker nodes, run the command:
    ps -ef | grep kubelet

    If the option "--hostname-override" is present, this is a finding.
  fixText: |
    Run the command:  
    systemctl status kubelet.  
    Note the path to the drop-in file.

    Determine the path to the environment file(s) with the command: 
    grep -i EnvironmentFile <path_to_drop_in_file>.

    Remove the "--hostname-override" option from any environment file where it is present.  

    Restart the kubelet service using the following command:
    systemctl daemon-reload && systemctl restart kubelet
  severity: "medium"
  checkType: "node"
  checkScript: |
    #!/bin/bash
    # Check if kubelet hostname override is disabled
    # STIG requirement: --hostname-override should not be present in kubelet configuration
    
    echo "Checking kubelet hostname override configuration..."
    
    HOSTNAME_OVERRIDE_FOUND=false
    
    # Method 1: Check running kubelet process
    if [ -d "/host/proc" ]; then
      for pid in $(ls /host/proc | grep '^[0-9]*$'); do
        if [ -f "/host/proc/$pid/cmdline" ] && grep -q kubelet "/host/proc/$pid/cmdline" 2>/dev/null; then
          cmdline=$(tr '\0' ' ' < "/host/proc/$pid/cmdline")
          echo "✓ Found kubelet process (PID: $pid)"
          
          if echo "$cmdline" | grep -q -- "--hostname-override"; then
            hostname_value=$(echo "$cmdline" | sed 's/.*--hostname-override[= ]\([^ ]*\).*/\1/')
            echo "  ✗ VIOLATION: Found --hostname-override in kubelet process: $hostname_value"
            HOSTNAME_OVERRIDE_FOUND=true
          else
            echo "  ✓ OK: No --hostname-override found in kubelet process"
          fi
          break
        fi
      done
    fi
    
    # Method 2: Check kubelet config files
    KUBELET_CONFIG_PATHS=(
      "/host/var/lib/kubelet/config.yaml"
      "/host/etc/kubernetes/kubelet/kubelet-config.yaml"
      "/host/etc/kubernetes/kubelet.yaml"
    )
    
    for config_path in "${KUBELET_CONFIG_PATHS[@]}"; do
      if [ -f "$config_path" ]; then
        echo "✓ Checking kubelet config file: $config_path"
        
        if grep -q "hostnameOverride:" "$config_path"; then
          hostname_value=$(grep "hostnameOverride:" "$config_path" | sed 's/.*hostnameOverride: *\([^[:space:]]*\).*/\1/')
          echo "  ✗ VIOLATION: Found hostnameOverride in config file: $hostname_value"
          HOSTNAME_OVERRIDE_FOUND=true
        else
          echo "  ✓ OK: No hostnameOverride found in config file"
        fi
      fi
    done
    
    # Method 3: Check systemd service files and environment files
    SYSTEMD_PATHS=(
      "/host/etc/systemd/system/kubelet.service"
      "/host/etc/systemd/system/kubelet.service.d"
      "/host/lib/systemd/system/kubelet.service"
      "/host/usr/lib/systemd/system/kubelet.service"
    )
    
    for systemd_path in "${SYSTEMD_PATHS[@]}"; do
      if [ -f "$systemd_path" ]; then
        echo "✓ Checking systemd service file: $systemd_path"
        
        if grep -q -- "--hostname-override" "$systemd_path"; then
          echo "  ✗ VIOLATION: Found --hostname-override in systemd service file"
          HOSTNAME_OVERRIDE_FOUND=true
        fi
        
        # Check for EnvironmentFile references
        if grep -q "EnvironmentFile" "$systemd_path"; then
          env_files=$(grep "EnvironmentFile" "$systemd_path" | sed 's/.*EnvironmentFile[= ]*\([^[:space:]]*\).*/\1/')
          for env_file in $env_files; do
            if [ -f "/host$env_file" ]; then
              echo "  ✓ Checking environment file: /host$env_file"
              if grep -q -- "--hostname-override" "/host$env_file"; then
                echo "    ✗ VIOLATION: Found --hostname-override in environment file"
                HOSTNAME_OVERRIDE_FOUND=true
              fi
            fi
          done
        fi
      elif [ -d "$systemd_path" ]; then
        echo "✓ Checking systemd service directory: $systemd_path"
        for drop_in_file in "$systemd_path"/*.conf; do
          if [ -f "$drop_in_file" ]; then
            echo "  ✓ Checking drop-in file: $drop_in_file"
            if grep -q -- "--hostname-override" "$drop_in_file"; then
              echo "    ✗ VIOLATION: Found --hostname-override in drop-in file"
              HOSTNAME_OVERRIDE_FOUND=true
            fi
          fi
        done
      fi
    done
    
    if [ "$HOSTNAME_OVERRIDE_FOUND" = true ]; then
      echo "FAIL: Found kubelet hostname override configuration"
      exit 1
    else
      echo "PASS: No kubelet hostname override found"
      exit 0
    fi