apiVersion: compliance-operator.alauda.io/v1alpha1
kind: Rule
metadata:
  name: stig-k8s-etcd-client-cert-auth
  namespace: compliance-system
spec:
  id: "V-242423"
  title: "Kubernetes etcd must enable client authentication to secure service."
  description: |
    Kubernetes container and pod configuration are maintained by Kubelet. Kubelet agents register nodes with the API Server, mount volume storage, and perform health checks for containers and pods. Anyone who gains access to Kubelet agents can effectively control applications within the pods and containers. Using authenticity protection, the communication can be protected against man-in-the-middle attacks/session hijacking and the insertion of false information into sessions.

    The communication session is protected by utilizing transport encryption protocols, such as TLS. TLS provides the Kubernetes API Server with a means to be able to authenticate sessions and encrypt traffic.

    To enable encrypted communication for Kubelet, the parameter client-cert-auth must be set. This parameter gives the location of the SSL Certificate Authority file used to secure Kubelet communication.
  checkText: |
    Change to the /etc/kubernetes/manifests directory on the Kubernetes Control Plane. Run the command:

    grep -i client-cert-auth * 

    If the setting client-cert-auth is not configured in the Kubernetes etcd manifest file or set to "false", this is a finding.
  fixText: |
    Edit the Kubernetes etcd manifest file in the /etc/kubernetes/manifests directory on the Kubernetes Control Plane. 

    Set the value of "--client-cert-auth" to "true" for the etcd.
  checkType: "platform"
  severity: "medium"
  checkScript: |
    #!/bin/bash
    # Check if etcd client certificate authentication is enabled
    # STIG requirement: --client-cert-auth should be set to true
    
    echo "Checking etcd client certificate authentication configuration..."
    
    client_cert_auth_found=false
    
    # Method 1: Check via kubectl for etcd pods
    if command -v kubectl >/dev/null 2>&1; then
      if kubectl get pod -n kube-system -l component=etcd -o yaml 2>/dev/null | grep -q "client-cert-auth.*true"; then
        echo "PASS: etcd client-cert-auth is enabled (kubectl)"
        client_cert_auth_found=true
      fi
    fi
    
    # Method 2: Check etcd manifest file
    if [ -f /host/etc/kubernetes/manifests/etcd.yaml ]; then
      if grep -q "client-cert-auth.*true" /host/etc/kubernetes/manifests/etcd.yaml; then
        echo "PASS: etcd client-cert-auth is enabled (manifest)"
        client_cert_auth_found=true
      fi
    fi
    
    # Method 3: Check etcd process
    if pgrep etcd >/dev/null 2>&1; then
      if ps aux | grep etcd | grep -q "client-cert-auth.*true"; then
        echo "PASS: etcd client-cert-auth is enabled (process)"
        client_cert_auth_found=true
      fi
    fi
    
    if [ "$client_cert_auth_found" = true ]; then
      exit 0
    else
      echo "FAIL: etcd client-cert-auth is not enabled or not configured"
      exit 1
    fi