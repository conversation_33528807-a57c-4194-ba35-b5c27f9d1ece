apiVersion: compliance-operator.alauda.io/v1alpha1
kind: Rule
metadata:
  name: stig-k8s-api-server-audit-log-enabled
  namespace: compliance-system
spec:
  id: "V-242461"
  title: "Kubernetes API Server audit logs must be enabled."
  description: |
    Kubernetes API Server validates and configures pods and services for the API object. The REST operation provides frontend functionality to the cluster share state. Enabling audit logs provides a way to monitor and identify security risk events or misuse of information. Audit logs are necessary to provide evidence in the case the Kubernetes API Server is compromised requiring a Cyber Security Investigation.
  checkText: |
    Change to the /etc/kubernetes/manifests directory on the Kubernetes Control Plane. Run the command:
    grep -i audit-policy-file * 

    If the setting "audit-policy-file" is not set or is found in the Kubernetes API manifest file without valid content, this is a finding.
  fixText: |
    Edit the Kubernetes API Server manifest file in the /etc/kubernetes/manifests directory on the Kubernetes Control Plane. Set the argument "--audit-policy-file" to "log file directory".
  severity: "medium"
  checkType: "platform"
  checkScript: |
    #!/bin/bash
    # Check if API Server audit logs are enabled
    # STIG requirement: --audit-policy-file must be configured
    
    echo "Checking API Server audit log configuration..."
    
    AUDIT_POLICY_CONFIGURED=false
    
    # Method 1: Check via kubectl for API server pods
    if command -v kubectl >/dev/null 2>&1; then
      if kubectl get pod -n kube-system -l component=kube-apiserver -o yaml 2>/dev/null | grep -q "audit-policy-file"; then
        policy_file=$(kubectl get pod -n kube-system -l component=kube-apiserver -o yaml 2>/dev/null | grep "audit-policy-file" | head -1 | sed 's/.*--audit-policy-file[= ]\([^ ]*\).*/\1/')
        if [ -n "$policy_file" ]; then
          echo "✓ Found audit-policy-file in API server: $policy_file (kubectl)"
          AUDIT_POLICY_CONFIGURED=true
        fi
      fi
    fi
    
    # Method 2: Check API server manifest file
    if [ -f "/host/etc/kubernetes/manifests/kube-apiserver.yaml" ]; then
      echo "✓ Checking API server manifest: /host/etc/kubernetes/manifests/kube-apiserver.yaml"
      
      if grep -q "audit-policy-file" "/host/etc/kubernetes/manifests/kube-apiserver.yaml"; then
        policy_file=$(grep "audit-policy-file" "/host/etc/kubernetes/manifests/kube-apiserver.yaml" | head -1 | sed 's/.*--audit-policy-file[= ]\([^ ]*\).*/\1/')
        if [ -n "$policy_file" ]; then
          echo "  ✓ Found audit-policy-file in manifest: $policy_file"
          AUDIT_POLICY_CONFIGURED=true
          
          # Verify the policy file exists and has valid content
          if [ -f "/host$policy_file" ]; then
            echo "  ✓ Audit policy file exists: /host$policy_file"
            
            # Check if policy file has basic required content
            if grep -q "apiVersion.*audit" "/host$policy_file" && grep -q "kind: Policy" "/host$policy_file"; then
              echo "  ✓ Audit policy file appears to have valid content"
            else
              echo "  ✗ WARNING: Audit policy file may not have valid content"
            fi
          else
            echo "  ✗ WARNING: Audit policy file does not exist: /host$policy_file"
          fi
        fi
      else
        echo "  ✗ No audit-policy-file found in manifest"
      fi
    fi
    
    # Method 3: Check running API server process
    if [ -d "/host/proc" ]; then
      for pid in $(ls /host/proc | grep '^[0-9]*$'); do
        if [ -f "/host/proc/$pid/cmdline" ] && grep -q kube-apiserver "/host/proc/$pid/cmdline" 2>/dev/null; then
          cmdline=$(tr '\0' ' ' < "/host/proc/$pid/cmdline")
          if echo "$cmdline" | grep -q "audit-policy-file"; then
            policy_file=$(echo "$cmdline" | sed 's/.*--audit-policy-file[= ]\([^ ]*\).*/\1/')
            echo "✓ Found audit-policy-file in API server process: $policy_file"
            AUDIT_POLICY_CONFIGURED=true
          fi
          break
        fi
      done
    fi
    
    if [ "$AUDIT_POLICY_CONFIGURED" = false ]; then
      echo "FAIL: API Server audit logging is not configured (--audit-policy-file not found)"
      exit 1
    fi
    
    echo "PASS: API Server audit logging is properly configured"
    exit 0