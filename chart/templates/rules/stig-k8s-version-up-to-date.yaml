apiVersion: compliance-operator.alauda.io/v1alpha1
kind: Rule
metadata:
  name: stig-k8s-version-up-to-date
  namespace: compliance-system
spec:
  id: "V-242434"
  title: "Kubernetes must contain the latest updates as authorized by IAVMs, CTOs, DTMs, and STIGs."
  description: |
    Kubernetes software must stay up to date with the latest patches, service packs, and hot fixes. Flaws discovered during security assessments, continuous monitoring, incident response activities, or information system error handling must also be addressed expeditiously.
    
    Organization-defined time periods for updating security-relevant software may vary based on a variety of factors including, for example, the security category of the information system or the criticality of the update (i.e., severity of the vulnerability related to the discovered flaw).
    
    This requirement will apply to software patch management solutions that are used to install patches across the enclave and also to applications themselves that are not part of that patch management solution. For example, many browsers today provide the capability to install their own patch software. Patch criticality, as well as system criticality, will vary. Therefore, the tactical situations regarding the patch management process will also vary. This means that the time period used must be a configurable parameter. Time frames for application of security-relevant software updates may be dependent upon the Information Assurance Vulnerability Management (IAVM) process.
  checkText: |
    Verify that Kubernetes software is updated to the latest version as authorized by IAVMs, CTOs, DTMs, and STIGs.
    
    Check the current Kubernetes version by running:
    kubectl version --short
    
    Compare the version with the latest stable release and any security advisories.
    
    If Kubernetes is not updated to the latest authorized version, this is a finding.
  fixText: |
    Update Kubernetes to the latest authorized version following organizational change management procedures:
    
    1. Review current version and available updates
    2. Check for security advisories and IAVMs
    3. Plan and test the update in a non-production environment
    4. Apply updates following organizational procedures
    5. Verify the update was successful
  severity: "medium"
  checkType: "platform"
  checkScript: |
    #!/bin/bash
    # Check if Kubernetes version is reasonably current
    
    echo "Checking Kubernetes version..."
    
    VERSION_OK=false
    
    if ! command -v kubectl >/dev/null 2>&1; then
      echo "FAIL: kubectl not available"
      exit 1
    fi
    
    # Get both client and server versions
    CLIENT_VERSION=$(kubectl version --short --client 2>/dev/null | grep "Client Version" | awk '{print $3}' | sed 's/v//')
    SERVER_VERSION=$(kubectl version --short 2>/dev/null | grep "Server Version" | awk '{print $3}' | sed 's/v//')
    
    if [ -z "$CLIENT_VERSION" ] && [ -z "$SERVER_VERSION" ]; then
      echo "FAIL: Unable to retrieve Kubernetes version information"
      exit 1
    fi
    
    # Function to check if version is acceptable (1.25+)
    check_version() {
      local version=$1
      local version_name=$2
      
      if [ -z "$version" ]; then
        return 1
      fi
      
      MAJOR_VERSION=$(echo "$version" | cut -d. -f1)
      MINOR_VERSION=$(echo "$version" | cut -d. -f2)
      
      # Check if version is 1.25 or higher (adjust as needed for security requirements)
      if [ "$MAJOR_VERSION" -ge 1 ] && [ "$MINOR_VERSION" -ge 25 ]; then
        echo "PASS: $version_name version $version is acceptable (1.25+)"
        return 0
      else
        echo "FAIL: $version_name version $version is outdated (minimum required: 1.25)"
        return 1
      fi
    }
    
    # Check server version (more important for security)
    SERVER_OK=0
    if [ -n "$SERVER_VERSION" ]; then
      if check_version "$SERVER_VERSION" "Server"; then
        SERVER_OK=1
      else
        SERVER_OK=0
      fi
    else
      echo "WARN: Server version not available"
      SERVER_OK=0
    fi
    
    # Check client version
    CLIENT_OK=0
    if [ -n "$CLIENT_VERSION" ]; then
      if check_version "$CLIENT_VERSION" "Client"; then
        CLIENT_OK=1
      else
        CLIENT_OK=0
      fi
    else
      echo "WARN: Client version not available"
      CLIENT_OK=0
    fi
    
    # Additional security checks
    if [ -n "$SERVER_VERSION" ]; then
      # Check for known vulnerable versions (example)
      case "$SERVER_VERSION" in
        1.24.*|1.23.*|1.22.*|1.21.*|1.20.*)
          echo "SECURITY: Server version $SERVER_VERSION may have known security vulnerabilities"
          ;;
      esac
    fi
    
    # Final determination
    if [ "$SERVER_OK" -eq 1 ] || ([ "$SERVER_OK" -eq 0 ] && [ "$CLIENT_OK" -eq 1 ]); then
      VERSION_OK=true
    else
      echo "FAIL: Kubernetes version(s) do not meet minimum security requirements"
      VERSION_OK=false
    fi
    
    # Return appropriate exit code
    if [ "$VERSION_OK" = "true" ]; then
      exit 0
    else
      exit 1
    fi
