apiVersion: compliance-operator.alauda.io/v1alpha1
kind: Rule
metadata:
  name: stig-k8s-user-resources-dedicated-namespaces
  namespace: compliance-system
spec:
  id: "V-242383"
  title: "User-managed resources must be created in dedicated namespaces."
  description: |
    Creating namespaces for user-managed resources is important when implementing Role-Based Access Controls (RBAC). RBAC allows for the authorization of users and helps support proper API server permissions separation and network micro segmentation. If user-managed resources are placed within the default namespaces, it becomes impossible to implement policies for RBAC permission, service account usage, network policies, and more.
  checkText: |
    To view the available namespaces, run the command:
    kubectl get namespaces
    
    The default namespaces to be validated are default, kube-public, and kube-node-lease if it is created.
    
    For the default namespace, execute the commands:
    kubectl config set-context --current --namespace=default
    kubectl get all
    
    For the kube-public namespace, execute the commands:
    kubectl config set-context --current --namespace=kube-public
    kubectl get all
    
    For the kube-node-lease namespace, execute the commands:
    kubectl config set-context --current --namespace=kube-node-lease
    kubectl get all
    
    The only valid return values are the kubernetes service (i.e., service/kubernetes) and nothing at all.
    
    If a return value is returned from the "kubectl get all" command and it is not the kubernetes service (i.e., service/kubernetes), this is a finding.
  fixText: |
    Move any user-managed resources from the default, kube-public, and kube-node-lease namespaces to user namespaces.
  severity: "high"
  checkType: "platform"
  checkScript: |
    #!/bin/bash
    # Check if user-managed resources exist in default namespaces
    # V-242383: User-managed resources must be created in dedicated namespaces
    
    if ! command -v kubectl >/dev/null 2>&1; then
      echo "FAIL: kubectl not available"
      exit 1
    fi
    
    # Define system namespaces that should not contain user resources
    SYSTEM_NAMESPACES="default kube-public kube-node-lease"
    FINDING_COUNT=0
    
    for namespace in $SYSTEM_NAMESPACES; do
      # Check if namespace exists
      if ! kubectl get namespace "$namespace" >/dev/null 2>&1; then
        continue
      fi
      
      # Get all resources in the namespace, excluding the kubernetes service
      RESOURCES=$(kubectl get all -n "$namespace" --no-headers 2>/dev/null | grep -v "^service/kubernetes")
      
      if [ -n "$RESOURCES" ]; then
        echo "FAIL: Found user-managed resources in system namespace '$namespace':"
        echo "$RESOURCES"
        FINDING_COUNT=$((FINDING_COUNT + 1))
      fi
    done
    
    if [ "$FINDING_COUNT" -eq 0 ]; then
      echo "PASS: No user-managed resources found in system namespaces"
      exit 0
    else
      echo "FAIL: Found user-managed resources in $FINDING_COUNT system namespace(s)"
      exit 1
    fi