apiVersion: compliance-operator.alauda.io/v1alpha1
kind: Rule
metadata:
  name: stig-k8s-kubelet-tls-cert-file
  namespace: compliance-system
spec:
  id: "V-242430"
  title: "Kubernetes Kubelet must enable tlsCertFile for client authentication to secure service."
  description: |
    Kubernetes container and pod configuration are maintained by Kubelet. Kubelet agents register nodes with the API Server, mount volume storage, and perform health checks for containers and pods. Anyone who gains access to Kubelet agents can effectively control applications within the pods and containers. Using authenticity protection, the communication can be protected against man-in-the-middle attacks/session hijacking and the insertion of false information into sessions.
    
    The communication session is protected by utilizing transport encryption protocols, such as TLS. TLS provides the Kubernetes API Server with a means to be able to authenticate sessions and encrypt traffic.
    
    To enable encrypted communication for Kubelet, the parameter tlsCertFile must be set. This parameter gives the location of the SSL Certificate Authority file used to secure Kubelet communication.
  checkText: |
    On the Control Plane and Worker Nodes, run the command:
    ps -ef | grep kubelet
    
    Check the config file(s) referenced by the --config argument (example: --config=/etc/kubernetes/kubelet/kubelet-config.yaml):
    
    Change to the directory identified by --config argument and run the command:
    grep -i tlscertfile *
    
    If the setting tlsCertFile is not configured, this is a finding.
  fixText: |
    On the Control Plane and Worker Nodes, run the command:
    ps -ef | grep kubelet
    
    Check the config file(s) referenced by the --config argument (example: --config=/etc/kubernetes/kubelet/kubelet-config.yaml):
    
    Edit the Kubernetes Kubelet config file:
    Set the value of tlsCertFile to the Approved Organizational Certificate.
    
    Reset Kubelet service using the following command:
    service kubelet restart
  severity: "medium"
  checkType: "node"
  checkScript: |
    #!/bin/bash
    # Check if kubelet TLS cert file is configured
    # STIG requirement: tlsCertFile must be set in config or as command line parameter
    
    echo "Checking kubelet TLS certificate file configuration..."
    
    CERT_FILE_CONFIGURED=false
    CONFIG_FOUND=false
    
    # Check kubelet config file first
    for config_path in "/host/var/lib/kubelet/config.yaml" "/host/etc/kubernetes/kubelet/kubelet-config.yaml" "/host/etc/kubernetes/kubelet.yaml"; do
      if [ -f "$config_path" ]; then
        CONFIG_FOUND=true
        echo "✓ Found kubelet config file: $config_path"
        
        if grep -q "tlsCertFile:" "$config_path"; then
          CERT_FILE=$(grep "tlsCertFile:" "$config_path" | sed 's/.*tlsCertFile: *"\?\([^"]*\)"\?.*/\1/')
          echo "✓ Found tlsCertFile setting: $CERT_FILE"
          
          if [ -n "$CERT_FILE" ]; then
            if [ -f "/host$CERT_FILE" ]; then
              echo "✓ Certificate file exists: /host$CERT_FILE"
              CERT_FILE_CONFIGURED=true
            else
              echo "✗ WARNING: Certificate file does not exist: /host$CERT_FILE"
            fi
          else
            echo "✗ tlsCertFile is set but has no value"
          fi
        else
          echo "ℹ No tlsCertFile setting found in config file"
        fi
      fi
    done
    
    # Check kubelet process via host proc filesystem
    if [ -d /host/proc ]; then
      for pid in $(ls /host/proc | grep '^[0-9]*$'); do
        if [ -f "/host/proc/$pid/cmdline" ] && grep -q kubelet "/host/proc/$pid/cmdline" 2>/dev/null; then
          echo "✓ Found kubelet process"
          
          # Extract config file path if available
          if tr '\0' ' ' < "/host/proc/$pid/cmdline" | grep -q -- "--config"; then
            config_path=$(tr '\0' ' ' < "/host/proc/$pid/cmdline" | grep -o -- "--config=[^ ]*" | cut -d= -f2)
            if [ -n "$config_path" ] && [ -f "/host$config_path" ] && [ "$CONFIG_FOUND" = false ]; then
              CONFIG_FOUND=true
              echo "✓ Found kubelet config from process: /host$config_path"
              
              if grep -q "tlsCertFile:" "/host$config_path"; then
                CERT_FILE=$(grep "tlsCertFile:" "/host$config_path" | sed 's/.*tlsCertFile: *"\?\([^"]*\)"\?.*/\1/')
                echo "✓ Found tlsCertFile setting: $CERT_FILE"
                
                if [ -n "$CERT_FILE" ]; then
                  if [ -f "/host$CERT_FILE" ]; then
                    echo "✓ Certificate file exists: /host$CERT_FILE"
                    CERT_FILE_CONFIGURED=true
                  else
                    echo "✗ WARNING: Certificate file does not exist: /host$CERT_FILE"
                  fi
                else
                  echo "✗ tlsCertFile is set but has no value"
                fi
              else
                echo "ℹ No tlsCertFile setting found in config file"
              fi
            fi
          fi
          
          # Check for command line parameter
          if tr '\0' ' ' < "/host/proc/$pid/cmdline" | grep -q -- "--tls-cert-file"; then
            CERT_FILE=$(tr '\0' ' ' < "/host/proc/$pid/cmdline" | grep -o -- "--tls-cert-file=[^ ]*" | cut -d= -f2)
            echo "✓ Found --tls-cert-file parameter: $CERT_FILE"
            
            if [ -n "$CERT_FILE" ]; then
              if [ -f "/host$CERT_FILE" ]; then
                echo "✓ Certificate file exists: /host$CERT_FILE"
                CERT_FILE_CONFIGURED=true
              else
                echo "✗ WARNING: Certificate file does not exist: /host$CERT_FILE"
              fi
            else
              echo "✗ --tls-cert-file is set but has no value"
            fi
          fi
          
          break
        fi
      done
    fi
    
    # Final evaluation
    echo ""
    echo "=== EVALUATION ==="
    echo "Config found: $CONFIG_FOUND"
    echo "Certificate file configured: $CERT_FILE_CONFIGURED"
    
    if [ "$CERT_FILE_CONFIGURED" = true ]; then
      echo "PASS: Kubelet TLS certificate file is properly configured"
      exit 0
    else
      echo "FAIL: Kubelet TLS certificate file is not properly configured"
      exit 1
    fi
