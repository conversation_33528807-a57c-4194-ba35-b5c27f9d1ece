apiVersion: compliance-operator.alauda.io/v1alpha1
kind: Rule
metadata:
  name: stig-k8s-controller-manager-root-ca-file
  namespace: compliance-system
spec:
  id: "V-242421"
  title: "Kubernetes Controller Manager must have the SSL Certificate Authority set."
  description: |
    The Kubernetes Controller Manager is responsible for creating service accounts and tokens for the API Server, maintaining the correct number of pods for every replication controller and provides notifications when nodes are offline.  

    Anyone who gains access to the Controller Manager can generate backdoor accounts, take possession of, or diminish system performance without detection by disabling system notification. Using authenticity protection, the communication can be protected against man-in-the-middle attacks/session hijacking and the insertion of false information into sessions.

    The communication session is protected by utilizing transport encryption protocols, such as TLS. TLS provides the Kubernetes Controller Manager with a means to be able to authenticate sessions and encrypt traffic.
  checkText: |
    Change to the /etc/kubernetes/manifests/ directory on the Kubernetes Control Plane. Run the command:
    grep -i root-ca-file *

    If the setting "--root-ca-file" is not set in the Kubernetes Controller Manager manifest file or contains no value, this is a finding.
  fixText: |
    Edit the Kubernetes Controller Manager manifest file in the /etc/kubernetes/manifests directory on the Kubernetes Control Plane. 

    Set the value of "--root-ca-file" to path containing Approved Organizational Certificate.
  checkType: "platform"
  severity: "medium"
  checkScript: |
    #!/bin/bash
    # Check if Controller Manager root CA file is configured
    # STIG requirement: --root-ca-file must be set
    
    echo "Checking Controller Manager root CA file configuration..."
    
    root_ca_file_found=false
    
    # Method 1: Check via kubectl for controller-manager pods
    if command -v kubectl >/dev/null 2>&1; then
      if kubectl get pod -n kube-system -l component=kube-controller-manager -o yaml 2>/dev/null | grep -q "root-ca-file"; then
        ca_path=$(kubectl get pod -n kube-system -l component=kube-controller-manager -o yaml 2>/dev/null | grep "root-ca-file" | head -1 | awk '{print $2}')
        if [ -n "$ca_path" ]; then
          echo "PASS: Controller Manager root-ca-file is configured: $ca_path (kubectl)"
          root_ca_file_found=true
        fi
      fi
    fi
    
    # Method 2: Check controller-manager manifest file
    if [ -f /host/etc/kubernetes/manifests/kube-controller-manager.yaml ]; then
      if grep -q "root-ca-file" /host/etc/kubernetes/manifests/kube-controller-manager.yaml; then
        ca_path=$(grep "root-ca-file" /host/etc/kubernetes/manifests/kube-controller-manager.yaml | head -1 | awk '{print $2}')
        if [ -n "$ca_path" ]; then
          echo "PASS: Controller Manager root-ca-file is configured: $ca_path (manifest)"
          root_ca_file_found=true
        fi
      fi
    fi
    
    # Method 3: Check controller-manager process
    if pgrep kube-controller-manager >/dev/null 2>&1; then
      if ps aux | grep kube-controller-manager | grep -q "root-ca-file"; then
        echo "PASS: Controller Manager root-ca-file is configured (process)"
        root_ca_file_found=true
      fi
    fi
    
    if [ "$root_ca_file_found" = true ]; then
      exit 0
    else
      echo "FAIL: Controller Manager root-ca-file is not configured"
      exit 1
    fi