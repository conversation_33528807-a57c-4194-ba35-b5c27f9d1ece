apiVersion: compliance-operator.alauda.io/v1alpha1
kind: Rule
metadata:
  name: stig-k8s-api-server-tls-cert-file
  namespace: compliance-system
spec:
  id: "V-242422"
  title: "Kubernetes API Server must have a certificate for communication."
  description: |
    Kubernetes control plane and external communication is managed by API Server. The main implementation of the API Server is to manage hardware resources for pods and container using horizontal or vertical scaling. Anyone who can access the API Server can effectively control the Kubernetes architecture. Using authenticity protection, the communication can be protected against man-in-the-middle attacks/session hijacking and the insertion of false information into sessions.

    The communication session is protected by utilizing transport encryption protocols, such as TLS. TLS provides the Kubernetes API Server with a means to be able to authenticate sessions and encrypt traffic. 

    To enable encrypted communication for API Server, the parameter etcd-cafile must be set. This parameter gives the location of the SSL Certificate Authority file used to secure API Server communication.
  checkText: |
    Change to the /etc/kubernetes/manifests/ directory on the Kubernetes Control Plane. Run the command:
    grep -i tls-cert-file *
    grep -i tls-private-key-file *

    If the setting tls-cert-file and private-key-file is not set in the Kubernetes API server manifest file or contains no value, this is a finding.
  fixText: |
    Edit the Kubernetes API Server manifest file in the /etc/kubernetes/manifests directory on the Kubernetes Control Plane. Set the value of tls-cert-file and tls-private-key-file to path containing Approved Organizational Certificate.
  checkType: "platform"
  severity: "medium"
  checkScript: |
    #!/bin/bash
    # Check if API Server TLS certificate and private key files are configured
    # STIG requirement: --tls-cert-file and --tls-private-key-file must be set
    
    echo "Checking API Server TLS certificate configuration..."
    
    tls_cert_file_found=false
    tls_private_key_file_found=false
    
    # Method 1: Check via kubectl for API server pods
    if command -v kubectl >/dev/null 2>&1; then
      if kubectl get pod -n kube-system -l component=kube-apiserver -o yaml 2>/dev/null | grep -q "tls-cert-file"; then
        cert_path=$(kubectl get pod -n kube-system -l component=kube-apiserver -o yaml 2>/dev/null | grep "tls-cert-file" | head -1 | awk '{print $2}')
        if [ -n "$cert_path" ]; then
          echo "PASS: API Server tls-cert-file is configured: $cert_path (kubectl)"
          tls_cert_file_found=true
        fi
      fi
      
      if kubectl get pod -n kube-system -l component=kube-apiserver -o yaml 2>/dev/null | grep -q "tls-private-key-file"; then
        key_path=$(kubectl get pod -n kube-system -l component=kube-apiserver -o yaml 2>/dev/null | grep "tls-private-key-file" | head -1 | awk '{print $2}')
        if [ -n "$key_path" ]; then
          echo "PASS: API Server tls-private-key-file is configured: $key_path (kubectl)"
          tls_private_key_file_found=true
        fi
      fi
    fi
    
    # Method 2: Check API server manifest file
    if [ -f /host/etc/kubernetes/manifests/kube-apiserver.yaml ]; then
      if grep -q "tls-cert-file" /host/etc/kubernetes/manifests/kube-apiserver.yaml; then
        cert_path=$(grep "tls-cert-file" /host/etc/kubernetes/manifests/kube-apiserver.yaml | head -1 | awk '{print $2}')
        if [ -n "$cert_path" ]; then
          echo "PASS: API Server tls-cert-file is configured: $cert_path (manifest)"
          tls_cert_file_found=true
        fi
      fi
      
      if grep -q "tls-private-key-file" /host/etc/kubernetes/manifests/kube-apiserver.yaml; then
        key_path=$(grep "tls-private-key-file" /host/etc/kubernetes/manifests/kube-apiserver.yaml | head -1 | awk '{print $2}')
        if [ -n "$key_path" ]; then
          echo "PASS: API Server tls-private-key-file is configured: $key_path (manifest)"
          tls_private_key_file_found=true
        fi
      fi
    fi
    
    # Method 3: Check API server process
    if pgrep kube-apiserver >/dev/null 2>&1; then
      if ps aux | grep kube-apiserver | grep -q "tls-cert-file"; then
        echo "PASS: API Server tls-cert-file is configured (process)"
        tls_cert_file_found=true
      fi
      
      if ps aux | grep kube-apiserver | grep -q "tls-private-key-file"; then
        echo "PASS: API Server tls-private-key-file is configured (process)"
        tls_private_key_file_found=true
      fi
    fi
    
    if [ "$tls_cert_file_found" = true ] && [ "$tls_private_key_file_found" = true ]; then
      exit 0
    else
      echo "FAIL: API Server TLS certificate or private key file is not configured"
      [ "$tls_cert_file_found" = false ] && echo "  - tls-cert-file not found"
      [ "$tls_private_key_file_found" = false ] && echo "  - tls-private-key-file not found"
      exit 1
    fi