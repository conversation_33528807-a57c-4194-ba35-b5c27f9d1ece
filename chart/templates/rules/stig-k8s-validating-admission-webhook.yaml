apiVersion: compliance-operator.alauda.io/v1alpha1
kind: Rule
metadata:
  name: stig-k8s-validating-admission-webhook
  namespace: compliance-system
spec:
  id: "V-242436"
  title: "The Kubernetes API server must have the ValidatingAdmissionWebhook enabled."
  description: |
    Enabling the admissions webhook allows for Kubernetes to apply policies against objects that are to be created, read, updated, or deleted. The validating admission webhook is called during the validation phase of the admission control process. This webhook validates the incoming request and can reject requests that do not meet the defined policies.
    
    The ValidatingAdmissionWebhook is a critical security control that allows external systems to validate and potentially reject API requests before they are persisted in etcd. This provides an additional layer of security beyond the built-in admission controllers.
  checkText: |
    Change to the /etc/kubernetes/manifests directory on the Kubernetes Control Plane. Run the command:
    grep -i enable-admission-plugins *
    
    Review the \"--enable-admission-plugins\" setting in the Kubernetes API Server manifest file.
    
    If the \"--enable-admission-plugins\" setting does not contain \"ValidatingAdmissionWebhook\", this is a finding.
    
    Also verify that ValidatingAdmissionWebhook is not listed in the \"--disable-admission-plugins\" setting.
  fixText: |
    Edit the Kubernetes API Server manifest file in the /etc/kubernetes/manifests directory on the Kubernetes Control Plane.
    
    Add \"ValidatingAdmissionWebhook\" to the \"--enable-admission-plugins\" setting.
    
    If ValidatingAdmissionWebhook is listed in \"--disable-admission-plugins\", remove it from that list.
    
    Example: --enable-admission-plugins=NodeRestriction,ValidatingAdmissionWebhook
  severity: "high"
  checkType: "platform"
  checkScript: |
    #!/bin/bash
    # Check if ValidatingAdmissionWebhook is enabled
    
    echo "Checking API server ValidatingAdmissionWebhook configuration..."
    
    WEBHOOK_DISABLED=false
    WEBHOOK_ENABLED=false
    
    # First check if it's explicitly disabled
    if command -v kubectl >/dev/null 2>&1; then
      if kubectl get pod -n kube-system -l component=kube-apiserver -o yaml 2>/dev/null | grep -q "\--disable-admission-plugins.*ValidatingAdmissionWebhook"; then
        echo "FAIL: ValidatingAdmissionWebhook is explicitly disabled"
        WEBHOOK_DISABLED=true
      fi
    fi
    
    # Check API server manifest for explicit disable
    if [ -f /host/etc/kubernetes/manifests/kube-apiserver.yaml ]; then
      if grep -q "\--disable-admission-plugins.*ValidatingAdmissionWebhook" /host/etc/kubernetes/manifests/kube-apiserver.yaml; then
        echo "FAIL: ValidatingAdmissionWebhook is explicitly disabled (manifest)"
        WEBHOOK_DISABLED=true
      fi
    fi
    
    # Check running process for explicit disable
    if [ -d /host/proc ]; then
      for pid in $(ls /host/proc | grep '^[0-9]*$'); do
        if [ -f "/host/proc/$pid/cmdline" ] && grep -q kube-apiserver "/host/proc/$pid/cmdline" 2>/dev/null; then
          if tr '\0' ' ' < "/host/proc/$pid/cmdline" | grep -q "\--disable-admission-plugins.*ValidatingAdmissionWebhook"; then
            echo "FAIL: ValidatingAdmissionWebhook is explicitly disabled (process)"
            WEBHOOK_DISABLED=true
          fi
        fi
      done
    fi
    
    # If webhook is explicitly disabled, this is a finding
    if [ "$WEBHOOK_DISABLED" = "true" ]; then
      exit 1
    fi
    
    # Check if ValidatingAdmissionWebhook is explicitly enabled
    if command -v kubectl >/dev/null 2>&1; then
      if kubectl get pod -n kube-system -l component=kube-apiserver -o yaml 2>/dev/null | grep -q "\--enable-admission-plugins.*ValidatingAdmissionWebhook"; then
        echo "PASS: ValidatingAdmissionWebhook explicitly enabled"
        WEBHOOK_ENABLED=true
      fi
    fi
    
    # Check API server manifest for explicit enable
    if [ -f /host/etc/kubernetes/manifests/kube-apiserver.yaml ]; then
      if grep -q "\--enable-admission-plugins.*ValidatingAdmissionWebhook" /host/etc/kubernetes/manifests/kube-apiserver.yaml; then
        echo "PASS: ValidatingAdmissionWebhook explicitly enabled (manifest)"
        WEBHOOK_ENABLED=true
      fi
    fi
    
    # Check running process for explicit enable
    if [ -d /host/proc ]; then
      for pid in $(ls /host/proc | grep '^[0-9]*$'); do
        if [ -f "/host/proc/$pid/cmdline" ] && grep -q kube-apiserver "/host/proc/$pid/cmdline" 2>/dev/null; then
          if tr '\0' ' ' < "/host/proc/$pid/cmdline" | grep -q "\--enable-admission-plugins.*ValidatingAdmissionWebhook"; then
            echo "PASS: ValidatingAdmissionWebhook explicitly enabled (process)"
            WEBHOOK_ENABLED=true
          fi
        fi
      done
    fi
    
    # If webhook is explicitly enabled, this is a pass
    if [ "$WEBHOOK_ENABLED" = "true" ]; then
      exit 0
    fi
    
    # Check if ValidatingWebhookConfigurations exist (indicates webhook is functional)
    if command -v kubectl >/dev/null 2>&1; then
      WEBHOOK_COUNT=$(kubectl get validatingwebhookconfigurations 2>/dev/null | grep -v NAME | wc -l)
      if [ "$WEBHOOK_COUNT" -gt 0 ]; then
        echo "PASS: ValidatingAdmissionWebhook enabled (found $WEBHOOK_COUNT webhook configurations)"
        exit 0
      fi
    fi
    
    # Check Kubernetes version (ValidatingAdmissionWebhook is enabled by default in 1.16+)
    if command -v kubectl >/dev/null 2>&1; then
      K8S_VERSION=$(kubectl version --short 2>/dev/null | grep "Server Version" | grep -o "v[0-9]*\.[0-9]*" | sed 's/v//')
      if [ -n "$K8S_VERSION" ]; then
        MAJOR=$(echo "$K8S_VERSION" | cut -d'.' -f1)
        MINOR=$(echo "$K8S_VERSION" | cut -d'.' -f2)
        if [ "$MAJOR" -gt 1 ] || ([ "$MAJOR" -eq 1 ] && [ "$MINOR" -ge 16 ]); then
          echo "PASS: ValidatingAdmissionWebhook enabled by default in Kubernetes $K8S_VERSION"
          exit 0
        fi
      fi
    fi
    
    echo "FAIL: ValidatingAdmissionWebhook status unclear - no explicit configuration found"
    exit 1
