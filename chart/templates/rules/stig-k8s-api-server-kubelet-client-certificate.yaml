apiVersion: compliance-operator.alauda.io/v1alpha1
kind: Rule
metadata:
  name: stig-k8s-api-server-kubelet-client-certificate
  namespace: compliance-system
spec:
  id: "V-245544"
  title: "Kubernetes endpoints must use approved organizational certificate and key pair to protect information in transit."
  description: |
    Kubernetes control plane and external communication is managed by API Server. The main implementation of the API Server is to manage hardware resources for pods and container using horizontal or vertical scaling. Anyone who can gain access to the API Server can effectively control your Kubernetes architecture. Using authenticity protection, the communication can be protected against man-in-the-middle attacks/session hijacking and the insertion of false information into sessions.

    The communication session is protected by utilizing transport encryption protocols, such as TLS. TLS provides the Kubernetes API Server with a means to be able to authenticate sessions and encrypt traffic.

    By default, the API Server does not authenticate to the kubelet HTTPs endpoint. To enable secure communication for API Server, the parameter -kubelet-client-certificate and kubelet-client-key must be set. This parameter gives the location of the certificate and key pair used to secure API Server communication.
  checkText: |
    Change to the /etc/kubernetes/manifests/ directory on the Kubernetes Control Plane. Run the command:
    grep -i kubelet-client-certificate *
    grep -I kubelet-client-key * 

    If the setting "--kubelet-client-certificate" is not configured in the Kubernetes API server manifest file or contains no value, this is a finding.

    If the setting "--kubelet-client-key" is not configured in the Kubernetes API server manifest file or contains no value, this is a finding.
  fixText: |
    Edit the Kubernetes API Server manifest file in the /etc/kubernetes/manifests directory on the Kubernetes Control Plane. Set the value of "--kubelet-client-certificate" and "--kubelet-client-key" to an Approved Organizational Certificate and key pair.
  checkType: "platform"
  severity: "high"
  checkScript: |
    #!/bin/bash
    # Check if API Server kubelet client certificate and key are configured
    # STIG requirement: --kubelet-client-certificate and --kubelet-client-key must be set
    
    echo "Checking API Server kubelet client certificate configuration..."
    
    kubelet_client_cert_found=false
    kubelet_client_key_found=false
    
    # Method 1: Check via kubectl for API server pods
    if command -v kubectl >/dev/null 2>&1; then
      if kubectl get pod -n kube-system -l component=kube-apiserver -o yaml 2>/dev/null | grep -q "kubelet-client-certificate"; then
        cert_path=$(kubectl get pod -n kube-system -l component=kube-apiserver -o yaml 2>/dev/null | grep "kubelet-client-certificate" | head -1 | awk '{print $2}')
        if [ -n "$cert_path" ]; then
          echo "PASS: API Server kubelet-client-certificate is configured: $cert_path (kubectl)"
          kubelet_client_cert_found=true
        fi
      fi
      
      if kubectl get pod -n kube-system -l component=kube-apiserver -o yaml 2>/dev/null | grep -q "kubelet-client-key"; then
        key_path=$(kubectl get pod -n kube-system -l component=kube-apiserver -o yaml 2>/dev/null | grep "kubelet-client-key" | head -1 | awk '{print $2}')
        if [ -n "$key_path" ]; then
          echo "PASS: API Server kubelet-client-key is configured: $key_path (kubectl)"
          kubelet_client_key_found=true
        fi
      fi
    fi
    
    # Method 2: Check API server manifest file
    if [ -f /host/etc/kubernetes/manifests/kube-apiserver.yaml ]; then
      if grep -q "kubelet-client-certificate" /host/etc/kubernetes/manifests/kube-apiserver.yaml; then
        cert_path=$(grep "kubelet-client-certificate" /host/etc/kubernetes/manifests/kube-apiserver.yaml | head -1 | awk '{print $2}')
        if [ -n "$cert_path" ]; then
          echo "PASS: API Server kubelet-client-certificate is configured: $cert_path (manifest)"
          kubelet_client_cert_found=true
        fi
      fi
      
      if grep -q "kubelet-client-key" /host/etc/kubernetes/manifests/kube-apiserver.yaml; then
        key_path=$(grep "kubelet-client-key" /host/etc/kubernetes/manifests/kube-apiserver.yaml | head -1 | awk '{print $2}')
        if [ -n "$key_path" ]; then
          echo "PASS: API Server kubelet-client-key is configured: $key_path (manifest)"
          kubelet_client_key_found=true
        fi
      fi
    fi
    
    # Method 3: Check API server process
    if pgrep kube-apiserver >/dev/null 2>&1; then
      if ps aux | grep kube-apiserver | grep -q "kubelet-client-certificate"; then
        echo "PASS: API Server kubelet-client-certificate is configured (process)"
        kubelet_client_cert_found=true
      fi
      
      if ps aux | grep kube-apiserver | grep -q "kubelet-client-key"; then
        echo "PASS: API Server kubelet-client-key is configured (process)"
        kubelet_client_key_found=true
      fi
    fi
    
    if [ "$kubelet_client_cert_found" = true ] && [ "$kubelet_client_key_found" = true ]; then
      exit 0
    else
      echo "FAIL: API Server kubelet client certificate or key is not configured"
      [ "$kubelet_client_cert_found" = false ] && echo "  - kubelet-client-certificate not found"
      [ "$kubelet_client_key_found" = false ] && echo "  - kubelet-client-key not found"
      exit 1
    fi