apiVersion: compliance-operator.alauda.io/v1alpha1
kind: Rule
metadata:
  name: stig-k8s-controller-manager-conf-file-permissions
  namespace: compliance-system
spec:
  id: "V-242415"
  title: "The Kubernetes controller-manager.conf must have file permissions set to 644 or more restrictive."
  description: |
    The controller-manager.conf is the kubeconfig file for the Controller Manager. The Controller Manager is responsible for running controller processes. If an attacker can gain access to this file, changes can be made to open vulnerabilities and bypass user authorizations inherent within Kubernetes with RBAC implemented.
  checkText: |
    On the Control Plane, run the command:
    stat -c %a /etc/kubernetes/controller-manager.conf

    If the file has permissions more permissive than "644", this is a finding.
  fixText: |
    On the Control Plane, run the command:
    chmod 644 /etc/kubernetes/controller-manager.conf
  severity: "medium"
  checkType: "node"
  checkScript: |
    #!/bin/bash
    # Check controller-manager.conf file permissions
    # STIG requirement: controller-manager.conf should have 644 permissions or more restrictive
    
    echo "Checking controller-manager.conf file permissions..."
    
    CONTROLLER_MANAGER_CONF_PATHS=(
      "/host/etc/kubernetes/controller-manager.conf"
      "/host/etc/kubernetes/controller-manager.kubeconfig"
    )
    
    VIOLATIONS_FOUND=false
    FILES_CHECKED=0
    
    for cm_conf_path in "${CONTROLLER_MANAGER_CONF_PATHS[@]}"; do
      if [ -f "$cm_conf_path" ]; then
        FILES_CHECKED=$((FILES_CHECKED + 1))
        filename=$(basename "$cm_conf_path")
        perms=$(stat -c "%a" "$cm_conf_path" 2>/dev/null)
        
        echo "✓ Found controller-manager config file: $cm_conf_path"
        echo "  Permissions: $perms"
        
        # Check if permissions are more permissive than 644
        if [ "$perms" -gt 644 ]; then
          echo "  ✗ VIOLATION: Controller-manager config file $filename has overly permissive permissions: $perms (should be 644 or more restrictive)"
          VIOLATIONS_FOUND=true
        else
          echo "  ✓ OK: Controller-manager config file $filename has appropriate permissions: $perms"
        fi
      fi
    done
    
    # Try to find controller-manager config from running process
    if [ -d "/host/proc" ]; then
      for pid in $(ls /host/proc | grep '^[0-9]*$'); do
        if [ -f "/host/proc/$pid/cmdline" ] && grep -q kube-controller-manager "/host/proc/$pid/cmdline" 2>/dev/null; then
          cmdline=$(tr '\0' ' ' < "/host/proc/$pid/cmdline")
          if echo "$cmdline" | grep -q -- "--kubeconfig"; then
            config_file=$(echo "$cmdline" | sed 's/.*--kubeconfig[= ]\([^ ]*\).*/\1/')
            if [ -f "/host$config_file" ]; then
              FILES_CHECKED=$((FILES_CHECKED + 1))
              perms=$(stat -c "%a" "/host$config_file" 2>/dev/null)
              echo "✓ Found controller-manager config from process: /host$config_file"
              echo "  Permissions: $perms"
              
              if [ "$perms" -gt 644 ]; then
                echo "  ✗ VIOLATION: Controller-manager config file has overly permissive permissions: $perms (should be 644 or more restrictive)"
                VIOLATIONS_FOUND=true
              else
                echo "  ✓ OK: Controller-manager config file has appropriate permissions: $perms"
              fi
            fi
          fi
          break
        fi
      done
    fi
    
    if [ "$FILES_CHECKED" -eq 0 ]; then
      echo "WARNING: No controller-manager configuration files found"
      exit 0
    fi
    
    if [ "$VIOLATIONS_FOUND" = true ]; then
      echo "FAIL: Found controller-manager configuration files with overly permissive permissions"
      exit 1
    else
      echo "PASS: All controller-manager configuration files have appropriate permissions (644 or more restrictive)"
      exit 0
    fi