apiVersion: compliance-operator.alauda.io/v1alpha1
kind: Rule
metadata:
  name: stig-k8s-controller-manager-use-service-account-credentials
  namespace: compliance-system
spec:
  id: "V-242381"
  title: "The Kubernetes Controller Manager must create unique service accounts for each work payload."
  description: |
    The Kubernetes Controller Manager is a background process that embeds core control loops regulating cluster system state through the API Server. Every process executed in a pod has an associated service account. By default, service accounts use the same credentials for authentication. Implementing the default settings poses a High risk to the Kubernetes Controller Manager. Setting the "--use-service-account-credential" value lowers the attack surface by generating unique service accounts settings for each controller instance.
  checkText: |
    Change to the /etc/kubernetes/manifests directory on the Kubernetes Control Plane. Run the command:

    grep -i use-service-account-credentials * 

    If the setting "--use-service-account-credentials" is not configured in the Kubernetes Controller Manager manifest file or it is set to "false", this is a finding.
  fixText: |
    Edit the Kubernetes Controller Manager manifest file in the /etc/kubernetes/manifests directory on the Kubernetes Control Plane.

    Set the value of "--use-service-account-credentials" to "true".
  checkType: "platform"
  severity: "high"
  checkScript: |
    #!/bin/bash
    # Check if Controller Manager use-service-account-credentials is enabled
    # STIG requirement: --use-service-account-credentials should be set to true
    
    echo "Checking Controller Manager use-service-account-credentials configuration..."
    
    use_service_account_found=false
    
    # Method 1: Check via kubectl for controller-manager pods
    if command -v kubectl >/dev/null 2>&1; then
      if kubectl get pod -n kube-system -l component=kube-controller-manager -o yaml 2>/dev/null | grep -q "use-service-account-credentials.*true"; then
        echo "PASS: Controller Manager use-service-account-credentials is enabled (kubectl)"
        use_service_account_found=true
      fi
    fi
    
    # Method 2: Check controller-manager manifest file
    if [ -f /host/etc/kubernetes/manifests/kube-controller-manager.yaml ]; then
      if grep -q "use-service-account-credentials.*true" /host/etc/kubernetes/manifests/kube-controller-manager.yaml; then
        echo "PASS: Controller Manager use-service-account-credentials is enabled (manifest)"
        use_service_account_found=true
      fi
    fi
    
    # Method 3: Check controller-manager process
    if pgrep kube-controller-manager >/dev/null 2>&1; then
      if ps aux | grep kube-controller-manager | grep -q "use-service-account-credentials.*true"; then
        echo "PASS: Controller Manager use-service-account-credentials is enabled (process)"
        use_service_account_found=true
      fi
    fi
    
    if [ "$use_service_account_found" = true ]; then
      exit 0
    else
      echo "FAIL: Controller Manager use-service-account-credentials is not enabled or not configured"
      exit 1
    fi