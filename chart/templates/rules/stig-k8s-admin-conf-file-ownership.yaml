apiVersion: compliance-operator.alauda.io/v1alpha1
kind: Rule
metadata:
  name: stig-k8s-admin-conf-file-ownership
  namespace: compliance-system
spec:
  id: "V-242410"
  title: "The Kubernetes admin.conf must be owned by root."
  description: |
    The admin.conf is the administrator kubeconfig file defining administrator user, base64-encoded certificate, and cluster server location. This configuration file contains the credentials necessary to access the cluster as an administrator. If an attacker can gain access to this file, changes can be made to open vulnerabilities and bypass user authorizations inherent within Kubernetes with RBAC implemented.
  checkText: |
    On the Control Plane, run the command:
    stat -c %U:%G /etc/kubernetes/admin.conf

    If the file is not owned by root:root, this is a finding.
  fixText: |
    On the Control Plane, run the command:
    chown root:root /etc/kubernetes/admin.conf
  severity: "medium"
  checkType: "node"
  checkScript: |
    #!/bin/bash
    # Check admin.conf file ownership
    # STIG requirement: admin.conf should be owned by root:root
    
    echo "Checking admin.conf file ownership..."
    
    ADMIN_CONF_PATHS=(
      "/host/etc/kubernetes/admin.conf"
      "/host/root/.kube/config"
    )
    
    VIOLATIONS_FOUND=false
    FILES_CHECKED=0
    
    for admin_conf_path in "${ADMIN_CONF_PATHS[@]}"; do
      if [ -f "$admin_conf_path" ]; then
        FILES_CHECKED=$((FILES_CHECKED + 1))
        filename=$(basename "$admin_conf_path")
        owner=$(stat -c "%U:%G" "$admin_conf_path" 2>/dev/null)
        
        echo "✓ Found admin config file: $admin_conf_path"
        echo "  Owner: $owner"
        
        if [ "$owner" != "root:root" ]; then
          echo "  ✗ VIOLATION: Admin config file $filename is not owned by root:root (current: $owner)"
          VIOLATIONS_FOUND=true
        else
          echo "  ✓ OK: Admin config file $filename is properly owned by root:root"
        fi
      fi
    done
    
    # Check for additional kubeconfig files in common locations
    ADDITIONAL_PATHS=(
      "/host/home/<USER>/.kube/config"
      "/host/root/.kube/admin.conf"
    )
    
    for path_pattern in "${ADDITIONAL_PATHS[@]}"; do
      for config_file in $path_pattern; do
        if [ -f "$config_file" ]; then
          FILES_CHECKED=$((FILES_CHECKED + 1))
          filename=$(basename "$config_file")
          owner=$(stat -c "%U:%G" "$config_file" 2>/dev/null)
          
          echo "✓ Found additional config file: $config_file"
          echo "  Owner: $owner"
          
          # For admin configs, they should be owned by root
          if echo "$config_file" | grep -q admin; then
            if [ "$owner" != "root:root" ]; then
              echo "  ✗ VIOLATION: Admin config file $filename is not owned by root:root (current: $owner)"
              VIOLATIONS_FOUND=true
            else
              echo "  ✓ OK: Admin config file $filename is properly owned by root:root"
            fi
          fi
        fi
      done
    done
    
    if [ "$FILES_CHECKED" -eq 0 ]; then
      echo "WARNING: No admin configuration files found"
      # Return exit code 2 for MANUAL status (needs human review)
      exit 2
    fi
    
    if [ "$VIOLATIONS_FOUND" = true ]; then
      echo "FAIL: Found admin configuration files not owned by root:root"
      exit 1
    else
      echo "PASS: All admin configuration files are properly owned by root:root"
      exit 0
    fi