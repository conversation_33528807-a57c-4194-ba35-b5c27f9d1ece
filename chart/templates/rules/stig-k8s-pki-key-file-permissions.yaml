apiVersion: compliance-operator.alauda.io/v1alpha1
kind: Rule
metadata:
  name: stig-k8s-pki-key-file-permissions
  namespace: compliance-system
spec:
  id: "V-242418"
  title: "The Kubernetes PKI keys must have file permissions set to 600 or more restrictive."
  description: |
    The Kubernetes PKI directory contains all the public/private key pairs and certificates used by Kubernetes components. Private key files contain sensitive cryptographic material and must be protected from unauthorized access. If an attacker can gain access to these files, they could potentially compromise the security of the entire cluster.
  checkText: |
    On the Control Plane, run the command:
    stat -c %a /etc/kubernetes/pki/*.key

    If any key file has permissions more permissive than "600", this is a finding.
  fixText: |
    On the Control Plane, run the command:
    chmod 600 /etc/kubernetes/pki/*.key
  severity: "high"
  checkType: "node"
  checkScript: |
    #!/bin/bash
    # Check PKI private key file permissions
    # STIG requirement: Private key files should have 600 permissions or more restrictive
    
    echo "Checking PKI private key file permissions..."
    
    PKI_PATHS=(
      "/host/etc/kubernetes/pki"
      "/host/var/lib/kubernetes/pki"
    )
    
    VIOLATIONS_FOUND=false
    KEY_FILES_CHECKED=0
    PKI_DIR_FOUND=false
    
    for pki_path in "${PKI_PATHS[@]}"; do
      if [ -d "$pki_path" ]; then
        PKI_DIR_FOUND=true
        echo "✓ Found PKI directory: $pki_path"
        
        # Check private key files (.key)
        for key_file in $(find "$pki_path" -name "*.key" 2>/dev/null); do
          KEY_FILES_CHECKED=$((KEY_FILES_CHECKED + 1))
          filename=$(basename "$key_file")
          perms=$(stat -c "%a" "$key_file" 2>/dev/null)
          
          echo "  ✓ Checking private key file: $filename"
          echo "    Permissions: $perms"
          
          # Check if permissions are more permissive than 600
          if [ "$perms" -gt 600 ]; then
            echo "    ✗ VIOLATION: Private key file $filename has overly permissive permissions: $perms (should be 600 or more restrictive)"
            VIOLATIONS_FOUND=true
          else
            echo "    ✓ OK: Private key file $filename has appropriate permissions: $perms"
          fi
        done
      fi
    done
    
    # Check for additional private key files in common locations
    ADDITIONAL_KEY_PATHS=(
      "/host/etc/ssl/private/kubernetes"
      "/host/etc/kubernetes/ssl"
    )
    
    for additional_path in "${ADDITIONAL_KEY_PATHS[@]}"; do
      if [ -d "$additional_path" ]; then
        PKI_DIR_FOUND=true
        echo "✓ Found additional private key directory: $additional_path"
        
        for key_file in $(find "$additional_path" -name "*.key" 2>/dev/null); do
          KEY_FILES_CHECKED=$((KEY_FILES_CHECKED + 1))
          filename=$(basename "$key_file")
          perms=$(stat -c "%a" "$key_file" 2>/dev/null)
          
          echo "  ✓ Checking additional private key file: $filename"
          echo "    Permissions: $perms"
          
          if [ "$perms" -gt 600 ]; then
            echo "    ✗ VIOLATION: Additional private key file $filename has overly permissive permissions: $perms (should be 600 or more restrictive)"
            VIOLATIONS_FOUND=true
          fi
        done
      fi
    done
    
    # Check for service account key files
    SA_KEY_PATHS=(
      "/host/etc/kubernetes/pki/sa.key"
      "/host/var/lib/kubernetes/service-account-key.pem"
    )
    
    for sa_key_path in "${SA_KEY_PATHS[@]}"; do
      if [ -f "$sa_key_path" ]; then
        PKI_DIR_FOUND=true
        KEY_FILES_CHECKED=$((KEY_FILES_CHECKED + 1))
        filename=$(basename "$sa_key_path")
        perms=$(stat -c "%a" "$sa_key_path" 2>/dev/null)
        
        echo "✓ Found service account key file: $filename"
        echo "  Permissions: $perms"
        
        if [ "$perms" -gt 600 ]; then
          echo "  ✗ VIOLATION: Service account key file $filename has overly permissive permissions: $perms (should be 600 or more restrictive)"
          VIOLATIONS_FOUND=true
        else
          echo "  ✓ OK: Service account key file $filename has appropriate permissions: $perms"
        fi
      fi
    done
    
    echo ""
    echo "=== EVALUATION ==="
    echo "PKI directory found: $PKI_DIR_FOUND"
    echo "Key files checked: $KEY_FILES_CHECKED"
    echo "Violations found: $VIOLATIONS_FOUND"
    
    if [ "$PKI_DIR_FOUND" = false ]; then
      echo "WARNING: No PKI directories found to check"
      # Return exit code 2 for MANUAL status (needs human review)
      exit 2
    fi
    
    if [ "$KEY_FILES_CHECKED" -eq 0 ]; then
      echo "WARNING: No private key files found to check"
      # Return exit code 2 for MANUAL status (needs human review)
      exit 2
    fi
    
    if [ "$VIOLATIONS_FOUND" = true ]; then
      echo "FAIL: Found private key files with overly permissive permissions"
      exit 1
    else
      echo "PASS: All private key files have appropriate permissions (600 or more restrictive)"
      exit 0
    fi