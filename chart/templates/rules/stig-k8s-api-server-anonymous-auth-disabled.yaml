# STIG Kubernetes v2r2 Complete Rules Implementation
# Based on DISA STIG Kubernetes v2r2 (94 rules)
# Platform and Node compliance checks

# =============================================================================
# PLATFORM CONTROLS - API SERVER
# =============================================================================

apiVersion: compliance-operator.alauda.io/v1alpha1
kind: Rule
metadata:
  name: stig-k8s-api-server-anonymous-auth-disabled
  namespace: compliance-system
spec:
  id: "V-242390"
  title: "The Kubernetes API server must have anonymous authentication disabled."
  description: |
    The Kubernetes API Server controls Kubernetes via an API interface. A user who has access to the API essentially has root access to the entire Kubernetes cluster. To control access, users must be authenticated and authorized. By allowing anonymous connections, the controls put in place to secure the API can be bypassed.

    Setting "--anonymous-auth" to "false" also disables unauthenticated requests from kubelets.

    While there are instances where anonymous connections may be needed (e.g., health checks) and Role-Based Access Controls (RBACs) are in place to limit the anonymous access, this access should be disabled, and only enabled when necessary.
  checkText: |
    Change to the /etc/kubernetes/manifests directory on the Kubernetes Control Plane. Run the command:
    grep -i anonymous-auth *

    If the setting "--anonymous-auth" is set to "true" in the Kubernetes API Server manifest file, this is a finding.
  fixText: |
    Edit the Kubernetes API Server manifest file in the /etc/kubernetes/manifests directory on the Kubernetes Control Plane.

    Set the value of "--anonymous-auth" to "false".
  severity: "high"
  checkType: "platform"
  checkScript: |
    #!/bin/bash
    # Check if anonymous authentication is disabled
    # STIG requirement: --anonymous-auth should be set to false (not true)
    
    echo "Checking API server anonymous authentication configuration..."
    
    ANONYMOUS_AUTH_ENABLED=false
    
    # Method 1: Check via kubectl for explicit anonymous-auth=true (FAIL case)
    if command -v kubectl >/dev/null 2>&1; then
      if kubectl get pod -n kube-system -l component=kube-apiserver -o yaml 2>/dev/null | grep -q "anonymous-auth=true"; then
        echo "FAIL: Anonymous authentication is explicitly enabled (kubectl)"
        ANONYMOUS_AUTH_ENABLED=true
      fi
    fi
    
    # Method 2: Check API server manifest for explicit anonymous-auth=true (FAIL case)
    if [ -f /host/etc/kubernetes/manifests/kube-apiserver.yaml ]; then
      if grep -q "anonymous-auth=true" /host/etc/kubernetes/manifests/kube-apiserver.yaml; then
        echo "FAIL: Anonymous authentication is explicitly enabled (manifest)"
        ANONYMOUS_AUTH_ENABLED=true
      fi
    fi
    
    # Method 3: Check running process for explicit anonymous-auth=true (FAIL case)
    if [ -d /host/proc ]; then
      for pid in $(ls /host/proc | grep '^[0-9]*$'); do
        if [ -f "/host/proc/$pid/cmdline" ] && grep -q kube-apiserver "/host/proc/$pid/cmdline" 2>/dev/null; then
          if tr '\0' ' ' < "/host/proc/$pid/cmdline" | grep -q "anonymous-auth=true"; then
            echo "FAIL: Anonymous authentication is explicitly enabled (process)"
            ANONYMOUS_AUTH_ENABLED=true
          fi
        fi
      done
    fi
    
    # If anonymous auth is explicitly enabled, this is a finding
    if [ "$ANONYMOUS_AUTH_ENABLED" = "true" ]; then
      exit 1
    fi
    
    # Method 4: Check for explicit anonymous-auth=false (PASS case)
    if command -v kubectl >/dev/null 2>&1; then
      if kubectl get pod -n kube-system -l component=kube-apiserver -o yaml 2>/dev/null | grep -q "anonymous-auth=false"; then
        echo "PASS: Anonymous authentication is explicitly disabled"
        exit 0
      fi
    fi
    
    if [ -f /host/etc/kubernetes/manifests/kube-apiserver.yaml ]; then
      if grep -q "anonymous-auth=false" /host/etc/kubernetes/manifests/kube-apiserver.yaml; then
        echo "PASS: Anonymous authentication is explicitly disabled (manifest)"
        exit 0
      fi
    fi
    
    if [ -d /host/proc ]; then
      for pid in $(ls /host/proc | grep '^[0-9]*$'); do
        if [ -f "/host/proc/$pid/cmdline" ] && grep -q kube-apiserver "/host/proc/$pid/cmdline" 2>/dev/null; then
          if tr '\0' ' ' < "/host/proc/$pid/cmdline" | grep -q "anonymous-auth=false"; then
            echo "PASS: Anonymous authentication is explicitly disabled (process)"
            exit 0
          fi
        fi
      done
    fi
    
    # If no explicit setting found, assume secure default (Kubernetes 1.6+ defaults to false)
    echo "PASS: Anonymous authentication disabled by default (no explicit true setting found)"
    exit 0
