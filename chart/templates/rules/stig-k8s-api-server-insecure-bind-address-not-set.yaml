apiVersion: compliance-operator.alauda.io/v1alpha1
kind: Rule
metadata:
  name: stig-k8s-api-server-insecure-bind-address-not-set
  namespace: compliance-system
spec:
  id: "V-242388"
  title: "The Kubernetes API server must have the insecure bind address not set."
  description: |
    By default, the API server will listen on two ports and addresses. One address is the secure address and the other address is called the "insecure bind" address and is set by default to localhost. Any requests to this address bypass authentication and authorization checks. If this insecure bind address is set to localhost, anyone who gains access to the host on which the Control Plane is running can bypass all authorization and authentication mechanisms put in place and have full control over the entire cluster.

    Close or set the insecure bind address by setting the API server's "--insecure-bind-address" flag to an IP or leave it unset and ensure that the "--insecure-bind-port" is not set.
  checkText: |
    Change to the /etc/kubernetes/manifests directory on the Kubernetes Control Plane. Run the command:
    grep -i insecure-bind-address *

    If the setting "--insecure-bind-address" is found and set to "localhost" in the Kubernetes API manifest file, this is a finding.
  fixText: |
    Edit the Kubernetes API Server manifest file in the /etc/kubernetes/manifests directory on the Kubernetes Control Plane.

    Remove the value of "--insecure-bind-address" setting.
  severity: "high"
  checkType: "platform"
  checkScript: |
    #!/bin/bash
    # Check if insecure bind address is not set
    
    echo "Checking API server insecure bind address configuration..."
    
    INSECURE_BIND_ADDRESS_FOUND=false
    
    # Method 1: Check via kubectl
    if command -v kubectl >/dev/null 2>&1; then
      if kubectl get pod -n kube-system -l component=kube-apiserver -o yaml 2>/dev/null | grep -q "insecure-bind-address"; then
        echo "FAIL: Insecure bind address is configured"
        INSECURE_BIND_ADDRESS_FOUND=true
      fi
    fi
    
    # Method 2: Check API server manifest
    if [ -f /host/etc/kubernetes/manifests/kube-apiserver.yaml ]; then
      if grep -q "insecure-bind-address" /host/etc/kubernetes/manifests/kube-apiserver.yaml; then
        echo "FAIL: Insecure bind address is configured"
        INSECURE_BIND_ADDRESS_FOUND=true
      fi
    fi
    
    # Method 3: Check running process
    if [ -d /host/proc ]; then
      for pid in $(ls /host/proc | grep '^[0-9]*$'); do
        if [ -f "/host/proc/$pid/cmdline" ] && grep -q kube-apiserver "/host/proc/$pid/cmdline" 2>/dev/null; then
          if tr '\0' ' ' < "/host/proc/$pid/cmdline" | grep -q "insecure-bind-address"; then
            echo "FAIL: Insecure bind address is configured"
            INSECURE_BIND_ADDRESS_FOUND=true
          fi
        fi
      done
    fi
    
    # If insecure bind address is found, this is a finding
    if [ "$INSECURE_BIND_ADDRESS_FOUND" = "true" ]; then
      exit 1
    fi
    
    echo "PASS: Insecure bind address is not configured"
    exit 0
