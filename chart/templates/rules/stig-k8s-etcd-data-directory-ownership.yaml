apiVersion: compliance-operator.alauda.io/v1alpha1
kind: Rule
metadata:
  name: stig-k8s-etcd-data-directory-ownership
  namespace: compliance-system
spec:
  id: "V-242424"
  title: "The Kubernetes etcd must be owned by etcd."
  description: |
    The etcd database stores the state of the cluster. If an attacker can gain access to the etcd database, changes can be made to open vulnerabilities and bypass user authorizations inherent within Kubernetes with RBAC implemented. The etcd database should be owned by etcd to ensure proper access controls.
  checkText: |
    Determine the etcd data directory by running the following command:
    ps -ef | grep etcd

    Note the data directory (identified by --data-dir).

    Run the command:
    stat -c %U:%G <etcd_data_directory>

    If the directory is not owned by etcd:etcd, this is a finding.
  fixText: |
    Run the command:
    chown etcd:etcd <etcd_data_directory>

    To verify the change took place, run the command:
    stat -c %U:%G <etcd_data_directory>
  severity: "medium"
  checkType: "node"
  checkScript: |
    #!/bin/bash
    # Check etcd data directory ownership
    # STIG requirement: etcd data directory should be owned by etcd:etcd
    
    echo "Checking etcd data directory ownership..."
    
    ETCD_DATA_DIRS=()
    VIOLATIONS_FOUND=false
    DIRS_CHECKED=0
    
    # Method 1: Find data directory from running etcd process
    if [ -d "/host/proc" ]; then
      for pid in $(ls /host/proc | grep '^[0-9]*$'); do
        if [ -f "/host/proc/$pid/cmdline" ] && grep -q etcd "/host/proc/$pid/cmdline" 2>/dev/null; then
          cmdline=$(tr '\0' ' ' < "/host/proc/$pid/cmdline")
          if echo "$cmdline" | grep -q -- "--data-dir"; then
            data_dir=$(echo "$cmdline" | sed 's/.*--data-dir[= ]\([^ ]*\).*/\1/')
            ETCD_DATA_DIRS+=("/host$data_dir")
            echo "✓ Found etcd data directory from process: /host$data_dir"
          fi
        fi
      done
    fi
    
    # Method 2: Check common etcd data directory locations
    COMMON_ETCD_DIRS=(
      "/host/var/lib/etcd"
      "/host/var/etcd"
      "/host/opt/etcd/data"
      "/host/etc/etcd/data"
    )
    
    for common_dir in "${COMMON_ETCD_DIRS[@]}"; do
      if [ -d "$common_dir" ]; then
        ETCD_DATA_DIRS+=("$common_dir")
        echo "✓ Found common etcd data directory: $common_dir"
      fi
    done
    
    # Check ownership of found directories
    for etcd_dir in "${ETCD_DATA_DIRS[@]}"; do
      if [ -d "$etcd_dir" ]; then
        DIRS_CHECKED=$((DIRS_CHECKED + 1))
        dirname=$(basename "$etcd_dir")
        owner=$(stat -c "%U:%G" "$etcd_dir" 2>/dev/null)
        
        echo "  ✓ Checking etcd data directory: $etcd_dir"
        echo "    Owner: $owner"
        
        # Check if owned by etcd:etcd or root:root (acceptable alternatives)
        if [ "$owner" != "etcd:etcd" ] && [ "$owner" != "root:root" ]; then
          echo "    ✗ VIOLATION: etcd data directory is not owned by etcd:etcd or root:root (current: $owner)"
          VIOLATIONS_FOUND=true
        else
          echo "    ✓ OK: etcd data directory has appropriate ownership: $owner"
        fi
        
        # Check subdirectories and files ownership
        if [ -d "$etcd_dir" ]; then
          find "$etcd_dir" -type d | head -10 | while read -r subdir; do
            if [ "$subdir" != "$etcd_dir" ]; then
              subdir_owner=$(stat -c "%U:%G" "$subdir" 2>/dev/null)
              subdir_name=$(basename "$subdir")
              
              if [ "$subdir_owner" != "etcd:etcd" ] && [ "$subdir_owner" != "root:root" ]; then
                echo "    ✗ WARNING: etcd subdirectory $subdir_name has unexpected ownership: $subdir_owner"
              fi
            fi
          done
        fi
      fi
    done
    
    if [ "$DIRS_CHECKED" -eq 0 ]; then
      echo "WARNING: No etcd data directories found"
      exit 0
    fi
    
    if [ "$VIOLATIONS_FOUND" = true ]; then
      echo "FAIL: Found etcd data directories with inappropriate ownership"
      exit 1
    else
      echo "PASS: All etcd data directories have appropriate ownership"
      exit 0
    fi