apiVersion: compliance-operator.alauda.io/v1alpha1
kind: Rule
metadata:
  name: stig-k8s-manifest-file-permissions
  namespace: compliance-system
spec:
  id: "V-242408"
  title: "The Kubernetes manifest files must have least privileges."
  description: |
    The manifest files contain the runtime configuration of the API server, scheduler, controller, and etcd. If an attacker can gain access to these files, changes can be made to open vulnerabilities and bypass user authorizations inherent within Kubernetes with RBAC implemented.

    Satisfies: SRG-APP-000133-CTR-000310, SRG-APP-000133-CTR-000295, SRG-APP-000516-CTR-001335
  checkText: |
    On both Control Plane and Worker Nodes, change to the /etc/kubernetes/manifest directory. Run the command:
    ls -l *

    Each manifest file must have permissions "644" or more restrictive.

    If any manifest file is less restrictive than "644", this is a finding.
  fixText: |
    On both Control Plane and Worker Nodes, change to the /etc/kubernetes/manifest directory. Run the command:
    chmod 644 *

    To verify the change took place, run the command:
    ls -l *

    All the manifest files should now have privileges of "644".
  severity: "medium"
  checkType: "node"
  checkScript: |
    #!/bin/bash
    # Check manifest file permissions
    # STIG requirement: manifest files should have 644 permissions or more restrictive
    
    echo "Checking Kubernetes manifest file permissions..."
    
    MANIFEST_DIRS="/host/etc/kubernetes/manifests /host/etc/kubernetes/manifest"
    VIOLATIONS_FOUND=false
    
    for MANIFEST_DIR in $MANIFEST_DIRS; do
      if [ -d "$MANIFEST_DIR" ]; then
        echo "✓ Checking manifest directory: $MANIFEST_DIR"
        
        for file in "$MANIFEST_DIR"/*; do
          if [ -f "$file" ]; then
            filename=$(basename "$file")
            perms=$(stat -c "%a" "$file" 2>/dev/null)
            
            if [ -n "$perms" ]; then
              echo "  File: $filename - Permissions: $perms"
              
              # Check if permissions are more permissive than 644
              if [ "$perms" -gt 644 ]; then
                echo "  ✗ VIOLATION: File $filename has overly permissive permissions: $perms (should be 644 or more restrictive)"
                VIOLATIONS_FOUND=true
              else
                echo "  ✓ OK: File $filename has appropriate permissions: $perms"
              fi
            fi
          fi
        done
      else
        echo "ℹ Manifest directory not found: $MANIFEST_DIR"
      fi
    done
    
    if [ "$VIOLATIONS_FOUND" = true ]; then
      echo "FAIL: Found manifest files with overly permissive permissions"
      exit 1
    else
      echo "PASS: All manifest files have appropriate permissions (644 or more restrictive)"
      exit 0
    fi