apiVersion: compliance-operator.alauda.io/v1alpha1
kind: Rule
metadata:
  name: stig-k8s-pod-security-standards-enabled
  namespace: compliance-system
spec:
  id: "V-242406"
  title: "Kubernetes must enable PodSecurity admission controller."
  description: |
    PodSecurity is an admission controller that validates and enforces security policies for pods running within a Kubernetes cluster. It replaces the deprecated Pod Security Policies (PSPs) and provides a more streamlined approach to pod security.
    
    The PodSecurity admission controller enforces Pod Security Standards at the namespace level. These standards define three different policies to broadly cover the security spectrum:
    - Privileged: Unrestricted policy, providing the widest possible level of permissions
    - Baseline: Minimally restrictive policy which prevents known privilege escalations
    - Restricted: Heavily restricted policy, following current Pod hardening best practices
    
    Without proper pod security controls, containers may run with excessive privileges, potentially compromising the security of the entire cluster.
  checkText: |
    On the Control Plane, run the command:
    ps -ef | grep kube-apiserver
    
    Check the output for the \"--enable-admission-plugins\" setting. Verify that PodSecurity is included in the list of enabled admission plugins.
    
    If PodSecurity is not listed in the \"--enable-admission-plugins\" setting, check if it's listed in the \"--disable-admission-plugins\" setting.
    
    If PodSecurity is listed in the \"--disable-admission-plugins\" setting, this is a finding.
    
    If PodSecurity is not explicitly enabled or disabled, check the Kubernetes version:
    kubectl version
    
    For Kubernetes 1.25 and later, PodSecurity is enabled by default. For earlier versions, it must be explicitly enabled.
  fixText: |
    Edit the Kubernetes API Server manifest file (usually located at /etc/kubernetes/manifests/kube-apiserver.yaml):
    
    Add or modify the \"--enable-admission-plugins\" setting to include PodSecurity:
    --enable-admission-plugins=NodeRestriction,PodSecurity
    
    If PodSecurity is listed in \"--disable-admission-plugins\", remove it from that list.
    
    Restart the API Server to apply the changes.
  severity: "high"
  checkType: "platform"
  checkScript: |
    #!/bin/bash
    # Check if PodSecurity admission controller is enabled
    
    echo "Checking API server PodSecurity admission controller configuration..."
    
    PODSECURITY_DISABLED=false
    PODSECURITY_ENABLED=false
    
    # First check if it's explicitly disabled
    if command -v kubectl >/dev/null 2>&1; then
      if kubectl get pod -n kube-system -l component=kube-apiserver -o yaml 2>/dev/null | grep -q "\--disable-admission-plugins.*PodSecurity"; then
        echo "FAIL: PodSecurity admission controller is explicitly disabled"
        PODSECURITY_DISABLED=true
      fi
    fi
    
    # Check API server manifest for explicit disable
    if [ -f /host/etc/kubernetes/manifests/kube-apiserver.yaml ]; then
      if grep -q "\--disable-admission-plugins.*PodSecurity" /host/etc/kubernetes/manifests/kube-apiserver.yaml; then
        echo "FAIL: PodSecurity admission controller is explicitly disabled (manifest)"
        PODSECURITY_DISABLED=true
      fi
    fi
    
    # Check running process for explicit disable
    if [ -d /host/proc ]; then
      for pid in $(ls /host/proc | grep '^[0-9]*$'); do
        if [ -f "/host/proc/$pid/cmdline" ] && grep -q kube-apiserver "/host/proc/$pid/cmdline" 2>/dev/null; then
          if tr '\0' ' ' < "/host/proc/$pid/cmdline" | grep -q "\--disable-admission-plugins.*PodSecurity"; then
            echo "FAIL: PodSecurity admission controller is explicitly disabled (process)"
            PODSECURITY_DISABLED=true
          fi
        fi
      done
    fi
    
    # If PodSecurity is explicitly disabled, this is a finding
    if [ "$PODSECURITY_DISABLED" = "true" ]; then
      exit 1
    fi
    
    # Check if PodSecurity is explicitly enabled
    if command -v kubectl >/dev/null 2>&1; then
      if kubectl get pod -n kube-system -l component=kube-apiserver -o yaml 2>/dev/null | grep -q "\--enable-admission-plugins.*PodSecurity"; then
        echo "PASS: PodSecurity admission controller explicitly enabled"
        PODSECURITY_ENABLED=true
      fi
    fi
    
    # Check API server manifest for explicit enable
    if [ -f /host/etc/kubernetes/manifests/kube-apiserver.yaml ]; then
      if grep -q "\--enable-admission-plugins.*PodSecurity" /host/etc/kubernetes/manifests/kube-apiserver.yaml; then
        echo "PASS: PodSecurity admission controller explicitly enabled (manifest)"
        PODSECURITY_ENABLED=true
      fi
    fi
    
    # Check running process for explicit enable
    if [ -d /host/proc ]; then
      for pid in $(ls /host/proc | grep '^[0-9]*$'); do
        if [ -f "/host/proc/$pid/cmdline" ] && grep -q kube-apiserver "/host/proc/$pid/cmdline" 2>/dev/null; then
          if tr '\0' ' ' < "/host/proc/$pid/cmdline" | grep -q "\--enable-admission-plugins.*PodSecurity"; then
            echo "PASS: PodSecurity admission controller explicitly enabled (process)"
            PODSECURITY_ENABLED=true
          fi
        fi
      done
    fi
    
    # If PodSecurity is explicitly enabled, this is a pass
    if [ "$PODSECURITY_ENABLED" = "true" ]; then
      exit 0
    fi
    
    # Check for namespace-level Pod Security Standards configuration (indicates PSS is functional)
    if command -v kubectl >/dev/null 2>&1; then
      NAMESPACES_WITH_PSS=$(kubectl get namespaces -o yaml 2>/dev/null | grep -c "pod-security.kubernetes.io")
      if [ "$NAMESPACES_WITH_PSS" -gt 0 ]; then
        echo "PASS: PodSecurity admission controller enabled (found $NAMESPACES_WITH_PSS namespace labels)"
        exit 0
      fi
    fi
    
    # Check Kubernetes version (PodSecurity is enabled by default in 1.25+)
    if command -v kubectl >/dev/null 2>&1; then
      K8S_VERSION=$(kubectl version --short 2>/dev/null | grep "Server Version" | grep -o "v[0-9]*\.[0-9]*" | sed 's/v//')
      if [ -n "$K8S_VERSION" ]; then
        MAJOR=$(echo "$K8S_VERSION" | cut -d'.' -f1)
        MINOR=$(echo "$K8S_VERSION" | cut -d'.' -f2)
        if [ "$MAJOR" -gt 1 ] || ([ "$MAJOR" -eq 1 ] && [ "$MINOR" -ge 25 ]); then
          echo "PASS: PodSecurity admission controller enabled by default in Kubernetes $K8S_VERSION"
          exit 0
        fi
      fi
    fi
    
    echo "FAIL: PodSecurity admission controller status unclear - no explicit configuration found"
    exit 1
