apiVersion: compliance-operator.alauda.io/v1alpha1
kind: Rule
metadata:
  name: stig-k8s-etcd-data-directory-permissions
  namespace: compliance-system
spec:
  id: "V-242425"
  title: "The Kubernetes etcd must have file permissions set to 644 or more restrictive."
  description: |
    The etcd database stores the state of the cluster. If an attacker can gain access to the etcd database, changes can be made to open vulnerabilities and bypass user authorizations inherent within Kubernetes with RBAC implemented. The etcd data directory should have restrictive permissions to prevent unauthorized access.
  checkText: |
    Determine the etcd data directory by running the following command:
    ps -ef | grep etcd

    Note the data directory (identified by --data-dir).

    Run the command:
    stat -c %a <etcd_data_directory>

    If the directory has permissions more permissive than "700", this is a finding.
  fixText: |
    Run the command:
    chmod 700 <etcd_data_directory>

    To verify the change took place, run the command:
    stat -c %a <etcd_data_directory>
  severity: "medium"
  checkType: "node"
  checkScript: |
    #!/bin/bash
    # Check etcd data directory permissions
    # STIG requirement: etcd data directory should have 700 permissions or more restrictive
    
    echo "Checking etcd data directory permissions..."
    
    ETCD_DATA_DIRS=()
    VIOLATIONS_FOUND=false
    DIRS_CHECKED=0
    
    # Method 1: Find data directory from running etcd process
    if [ -d "/host/proc" ]; then
      for pid in $(ls /host/proc | grep '^[0-9]*$'); do
        if [ -f "/host/proc/$pid/cmdline" ] && grep -q etcd "/host/proc/$pid/cmdline" 2>/dev/null; then
          cmdline=$(tr '\0' ' ' < "/host/proc/$pid/cmdline")
          if echo "$cmdline" | grep -q -- "--data-dir"; then
            data_dir=$(echo "$cmdline" | sed 's/.*--data-dir[= ]\([^ ]*\).*/\1/')
            ETCD_DATA_DIRS+=("/host$data_dir")
            echo "✓ Found etcd data directory from process: /host$data_dir"
          fi
        fi
      done
    fi
    
    # Method 2: Check common etcd data directory locations
    COMMON_ETCD_DIRS=(
      "/host/var/lib/etcd"
      "/host/var/etcd"
      "/host/opt/etcd/data"
      "/host/etc/etcd/data"
    )
    
    for common_dir in "${COMMON_ETCD_DIRS[@]}"; do
      if [ -d "$common_dir" ]; then
        ETCD_DATA_DIRS+=("$common_dir")
        echo "✓ Found common etcd data directory: $common_dir"
      fi
    done
    
    # Check permissions of found directories
    for etcd_dir in "${ETCD_DATA_DIRS[@]}"; do
      if [ -d "$etcd_dir" ]; then
        DIRS_CHECKED=$((DIRS_CHECKED + 1))
        dirname=$(basename "$etcd_dir")
        perms=$(stat -c "%a" "$etcd_dir" 2>/dev/null)
        
        echo "  ✓ Checking etcd data directory: $etcd_dir"
        echo "    Permissions: $perms"
        
        # Check if permissions are more permissive than 700
        if [ "$perms" -gt 700 ]; then
          echo "    ✗ VIOLATION: etcd data directory has overly permissive permissions: $perms (should be 700 or more restrictive)"
          VIOLATIONS_FOUND=true
        else
          echo "    ✓ OK: etcd data directory has appropriate permissions: $perms"
        fi
        
        # Check key subdirectories permissions
        if [ -d "$etcd_dir/member" ]; then
          member_perms=$(stat -c "%a" "$etcd_dir/member" 2>/dev/null)
          echo "    ✓ Checking member subdirectory permissions: $member_perms"
          
          if [ "$member_perms" -gt 700 ]; then
            echo "    ✗ WARNING: etcd member subdirectory has overly permissive permissions: $member_perms"
          fi
        fi
      fi
    done
    
    if [ "$DIRS_CHECKED" -eq 0 ]; then
      echo "WARNING: No etcd data directories found"
      exit 0
    fi
    
    if [ "$VIOLATIONS_FOUND" = true ]; then
      echo "FAIL: Found etcd data directories with overly permissive permissions"
      exit 1
    else
      echo "PASS: All etcd data directories have appropriate permissions (700 or more restrictive)"
      exit 0
    fi