apiVersion: compliance-operator.alauda.io/v1alpha1
kind: Rule
metadata:
  name: stig-k8s-controller-manager-profiling-disabled
  namespace: compliance-system
spec:
  id: "V-242409"
  title: "Kubernetes Controller Manager must disable profiling."
  description: |
    Kubernetes profiling provides the ability to analyze and troubleshoot Controller Manager events over a web interface on a host port. Enabling this service can expose details about the Kubernetes architecture. This service must not be enabled unless deemed necessary.
  checkText: |
    Change to the /etc/kubernetes/manifests/ directory on the Kubernetes Control Plane. Run the command:
    grep -i profiling * 

    If the setting "profiling" is not configured in the Kubernetes Controller Manager manifest file or it is set to "True", this is a finding.
  fixText: |
    Edit the Kubernetes Controller Manager manifest file in the /etc/kubernetes/manifests directory on the Kubernetes Control Plane. Set the argument "--profiling value" to "false".
  checkType: "platform"
  severity: "medium"
  checkScript: |
    #!/bin/bash
    # Check if Controller Manager profiling is disabled
    # STIG requirement: --profiling should be set to false or not present
    
    echo "Checking Controller Manager profiling configuration..."
    
    # Method 1: Check via kubectl for controller-manager pods
    if command -v kubectl >/dev/null 2>&1; then
      if kubectl get pod -n kube-system -l component=kube-controller-manager -o yaml 2>/dev/null | grep -q "profiling.*true"; then
        echo "FAIL: Controller Manager profiling is enabled (kubectl)"
        exit 1
      fi
    fi
    
    # Method 2: Check controller-manager manifest file
    if [ -f /host/etc/kubernetes/manifests/kube-controller-manager.yaml ]; then
      if grep -q "profiling.*true" /host/etc/kubernetes/manifests/kube-controller-manager.yaml; then
        echo "FAIL: Controller Manager profiling is enabled (manifest)"
        exit 1
      fi
    fi
    
    # Method 3: Check controller-manager process
    if pgrep kube-controller-manager >/dev/null 2>&1; then
      if ps aux | grep kube-controller-manager | grep -q "profiling.*true"; then
        echo "FAIL: Controller Manager profiling is enabled (process)"
        exit 1
      fi
    fi
    
    echo "PASS: Controller Manager profiling is properly configured (disabled or not present)"
    exit 0