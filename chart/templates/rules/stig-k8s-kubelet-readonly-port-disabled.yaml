apiVersion: compliance-operator.alauda.io/v1alpha1
kind: Rule
metadata:
  name: stig-k8s-kubelet-readonly-port-disabled
  namespace: compliance-system
spec:
  id: "V-242387"
  title: "The Kubernetes Kubelet must have the readOnlyPort flag disabled."
  description: |
    The kubelet process provides a read-only API in addition to the main Kubernetes API. Unauthenticated access to this read-only API could disclose information about the cluster that could assist an attacker in a further attack. The read-only API provided by the kubelet must be disabled.
    
    By default, the kubelet serves a read-only view of most of its internal state on a port (typically 10255). This port does not require authentication and provides access to potentially sensitive information about the node and running workloads. Disabling this port reduces the attack surface.
  checkText: |
    On the Control Plane and Worker Nodes, run the command:
    ps -ef | grep kubelet
    
    Check the config file(s) referenced by the --config argument (example: --config=/etc/kubernetes/kubelet/kubelet-config.yaml):
    
    Change to the directory identified by --config argument and run the command:
    grep -i readonlyport *
    
    If the setting \"readOnlyPort\" is not set to \"0\", this is a finding.
    
    If the setting \"readOnlyPort\" is not configured, check if there is an entry for \"--read-only-port\" in the kubelet command.
    
    On the Control Plane and Worker Nodes, run the command:
    ps -ef | grep kubelet
    
    If \"--read-only-port\" is not set to \"0\", this is a finding.
  fixText: |
    On the Control Plane and Worker Nodes, run the command:
    ps -ef | grep kubelet
    
    Check the config file(s) referenced by the --config argument (example: --config=/etc/kubernetes/kubelet/kubelet-config.yaml):
    
    Edit the Kubernetes Kubelet config file:
    Set the value of \"readOnlyPort\" to \"0\".
    
    Reset Kubelet service using the following command:
    service kubelet restart
  severity: "high"
  checkType: "node"
  checkScript: |
    #!/bin/bash
    # Check if kubelet read-only port is disabled
    # STIG requirement: read-only port should be disabled (set to 0)
    
    READONLY_PORT_DISABLED=false
    CONFIG_FOUND=false
    
    echo "Checking kubelet read-only port configuration..."
    
    # Function to check kubelet config file for read-only port setting
    check_kubelet_readonly_config() {
      local config_file="$1"
      if [ -f "$config_file" ]; then
        CONFIG_FOUND=true
        echo "✓ Config file found: $config_file"
        
        # Check if readOnlyPort is explicitly set to 0
        if grep -q "readOnlyPort: 0" "$config_file"; then
          echo "✓ Found: readOnlyPort explicitly disabled (readOnlyPort: 0)"
          READONLY_PORT_DISABLED=true
          return 0
        fi
        
        # Check if readOnlyPort is set to a non-zero value
        if grep -q "readOnlyPort:" "$config_file"; then
          local port_value=$(grep "readOnlyPort:" "$config_file" | sed 's/.*readOnlyPort: *\([0-9]*\).*/\1/')
          if [ "$port_value" != "0" ] 2>/dev/null; then
            echo "✗ Found: readOnlyPort enabled with value: $port_value"
            return 1
          fi
        else
          echo "ℹ No explicit readOnlyPort setting found (using default behavior)"
        fi
      fi
      return 0
    }
    
    # Function to check kubelet process for read-only port parameter
    check_kubelet_readonly_process() {
      if [ -d /host/proc ]; then
        for pid in $(ls /host/proc | grep '^[0-9]*$'); do
          if [ -f "/host/proc/$pid/cmdline" ] && grep -q kubelet "/host/proc/$pid/cmdline" 2>/dev/null; then
            echo "✓ Found kubelet process: PID $pid"
            
            # Check for explicit --read-only-port=0
            if tr '\0' ' ' < "/host/proc/$pid/cmdline" | grep -q -- "--read-only-port=0"; then
              echo "✓ Found: --read-only-port=0 in kubelet command line"
              READONLY_PORT_DISABLED=true
              return 0
            fi
            
            # Check for non-zero read-only port
            if tr '\0' ' ' < "/host/proc/$pid/cmdline" | grep -q -- "--read-only-port=[1-9]"; then
              echo "✗ Found: read-only port enabled via command line"
              return 1
            fi
            
            echo "ℹ No explicit --read-only-port parameter found (using default)"
          fi
        done
      fi
      return 0
    }
    
    # Function to test if read-only port is actually accessible
    test_readonly_port_access() {
      # Test common read-only port (10255)
      if command -v netstat >/dev/null 2>&1; then
        if netstat -tlnp 2>/dev/null | grep -q ":10255"; then
          echo "✗ Read-only port 10255 is listening"
          return 1
        else
          echo "✓ Read-only port 10255 is not listening"
          READONLY_PORT_DISABLED=true
          return 0
        fi
      fi
      
      # Alternative check using /proc/net/tcp
      if [ -f /host/proc/net/tcp ]; then
        # 10255 in hex is 280F
        if grep -q ":280F " /host/proc/net/tcp; then
          echo "✗ Read-only port 10255 is listening (found in /proc/net/tcp)"
          return 1
        else
          echo "✓ Read-only port 10255 is not listening (checked /proc/net/tcp)"
          READONLY_PORT_DISABLED=true
          return 0
        fi
      fi
      
      return 0
    }
    
    # Check kubelet configuration files
    for config_path in "/host/var/lib/kubelet/config.yaml" "/host/etc/kubernetes/kubelet/kubelet-config.yaml" "/host/etc/kubernetes/kubelet.yaml"; do
      if ! check_kubelet_readonly_config "$config_path"; then
        echo "FAIL: Read-only port is enabled in configuration"
        exit 1
      fi
    done
    
    # Check kubelet process parameters
    if ! check_kubelet_readonly_process; then
      echo "FAIL: Read-only port is enabled via command line"
      exit 1
    fi
    
    # Test actual port accessibility
    if ! test_readonly_port_access; then
      echo "FAIL: Read-only port is accessible"
      exit 1
    fi
    
    # Final evaluation
    echo ""
    echo "=== EVALUATION ==="
    echo "Config found: $CONFIG_FOUND"
    echo "Read-only port disabled: $READONLY_PORT_DISABLED"
    
    if [ "$READONLY_PORT_DISABLED" = "true" ]; then
      echo "✓ PASS: Kubelet read-only port is disabled"
      exit 0
    elif [ "$CONFIG_FOUND" = "true" ]; then
      # If config was found but no explicit setting, check Kubernetes version behavior
      echo "ℹ No explicit readOnlyPort setting found"
      echo "ℹ Kubernetes 1.20+ disables read-only port by default"
      echo "✓ PASS: Read-only port disabled by default (Kubernetes security improvement)"
      exit 0
    else
      echo "FAIL: No kubelet configuration found to verify read-only port status"
      exit 1
    fi
