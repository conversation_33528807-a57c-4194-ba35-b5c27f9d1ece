# https://stigviewer.com/stigs/kubernetes/2025-02-20/finding/V-242400
# https://stigviewer.com/stigs/kubernetes
---
apiVersion: compliance-operator.alauda.io/v1alpha1
kind: Rule
metadata:
  name: stig-k8s-api-server-alpha-apis-disabled
  namespace: compliance-system
spec:
  id: "V-242400"
  title: "The Kubernetes API server must have Alpha APIs disabled."
  description: |
    Kubernetes allows alpha API calls within the API server. The alpha features are disabled by default since they are not ready for production and likely to change without notice. These features may also contain security issues that are rectified as the feature matures. To keep the Kubernetes cluster secure and stable, these alpha features must not be used.
  checkText: |
    On the Control Plane, change to the manifests' directory at /etc/kubernetes/manifests and run the command:
    grep -i feature-gates *
    
    Review the "--feature-gates" setting, if one is returned.
    
    If the "--feature-gate"s setting is available and contains the "AllAlpha" flag set to "true", this is a finding.
  fixText: |
    Edit any manifest file that contains the "--feature-gates" setting with "AllAlpha" set to "true".
    
    Set the value of "AllAlpha" to "false" or remove the setting completely. (AllAlpha - default=false)
  severity: "medium"
  checkType: "platform"
  checkScript: |
    #!/bin/bash
    # Check if Alpha APIs are disabled via feature-gates
    # STIG requirement: --feature-gates should not contain AllAlpha=true
    
    echo "Checking API server for AllAlpha feature gate configuration..."
    
    ALLALPHA_ENABLED=false
    
    # Method 1: Check via kubectl
    if command -v kubectl >/dev/null 2>&1; then
      if kubectl get pod -n kube-system -l component=kube-apiserver -o yaml 2>/dev/null | grep -q "feature-gates.*AllAlpha=true"; then
        echo "FAIL: AllAlpha feature gate is enabled (kubectl)"
        ALLALPHA_ENABLED=true
      fi
    fi
    
    # Method 2: Check API server manifest
    if [ -f /host/etc/kubernetes/manifests/kube-apiserver.yaml ]; then
      if grep -q "feature-gates.*AllAlpha=true" /host/etc/kubernetes/manifests/kube-apiserver.yaml; then
        echo "FAIL: AllAlpha feature gate is enabled (manifest)"
        ALLALPHA_ENABLED=true
      fi
    fi
    
    # Method 3: Check running process via host proc
    if [ -d /host/proc ]; then
      for pid in $(ls /host/proc | grep '^[0-9]*$'); do
        if [ -f "/host/proc/$pid/cmdline" ] && grep -q kube-apiserver "/host/proc/$pid/cmdline" 2>/dev/null; then
          if tr '\0' ' ' < "/host/proc/$pid/cmdline" | grep -q "feature-gates.*AllAlpha=true"; then
            echo "FAIL: AllAlpha feature gate is enabled (process)"
            ALLALPHA_ENABLED=true
          fi
        fi
      done
    fi
    
    # If AllAlpha is enabled, this is a finding
    if [ "$ALLALPHA_ENABLED" = "true" ]; then
      exit 1
    fi
    
    # If we reach here, AllAlpha is not explicitly enabled
    echo "PASS: AllAlpha feature gate is not enabled (Alpha APIs disabled)"
    exit 0
