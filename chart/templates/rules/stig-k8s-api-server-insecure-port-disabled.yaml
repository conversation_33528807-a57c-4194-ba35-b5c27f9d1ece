apiVersion: compliance-operator.alauda.io/v1alpha1
kind: Rule
metadata:
  name: stig-k8s-api-server-insecure-port-disabled
  namespace: compliance-system
spec:
  id: "V-242386"
  title: "The Kubernetes API server must have the insecure port flag disabled."
  description: |
    By default, the API server will listen on two ports. One port is the secure port and the other port is called the "localhost port". This port is also called the "insecure port", port 8080. Any requests to this port bypass authentication and authorization checks. If this port is left open, anyone who gains access to the host on which the Control Plane is running can bypass all authorization and authentication mechanisms put in place, and have full control over the entire cluster.

    Close the insecure port by setting the API server's "--insecure-port" flag to "0", ensuring that the "--insecure-bind-address" is not set.
  checkText: |
    Change to the /etc/kubernetes/manifests directory on the Kubernetes Control Plane. Run the command:
    grep -i insecure-port *

    If the setting "--insecure-port" is not set to "0" or is not configured in the Kubernetes API server manifest file, this is a finding.

    Note: "--insecure-port" flag has been deprecated and can only be set to "0". **This flag will be removed in v1.24.*
  fixText: |
    Edit the Kubernetes API Server manifest file in the /etc/kubernetes/manifests directory on the Kubernetes Control Plane.

    Set the value of "--insecure-port" to "0".
  severity: "high"
  checkType: "platform"
  checkScript: |
    #!/bin/bash
    # Check if insecure port is disabled
    # STIG requirement: Insecure port (8080) must be disabled
    
    # Method 1: Check for explicit --insecure-port=0 parameter
    EXPLICIT_DISABLED=false
    
    # Check API server via kubectl
    if command -v kubectl >/dev/null 2>&1; then
      if kubectl get pod -n kube-system -l component=kube-apiserver -o yaml 2>/dev/null | grep -q "\--insecure-port=0"; then
        EXPLICIT_DISABLED=true
      fi
    fi
    
    # Check API server manifest
    if [ -f /host/etc/kubernetes/manifests/kube-apiserver.yaml ]; then
      if grep -q "\--insecure-port=0" /host/etc/kubernetes/manifests/kube-apiserver.yaml; then
        EXPLICIT_DISABLED=true
      fi
    fi
    
    # Check running process via host proc
    if [ -d /host/proc ]; then
      for pid in $(ls /host/proc | grep '^[0-9]*$'); do
        if [ -f "/host/proc/$pid/cmdline" ] && grep -q kube-apiserver "/host/proc/$pid/cmdline" 2>/dev/null; then
          if tr '\0' ' ' < "/host/proc/$pid/cmdline" | grep -q "\--insecure-port=0"; then
            EXPLICIT_DISABLED=true
            break
          fi
        fi
      done
    fi
    
    # If explicitly disabled, we're compliant
    if [ "$EXPLICIT_DISABLED" = "true" ]; then
      echo "PASS: Insecure port explicitly disabled (--insecure-port=0)"
      exit 0
    fi
    
    # Method 2: Check if insecure port is explicitly enabled with non-zero value (non-compliant)
    if command -v kubectl >/dev/null 2>&1; then
      if kubectl get pod -n kube-system -l component=kube-apiserver -o yaml 2>/dev/null | grep -q "\--insecure-port=[1-9]"; then
        echo "FAIL: Insecure port is explicitly configured with non-zero value"
        exit 1
      fi
    fi
    
    # Method 3: Check if any process is listening on port 8080
    if command -v netstat >/dev/null 2>&1; then
      if netstat -tlnp 2>/dev/null | grep -q ":8080 "; then
        echo "FAIL: Port 8080 is being listened on"
        exit 1
      fi
    elif command -v ss >/dev/null 2>&1; then
      if ss -tlnp 2>/dev/null | grep -q ":8080 "; then
        echo "FAIL: Port 8080 is being listened on"
        exit 1
      fi
    fi
    
    # Method 4: Test localhost accessibility (most reliable test)
    if command -v curl >/dev/null 2>&1; then
      CURL_OUTPUT=$(timeout 3 curl -s "http://localhost:8080/api/v1" 2>/dev/null)
      CURL_EXIT_CODE=$?
      if [ $CURL_EXIT_CODE -eq 0 ] && [ -n "$CURL_OUTPUT" ] && echo "$CURL_OUTPUT" | grep -q '"kind"'; then
        echo "FAIL: Insecure port 8080 is accessible on localhost"
        exit 1
      fi
    fi
    
    # Method 5: Check Kubernetes version for default behavior
    if command -v kubectl >/dev/null 2>&1; then
      K8S_VERSION=$(kubectl version --short 2>/dev/null | grep "Server Version" | grep -o "v[0-9]*\.[0-9]*" | sed 's/v//')
      if [ -n "$K8S_VERSION" ]; then
        MAJOR=$(echo "$K8S_VERSION" | cut -d. -f1)
        MINOR=$(echo "$K8S_VERSION" | cut -d. -f2)
        # Kubernetes 1.20+ has insecure-port=0 as default
        if [ "$MAJOR" -gt 1 ] || ([ "$MAJOR" -eq 1 ] && [ "$MINOR" -ge 20 ]); then
          echo "PASS: Insecure port disabled by default (Kubernetes $K8S_VERSION)"
          exit 0
        fi
      fi
    fi
    
    echo "PASS: No evidence of insecure port being enabled"
    exit 0
