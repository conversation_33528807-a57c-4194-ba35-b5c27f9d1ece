apiVersion: compliance-operator.alauda.io/v1alpha1
kind: Rule
metadata:
  name: stig-k8s-controller-manager-secure-binding
  namespace: compliance-system
spec:
  id: "V-242385"
  title: "The Kubernetes Controller Manager must have secure binding."
  description: |
    Limiting the number of attack vectors and implementing authentication and encryption on the endpoints available to external sources is paramount when securing the overall Kubernetes cluster. The Controller Manager API service exposes port 10252/TCP by default for health and metrics information use. This port does not encrypt or authenticate connections. If this port is exposed externally, an attacker can use this port to attack the entire Kubernetes cluster. By setting the bind address to only localhost (i.e., 127.0.0.1), only those internal services that require health and metrics information can access the Control Manager API.
  checkText: |
    Change to the /etc/kubernetes/manifests directory on the Kubernetes Control Plane. Run the command:
    grep -i bind-address *
    
    If the setting bind-address is not set to "127.0.0.1" or is not found in the Kubernetes Controller Manager manifest file, this is a finding.
  fixText: |
    Edit the Kubernetes Controller Manager manifest file in the /etc/kubernetes/manifests directory on the Kubernetes Control Plane. Set the argument "--bind-address" to "127.0.0.1".
  severity: "medium"
  checkType: "platform"
  checkScript: |
    #!/bin/bash
    # Check if controller manager has secure binding
    # STIG requirement: Controller manager should not bind to insecure addresses like 0.0.0.0
    
    INSECURE_BINDING=false
    BIND_ADDRESS_FOUND=false
    ACTUAL_BIND_ADDRESS=""
    
    # Function to check if an address is insecure
    is_insecure_address() {
      local addr="$1"
      case "$addr" in
        "0.0.0.0"|"::"|"*"|"")
          return 0  # insecure
          ;;
        *)
          return 1  # secure
          ;;
      esac
    }
    
    # Method 1: Check controller manager via kubectl
    if command -v kubectl >/dev/null 2>&1; then
      CM_YAML=$(kubectl get pod -n kube-system -l component=kube-controller-manager -o yaml 2>/dev/null)
      if [ -n "$CM_YAML" ]; then
        # Extract bind-address parameter
        BIND_ADDR=$(echo "$CM_YAML" | grep -o "\--bind-address=[^[:space:]]*" | tail -1 | cut -d= -f2)
        if [ -n "$BIND_ADDR" ]; then
          BIND_ADDRESS_FOUND=true
          ACTUAL_BIND_ADDRESS="$BIND_ADDR"
          if is_insecure_address "$BIND_ADDR"; then
            INSECURE_BINDING=true
          fi
        fi
      fi
    fi
    
    # Method 2: Check controller manager manifest
    if [ -f /host/etc/kubernetes/manifests/kube-controller-manager.yaml ]; then
      BIND_ADDR=$(grep -o "\--bind-address=[^[:space:]]*" /host/etc/kubernetes/manifests/kube-controller-manager.yaml 2>/dev/null | tail -1 | cut -d= -f2)
      if [ -n "$BIND_ADDR" ]; then
        BIND_ADDRESS_FOUND=true
        ACTUAL_BIND_ADDRESS="$BIND_ADDR"
        if is_insecure_address "$BIND_ADDR"; then
          INSECURE_BINDING=true
        fi
      fi
    fi
    
    # Method 3: Check running process via host proc
    if [ -d /host/proc ]; then
      for pid in $(ls /host/proc | grep '^[0-9]*$'); do
        if [ -f "/host/proc/$pid/cmdline" ] && grep -q kube-controller-manager "/host/proc/$pid/cmdline" 2>/dev/null; then
          CMDLINE=$(tr '\0' ' ' < "/host/proc/$pid/cmdline")
          BIND_ADDR=$(echo "$CMDLINE" | grep -o "\--bind-address=[^[:space:]]*" | tail -1 | cut -d= -f2)
          if [ -n "$BIND_ADDR" ]; then
            BIND_ADDRESS_FOUND=true
            ACTUAL_BIND_ADDRESS="$BIND_ADDR"
            if is_insecure_address "$BIND_ADDR"; then
              INSECURE_BINDING=true
            fi
            break
          fi
        fi
      done
    fi
    
    # Evaluate results
    if [ "$INSECURE_BINDING" = "true" ]; then
      echo "FAIL: Controller Manager is bound to insecure address ($ACTUAL_BIND_ADDRESS)"
      exit 1
    elif [ "$BIND_ADDRESS_FOUND" = "true" ]; then
      echo "PASS: Controller Manager is securely bound to $ACTUAL_BIND_ADDRESS"
      exit 0
    else
      # If no bind-address is specified, check default behavior
      echo "PASS: Controller Manager secure binding (no insecure bind-address found)"
      exit 0
    fi
