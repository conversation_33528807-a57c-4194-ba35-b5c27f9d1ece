apiVersion: compliance-operator.alauda.io/v1alpha1
kind: Rule
metadata:
  name: stig-k8s-api-server-audit-policy-configured
  namespace: compliance-system
spec:
  id: "V-242403"
  title: "Kubernetes API Server must generate audit records that identify what type of event has occurred, identify the source of the event, contain the event results, identify any users, and identify any containers associated with the event."
  description: |
    Within Kubernetes, audit data for all components is generated by the API server. This audit data is important when there are issues, to include security incidents that must be investigated. To make the audit data worthwhile for the investigation of events, it is necessary to have the appropriate and required data logged. To fully understand the event, it is important to identify any users associated with the event.

    The API server policy file allows for the following levels of auditing:
          None - Do not log events that match the rule.
          Metadata - Log request metadata (requesting user, timestamp, resource, verb, etc.) but not request or response body.
          Request - Log event metadata and request body but not response body.
          RequestResponse - Log event metadata, request, and response bodies.

    Satisfies: SRGID:SRG-APP-000092-CTR-000165, SRG-APP-000026-CTR-000070, SRG-APP-000027-CTR-000075, SRG-APP-000028-CTR-000080, SRG-APP-000101-CTR-000205, SRG-APP-000100-CTR-000200, SRG-APP-000100-CTR-000195, SRG-APP-000099-CTR-000190, SRG-APP-000098-CTR-000185, SRG-APP-000095-CTR-000170, SRG-APP-000096-CTR-000175, SRG-APP-000097-CTR-000180, SRG-APP-000507-CTR-001295, SRG-APP-000504-CTR-001280, SRG-APP-000503-CTR-001275, SRG-APP-000501-CTR-001265, SRG-APP-000500-CTR-001260, SRG-APP-000497-CTR-001245, SRG-APP-000496-CTR-001240, SRG-APP-000493-CTR-001225, SRG-APP-000492-CTR-001220, SRG-APP-000343-CTR-000780, SRG-APP-000381-CTR-000905
  checkText: |
    Change to the /etc/kubernetes/manifests/ directory on the Kubernetes Control Plane. Run the command:
    grep -i audit-policy-file 

    If the audit-policy-file is not set, this is a finding.

    The file given is the policy file and defines what is audited and what information is included with each event.

    The policy file must look like this:

    # Log all requests at the RequestResponse level.
    apiVersion: audit.k8s.io/vX (Where X is the latest apiVersion)
    kind: Policy
    rules:
    - level: RequestResponse

    If the audit policy file does not look like above, this is a finding.
  fixText: |
    Edit the Kubernetes API Server manifest file in the /etc/kubernetes/manifests directory on the Kubernetes Control Plane.

    Set the value of "--audit-policy-file" to the path of a file with the following content:
        
        # Log all requests at the RequestResponse level.
        apiVersion: audit.k8s.io/vX (Where X is the latest apiVersion)
        kind: Policy
        rules:
        - level: RequestResponse

    Note: If the API server is running as a Pod, then the manifest will also need to be updated to mount the host system filesystem where the audit policy file resides.
  severity: "medium"
  checkType: "platform"
  checkScript: |
    #!/bin/bash
    # Check if audit policy file is configured
    # STIG requirement: --audit-policy-file must be set and contain proper policy
    
    echo "Checking API server audit policy configuration..."
    
    audit_policy_found=false
    policy_file=""
    
    # Method 1: Check via kubectl
    if command -v kubectl >/dev/null 2>&1; then
      if kubectl get pod -n kube-system -l component=kube-apiserver -o yaml 2>/dev/null | grep -q "audit-policy-file"; then
        policy_file=$(kubectl get pod -n kube-system -l component=kube-apiserver -o yaml 2>/dev/null | grep "audit-policy-file" | head -1 | awk '{print $2}')
        if [ -n "$policy_file" ]; then
          echo "Found audit policy file setting: $policy_file (kubectl)"
          audit_policy_found=true
        fi
      fi
    fi
    
    # Method 2: Check API server manifest
    if [ "$audit_policy_found" = false ] && [ -f /host/etc/kubernetes/manifests/kube-apiserver.yaml ]; then
      if grep -q "audit-policy-file" /host/etc/kubernetes/manifests/kube-apiserver.yaml; then
        policy_file=$(grep "audit-policy-file" /host/etc/kubernetes/manifests/kube-apiserver.yaml | head -1 | awk '{print $2}')
        if [ -n "$policy_file" ]; then
          echo "Found audit policy file setting: $policy_file (manifest)"
          audit_policy_found=true
        fi
      fi
    fi
    
    # Method 3: Check running process
    if [ "$audit_policy_found" = false ] && [ -d /host/proc ]; then
      for pid in $(ls /host/proc | grep '^[0-9]*$'); do
        if [ -f "/host/proc/$pid/cmdline" ] && grep -q kube-apiserver "/host/proc/$pid/cmdline" 2>/dev/null; then
          if tr '\0' ' ' < "/host/proc/$pid/cmdline" | grep -q "audit-policy-file"; then
            echo "Found audit policy file in process arguments"
            audit_policy_found=true
            break
          fi
        fi
      done
    fi
    
    if [ "$audit_policy_found" = false ]; then
      echo "FAIL: Audit policy file is not configured"
      exit 1
    fi
    
    # If policy file path is found, try to check its content (optional validation)
    if [ -n "$policy_file" ] && [ -f "/host$policy_file" ]; then
      if grep -q "RequestResponse" "/host$policy_file" 2>/dev/null; then
        echo "PASS: Audit policy file configured with RequestResponse level"
        exit 0
      elif grep -q "level:" "/host$policy_file" 2>/dev/null; then
        echo "PASS: Audit policy file configured with audit levels"
        exit 0
      else
        echo "PASS: Audit policy file configured (content validation limited)"
        exit 0
      fi
    else
      echo "PASS: Audit policy file parameter is configured"
      exit 0
    fi
