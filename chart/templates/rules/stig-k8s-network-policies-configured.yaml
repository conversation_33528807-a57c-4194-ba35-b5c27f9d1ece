apiVersion: compliance-operator.alauda.io/v1alpha1
kind: Rule
metadata:
  name: stig-k8s-network-policies-configured
  namespace: compliance-system
spec:
  id: "V-242417"
  title: "Kubernetes must separate user functionality."
  description: |
    Separating user functionality from management functionality is required for all components within the Kubernetes Control Plane. Without the separation, users may have access to management functions that can degrade or compromise the operational effectiveness of the system. Kubernetes namespaces are the way to separate user functionality from management functionality.
    
    Network policies provide additional separation by controlling network traffic between pods and namespaces. Properly configured network policies help ensure that user workloads cannot interfere with system components and that different user applications are isolated from each other.
  checkText: |
    To check if network policies are configured to separate user functionality, run the command:
    kubectl get networkpolicies --all-namespaces
    
    Review the network policies to ensure that:
    1. System namespaces (kube-system, kube-public) have appropriate network policies
    2. User namespaces have network policies that restrict unnecessary communication
    3. Default deny policies are in place where appropriate
    
    If network policies are not configured to properly separate user functionality from management functionality, this is a finding.
  fixText: |
    Configure network policies to separate user functionality from management functionality:
    
    1. Create default deny network policies for user namespaces
    2. Create specific allow policies for required communication
    3. Ensure system namespaces have appropriate network restrictions
    
    Example default deny policy:
    apiVersion: networking.k8s.io/v1
    kind: NetworkPolicy
    metadata:
      name: default-deny-all
    spec:
      podSelector: {}
      policyTypes:
      - Ingress
      - Egress
  severity: "medium"
  checkType: "platform"
  checkScript: |
    #!/bin/bash
    # Check if network policies are configured
    # STIG requirement: Network policies should be configured to control traffic
    
    echo "Checking for configured network policies..."
    
    if ! command -v kubectl >/dev/null 2>&1; then
      echo "SKIP: kubectl not available for network policy check"
      exit 0
    fi
    
    # Check for any network policies in the cluster
    network_policies=$(kubectl get networkpolicy --all-namespaces --no-headers 2>/dev/null | wc -l)
    
    if [ "$network_policies" -gt 0 ]; then
      echo "PASS: Found $network_policies network policies configured"
      kubectl get networkpolicy --all-namespaces --no-headers 2>/dev/null | while read namespace name rest; do
        echo "  - $namespace/$name"
      done
      exit 0
    fi
    
    # Check for CNI that provides default network policies
    if kubectl get pods -n kube-system --no-headers 2>/dev/null | grep -q -E "(calico|cilium|weave)"; then
      echo "PASS: CNI with network policy support detected"
      exit 0
    fi
    
    # Check system namespaces specifically
    system_policies=$(kubectl get networkpolicy -n kube-system --no-headers 2>/dev/null | wc -l)
    if [ "$system_policies" -gt 0 ]; then
      echo "PASS: Network policies found in system namespace"
      exit 0
    fi
    
    # Check if cluster has any user namespaces with policies
    user_namespaces=$(kubectl get namespaces --no-headers 2>/dev/null | grep -v -E "^(default|kube-system|kube-public|kube-node-lease)" | awk '{print $1}')
    for ns in $user_namespaces; do
      if [ -n "$ns" ]; then
        ns_policies=$(kubectl get networkpolicy -n "$ns" --no-headers 2>/dev/null | wc -l)
        if [ "$ns_policies" -gt 0 ]; then
          echo "PASS: Network policies found in namespace $ns"
          exit 0
        fi
      fi
    done
    
    echo "FAIL: No network policies configured in the cluster"
    exit 1
