apiVersion: builds.katanomi.dev/v1alpha1
kind: Build
spec:
  git:
    options:
      depth: 1
      resources:
        limits:
          cpu: 200m
          memory: 200Mi
        requests:
          cpu: 200m
          memory: 200Mi
      retries: 0
      timeout: 10m
  tasks:
    - name: vendor
      runAfter: []
      timeout: 30m
      retries: 0
      taskRef:
        kind: ClusterTask
        name: go-build
      workspaces:
        - name: source
          workspace: source
      params:
        - name: command
          value: |-
            export GO111MODULE=on
            export GOPROXY=https://build-nexus.alauda.cn/repository/golang/,direct
            export GONOSUMDB="gitlab-ce.alauda.cn/*,gomod.alauda.cn/*"
            export GOSUMDB=gosum.io+ce6e7565+AY5qEHUk/qmHc5btzW45JVoENfazw8LielDsaI+lEbq6
            go mod vendor
        - name: build-outputs-path
          value: []
    - name: build-content-image
      runAfter:
        - vendor
      timeout: 45m
      retries: 1
      taskRef:
        kind: ClusterTask
        name: alauda-build-image
      workspaces:
        - name: source
          workspace: source
      when: []
      params:
        - name: container-image
          value: build-harbor.alauda.cn/ait/compliance-content
        - name: container-image-tag
          value: $(build.git.version.docker)
        - name: dockerfile
          value: ./images/content/Dockerfile
        - name: labels
          value:
            - branch=$(build.git.branch.name)
            - commit=$(build.git.lastCommit.id)
            - component=compliance-content
        - name: context
          value: ./images/content
    - name: build-operator-image
      runAfter:
        - vendor
      timeout: 45m
      retries: 1
      taskRef:
        kind: ClusterTask
        name: alauda-build-image
      workspaces:
        - name: source
          workspace: source
      when: []
      params:
        - name: container-image
          value: build-harbor.alauda.cn/ait/compliance-operator
        - name: container-image-tag
          value: $(build.git.version.docker)
        - name: dockerfile
          value: ./images/operator/Dockerfile
        - name: labels
          value:
            - branch=$(build.git.branch.name)
            - commit=$(build.git.lastCommit.id)
            - component=compliance-operator
    - name: build-unified-scanner-image
      runAfter:
        - build-content-image
      timeout: 45m
      retries: 1
      taskRef:
        kind: ClusterTask
        name: alauda-build-image
      workspaces:
        - name: source
          workspace: source
      when: []
      params:
        - name: container-image
          value: build-harbor.alauda.cn/ait/compliance-unified-scanner
        - name: container-image-tag
          value: $(build.git.version.docker)
        - name: dockerfile
          value: ./images/unified-scanner/Dockerfile
        - name: labels
          value:
            - branch=$(build.git.branch.name)
            - commit=$(build.git.lastCommit.id)
            - component=compliance-unified-scanner
        - name: context
          value: ./images/unified-scanner
    - name: build-openscap-scanner-image
      runAfter:
        - build-content-image
      timeout: 45m
      retries: 1
      taskRef:
        kind: ClusterTask
        name: alauda-build-image
      workspaces:
        - name: source
          workspace: source
      when: []
      params:
        - name: container-image
          value: build-harbor.alauda.cn/ait/compliance-openscap-scanner
        - name: container-image-tag
          value: $(build.git.version.docker)
        - name: dockerfile
          value: ./images/openscap-scanner/Dockerfile
        - name: labels
          value:
            - branch=$(build.git.branch.name)
            - commit=$(build.git.lastCommit.id)
            - component=compliance-openscap-scanner
        - name: context
          value: ./images/openscap-scanner
    - name: build-content-extractor-image
      runAfter:
        - build-content-image
      timeout: 45m
      retries: 1
      taskRef:
        kind: ClusterTask
        name: alauda-build-image
      workspaces:
        - name: source
          workspace: source
      when: []
      params:
        - name: container-image
          value: build-harbor.alauda.cn/ait/compliance-content-extractor
        - name: container-image-tag
          value: $(build.git.version.docker)
        - name: dockerfile
          value: ./images/content-extractor/Dockerfile
        - name: labels
          value:
            - branch=$(build.git.branch.name)
            - commit=$(build.git.lastCommit.id)
            - component=compliance-content-extractor
        - name: context
          value: ./images/content-extractor
    - name: build-report-service-image
      runAfter:
        - vendor
      timeout: 45m
      retries: 1
      taskRef:
        kind: ClusterTask
        name: alauda-build-image
      workspaces:
        - name: source
          workspace: source
      when: []
      params:
        - name: container-image
          value: build-harbor.alauda.cn/ait/compliance-report-service
        - name: container-image-tag
          value: $(build.git.version.docker)
        - name: dockerfile
          value: ./images/openscap-report-service/Dockerfile
        - name: labels
          value:
            - branch=$(build.git.branch.name)
            - commit=$(build.git.lastCommit.id)
            - component=compliance-report-service
    - name: update-module-plugin
      runAfter:
        - build-content-image
        - build-operator-image
        - build-unified-scanner-image
        - build-content-extractor-image
        - build-report-service-image
        - build-openscap-scanner-image
      workspaces:
        - name: source
          workspace: source
      params:
        - name: build_git_version_shart
          value: $(build.git.version.short)
      taskSpec:
        description: |
          set the version
        workspaces:
          - name: source
            workspace: source
        params:
          - name: build_git_version_shart
            type: string
        steps:
          - name: set-version
            image: build-harbor.alauda.cn/ops/alpine:latest
            imagePullPolicy: IfNotPresent
            workingDir: $(workspaces.source.path)
            script: |
              #!/bin/sh
              sed -i "s|version: .*|version: $(params.build_git_version_shart)|g" ./chart/module-plugin.yaml
              cat ./chart/module-plugin.yaml
            resources:
              requests:
                cpu: 100m
                memory: 100Mi
    - name: chart-build
      runAfter:
        - update-module-plugin
      timeout: 30m
      retries: 1
      taskRef:
        kind: ClusterTask
        name: chart-build
      workspaces:
        - name: source
          workspace: source
      when: []
      params:
        - name: values
          value:
            - global.images.operator.tag=$(build.git.version.docker)
            - global.images.content.tag=$(build.git.version.docker)
            - global.images.scanner.tag=$(build.git.version.docker)
            - global.images.openscapScanner.tag=$(build.git.version.docker)
            - global.images.extractor.tag=$(build.git.version.docker)
            - global.images.report.tag=$(build.git.version.docker)
            - global.chartVersion=$(build.git.version.short)
        - name: annotations
          value:
            - branch=$(build.git.revision.raw)
            - commit=$(build.git.lastCommit.id)
        - name: helm-images
          value:
            - build-harbor.alauda.cn/ait/chart-compliance-operator:$(build.git.version.short)
        - name: dir
          value: ./chart
        - name: update-dependencies
          value: "true"
        - name: version
          value:
            - chart-version=$(build.git.version.short)
    - name: validate-chart
      runAfter:
        - chart-build
      timeout: 15m
      retries: 1
      taskRef:
        kind: ClusterTask
        name: alauda-validate-chart-artifacts
      workspaces:
        - name: source
          workspace: source
      params:
        - name: chart-repo
          value: ait/chart-compliance-operator
        - name: chart-tag
          value: $(build.git.version.short)
    - name: commit-push
      runAfter:
        - validate-chart
      timeout: 15m
      retries: 1
      taskRef:
        kind: ClusterTask
        name: alauda-git-commit-push
      workspaces:
        - name: source
          workspace: source
      when:
        - input: $(build.git.versionPhase)
          operator: in
          values:
            - beta
            - custom
            - ga
      params:
        - name: branch
          value: $(build.git.branch.name)
  finally:
    - name: release-tag
      when:
        - input: $(build.git.versionPhase)
          operator: in
          values:
            - custom
            - ga
        - input: $(tasks.status)
          operator: in
          values:
            - Succeeded
            - Completed
      timeout: 30m
      retries: 1
      taskRef:
        kind: ClusterTask
        name: alauda-release-tag
      workspaces:
        - name: source
          workspace: source
      params:
        - name: verbose
          value: "false"
        - name: version
          value: $(build.git.version.docker)
  workspaces:
    - description: |
        This workspace is shared among all the pipeline tasks to read/write common resources
      name: source
    - description: golang cache
      name: cache
      optional: true
  runTemplate:
    spec:
      taskRunSpecs:
        - pipelineTaskName: build-content-image
          stepOverrides:
            - name: build
              resources:
                requests:
                  cpu: 500m
                  memory: 1000Mi
                limits:
                  cpu: 1000m
                  memory: 2000Mi
        - pipelineTaskName: build-operator-image
          stepOverrides:
            - name: build
              resources:
                requests:
                  cpu: 800m
                  memory: 1500Mi
                limits:
                  cpu: 800m
                  memory: 1500Mi
        - pipelineTaskName: build-unified-scanner-image
          stepOverrides:
            - name: build
              resources:
                requests:
                  cpu: 600m
                  memory: 1200Mi
                limits:
                  cpu: 600m
                  memory: 1200Mi
        - pipelineTaskName: build-openscap-scanner-image
          stepOverrides:
            - name: build
              resources:
                requests:
                  cpu: 700m
                  memory: 1500Mi
                limits:
                  cpu: 700m
                  memory: 1500Mi
        - pipelineTaskName: build-content-extractor-image
          stepOverrides:
            - name: build
              resources:
                requests:
                  cpu: 400m
                  memory: 800Mi
                limits:
                  cpu: 400m
                  memory: 800Mi
        - pipelineTaskName: build-report-service-image
          stepOverrides:
            - name: build
              resources:
                requests:
                  cpu: 600m
                  memory: 1200Mi
                limits:
                  cpu: 600m
                  memory: 1200Mi
