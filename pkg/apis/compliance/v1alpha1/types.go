/*
Copyright 2024 Alauda.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

// +kubebuilder:object:generate=true
// +groupName=compliance-operator.alauda.io
package v1alpha1

import (
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
)

// +genclient
// +k8s:deepcopy-gen:interfaces=k8s.io/apimachinery/pkg/runtime.Object
// +kubebuilder:subresource:status

// ProfileBundle represents a collection of compliance profiles from OpenSCAP content
type ProfileBundle struct {
	metav1.TypeMeta   `json:",inline"`
	metav1.ObjectMeta `json:"metadata,omitempty"`

	Spec   ProfileBundleSpec   `json:"spec,omitempty"`
	Status ProfileBundleStatus `json:"status,omitempty"`
}

// ProfileBundleSpec defines the desired state of ProfileBundle
type ProfileBundleSpec struct {
	// ContentFile is the name of the SCAP data stream file in the content image
	ContentFile string `json:"contentFile"`

	// ContentImage is the container image that contains the SCAP content
	ContentImage string `json:"contentImage"`
}

// ProfileBundleStatus defines the observed state of ProfileBundle
type ProfileBundleStatus struct {
	// Conditions represent the latest available observations of the ProfileBundle's current state
	// +optional
	Conditions []metav1.Condition `json:"conditions,omitempty"`

	// DataStreamStatus indicates the status of the data stream processing
	DataStreamStatus string `json:"dataStreamStatus,omitempty"`

	// Message provides additional information about the status
	Message string `json:"message,omitempty"`

	// ProfileCount is the number of profiles found in the bundle
	ProfileCount int `json:"profileCount,omitempty"`

	// RuleCount is the number of rules found in the bundle
	RuleCount int `json:"ruleCount,omitempty"`
}

// +k8s:deepcopy-gen:interfaces=k8s.io/apimachinery/pkg/runtime.Object

// ProfileBundleList contains a list of ProfileBundle
type ProfileBundleList struct {
	metav1.TypeMeta `json:",inline"`
	metav1.ListMeta `json:"metadata,omitempty"`
	Items           []ProfileBundle `json:"items"`
}

// +genclient
// +k8s:deepcopy-gen:interfaces=k8s.io/apimachinery/pkg/runtime.Object

// Profile represents a compliance profile
type Profile struct {
	metav1.TypeMeta   `json:",inline"`
	metav1.ObjectMeta `json:"metadata,omitempty"`

	Spec ProfileSpec `json:"spec,omitempty"`
}

// ProfileSpec defines the desired state of Profile
type ProfileSpec struct {
	// ID is the XCCDF profile identifier (e.g., xccdf_org.ssgproject.content_profile_stig)
	// +optional
	ID string `json:"id,omitempty"`

	// Title is the human-readable title of the profile
	Title string `json:"title"`

	// Description provides detailed information about the profile
	// +optional
	Description string `json:"description,omitempty"`

	// Version is the profile version (e.g., V2R2)
	// +optional
	Version string `json:"version,omitempty"`

	// Extends indicates if this profile extends another profile
	// +optional
	Extends string `json:"extends,omitempty"`

	// Rules is the list of rules included in this profile
	// +optional
	Rules []RuleReference `json:"rules,omitempty"`

	// Selections allows enabling/disabling specific rules
	// +optional
	Selections []Selection `json:"selections,omitempty"`

	// OpenSCAP specific fields for datastream-based profiles
	// DataStream indicates this profile uses OpenSCAP datastream scanning
	// +optional
	DataStream *DataStreamSpec `json:"dataStream,omitempty"`
}

// DataStreamSpec defines OpenSCAP datastream configuration
type DataStreamSpec struct {
	// ContentFile is the name of the SCAP data stream file (e.g., ssg-ubuntu2204-ds.xml)
	ContentFile string `json:"contentFile"`

	// ContentImage is the container image that contains the SCAP content
	ContentImage string `json:"contentImage"`

	// ProfileID is the XCCDF profile identifier within the datastream
	// (e.g., xccdf_org.ssgproject.content_profile_stig)
	ProfileID string `json:"profileId"`

	// ScanType indicates the type of scan this datastream supports
	// +kubebuilder:validation:Enum=platform;node;both
	// +optional
	ScanType string `json:"scanType,omitempty"`
}

// RuleReference references a compliance rule
type RuleReference struct {
	Name string `json:"name"`
}

// Selection allows enabling/disabling specific rules
type Selection struct {
	ID       string `json:"id"`
	Selected bool   `json:"selected"`
}

// +k8s:deepcopy-gen:interfaces=k8s.io/apimachinery/pkg/runtime.Object

// ProfileList contains a list of Profile
type ProfileList struct {
	metav1.TypeMeta `json:",inline"`
	metav1.ListMeta `json:"metadata,omitempty"`
	Items           []Profile `json:"items"`
}

// +genclient
// +k8s:deepcopy-gen:interfaces=k8s.io/apimachinery/pkg/runtime.Object

// Rule represents a compliance rule
type Rule struct {
	metav1.TypeMeta   `json:",inline"`
	metav1.ObjectMeta `json:"metadata,omitempty"`

	Spec RuleSpec `json:"spec,omitempty"`
}

// RuleSpec defines the desired state of Rule
type RuleSpec struct {
	// Title is the human-readable title of the rule
	Title string `json:"title"`

	// Description provides detailed information about the rule
	Description string `json:"description"`

	// CheckText provides the specific check instructions
	CheckText string `json:"checkText"`

	// FixText provides the remediation instructions
	FixText string `json:"fixText"`

	// ID is the STIG vulnerability ID
	ID string `json:"id,omitempty"`

	// Severity indicates the severity level of the rule
	Severity string `json:"severity"`

	// CheckType indicates whether this is a platform or node check
	CheckType string `json:"checkType"` // "platform" or "node"

	// CheckScript is the script to execute for checking compliance
	CheckScript string `json:"checkScript,omitempty"`

	// Instructions provide manual remediation steps
	Instructions string `json:"instructions,omitempty"`

	// AvailableFixes lists available automated fixes
	AvailableFixes []Fix `json:"availableFixes,omitempty"`

	// STIG information
	STIG STIGInfo `json:"stig,omitempty"`
}

// Fix represents an available automated fix
type Fix struct {
	Platform   string `json:"platform"`
	Disruption string `json:"disruption"`
}

// STIGInfo contains STIG-specific information
type STIGInfo struct {
	ID  string `json:"id,omitempty"`
	Cat string `json:"cat,omitempty"`
}

// +k8s:deepcopy-gen:interfaces=k8s.io/apimachinery/pkg/runtime.Object

// RuleList contains a list of Rule
type RuleList struct {
	metav1.TypeMeta `json:",inline"`
	metav1.ListMeta `json:"metadata,omitempty"`
	Items           []Rule `json:"items"`
}

// +genclient
// +k8s:deepcopy-gen:interfaces=k8s.io/apimachinery/pkg/runtime.Object
// +kubebuilder:subresource:status

// Scan represents a compliance scan
type Scan struct {
	metav1.TypeMeta   `json:",inline"`
	metav1.ObjectMeta `json:"metadata,omitempty"`

	Spec   ScanSpec   `json:"spec,omitempty"`
	Status ScanStatus `json:"status,omitempty"`
}

// ScanSpec defines the desired state of Scan
type ScanSpec struct {
	// Profile is the name of the compliance profile to use for this scan
	Profile string `json:"profile"`

	// ScanType indicates the type of scan to perform (platform, node, or all)
	ScanType string `json:"scanType,omitempty"`

	// NodeSelector is used to select nodes for node scans
	NodeSelector map[string]string `json:"nodeSelector,omitempty"`

	// Schedule defines when to run the scan using cron syntax
	Schedule string `json:"schedule,omitempty"`

	// MaxHistoricalResults defines how many historical scan results to keep
	// +optional
	MaxHistoricalResults int32 `json:"maxHistoricalResults,omitempty"`
}

// ScanStatus defines the observed state of Scan
type ScanStatus struct {
	// Phase is the current phase of the scan: Pending, Running, Done, Error
	Phase string `json:"phase,omitempty"`

	// Result is the overall result of the scan: Compliant, NonCompliant, Error
	Result string `json:"result,omitempty"`

	// Message provides additional information about the scan status
	Message string `json:"message,omitempty"`

	// StartTime is when the scan was started
	StartTime *metav1.Time `json:"startTime,omitempty"`

	// EndTime is when the scan completed
	EndTime *metav1.Time `json:"endTime,omitempty"`

	// LatestResult references the most recent scan result
	// +optional
	LatestResult *HistoricalResultRef `json:"latestResult,omitempty"`

	// HistoricalResults contains references to previous scan results
	// +optional
	HistoricalResults []HistoricalResultRef `json:"historicalResults,omitempty"`
}

// LatestResultRef represents a reference to the latest scan result
type LatestResultRef struct {
	// ScanID is the unique identifier for this scan execution
	ScanID string `json:"scanID"`

	// CheckResultName is the name of the CheckResult resource
	CheckResultName string `json:"checkResultName"`

	// ReportName is the name of the report ConfigMap
	ReportName string `json:"reportName,omitempty"`

	// Timestamp is when the scan was executed
	Timestamp metav1.Time `json:"timestamp"`

	// Summary provides a brief summary of the results
	Summary string `json:"summary"`
}

// HistoricalResultRef references a historical scan result
type HistoricalResultRef struct {
	// ScanID is the unique identifier for this scan execution
	ScanID string `json:"scanID"`

	// Timestamp is when the scan was executed
	Timestamp metav1.Time `json:"timestamp"`

	// CheckResultName is the name of the CheckResult resource
	CheckResultName string `json:"checkResultName"`

	// ReportName is the name of the report ConfigMap
	ReportName string `json:"reportName,omitempty"`

	// Stats provides a summary of the scan results
	Stats ScanStats `json:"stats,omitempty"`
}

// ScanStats provides statistics about a scan
type ScanStats struct {
	// Total is the total number of rules checked
	Total int `json:"total"`

	// Pass is the number of rules that passed
	Pass int `json:"pass"`

	// Fail is the number of rules that failed
	Fail int `json:"fail"`

	// Error is the number of rules that encountered errors
	Error int `json:"error"`

	// Manual is the number of rules requiring manual verification
	Manual int `json:"manual"`

	// NotApplicable is the number of rules that were not applicable
	NotApplicable int `json:"notApplicable"`

	// Inconsistent is the number of rules with inconsistent results
	Inconsistent int `json:"inconsistent"`
}

// +k8s:deepcopy-gen:interfaces=k8s.io/apimachinery/pkg/runtime.Object

// ScanList contains a list of Scan
type ScanList struct {
	metav1.TypeMeta `json:",inline"`
	metav1.ListMeta `json:"metadata,omitempty"`
	Items           []Scan `json:"items"`
}

// Compliance check result status constants
const (
	// CheckResultStatusPass indicates the check passed
	CheckResultStatusPass = "PASS"
	// CheckResultStatusFail indicates the check failed (non-compliant)
	CheckResultStatusFail = "FAIL"
	// CheckResultStatusManual indicates manual review is required
	CheckResultStatusManual = "MANUAL"
	// CheckResultStatusError indicates an error occurred during the check
	CheckResultStatusError = "ERROR"
	// CheckResultStatusInconsistent indicates inconsistent results
	CheckResultStatusInconsistent = "INCONSISTENT"
	// CheckResultStatusNotApplicable indicates the check is not applicable
	CheckResultStatusNotApplicable = "NOT-APPLICABLE"
)

// +genclient
// +k8s:deepcopy-gen:interfaces=k8s.io/apimachinery/pkg/runtime.Object

// CheckResult represents the result of a compliance check
type CheckResult struct {
	metav1.TypeMeta   `json:",inline"`
	metav1.ObjectMeta `json:"metadata,omitempty"`

	Spec CheckResultSpec `json:"spec,omitempty"`
}

// CheckResultSpec defines the result of a compliance check
type CheckResultSpec struct {
	// ScanName is the name of the scan that generated this result
	ScanName string `json:"scanName"`

	// ProfileName is the name of the profile used for the scan
	ProfileName string `json:"profileName"`

	// RuleResults contains the results of individual rule checks
	RuleResults []RuleResult `json:"ruleResults"`

	// Timestamp is when the check was performed
	Timestamp metav1.Time `json:"timestamp"`
}

// RuleResult represents the result of a single rule check
type RuleResult struct {
	// RuleID is the ID of the rule that was checked
	RuleID string `json:"ruleId"`

	// RuleName is the name of the rule
	RuleName string `json:"ruleName"`

	// Severity is the severity of the rule
	Severity string `json:"severity"`

	// CheckType indicates whether this is a platform or node check
	CheckType string `json:"checkType"`

	// Status is the overall result of the rule check
	// +kubebuilder:validation:Enum=PASS;FAIL;MANUAL;ERROR;INCONSISTENT;NOT-APPLICABLE
	Status string `json:"status"`

	// Message provides additional information
	Message string `json:"message,omitempty"`

	// NodeResults contains results for node-specific checks across multiple nodes
	// Only populated for node-type checks
	NodeResults []NodeResult `json:"nodeResults,omitempty"`
}

// NodeResult represents a rule check result on a specific node
type NodeResult struct {
	// NodeName is the node where the check was performed
	NodeName string `json:"nodeName"`

	// Status is the result of the check on this specific node
	// +kubebuilder:validation:Enum=PASS;FAIL;MANUAL;ERROR;INCONSISTENT;NOT-APPLICABLE
	Status string `json:"status"`

	// Message provides additional information
	Message string `json:"message,omitempty"`

	// Evidence contains proof of the check result
	Evidence string `json:"evidence,omitempty"`
}

// +k8s:deepcopy-gen:interfaces=k8s.io/apimachinery/pkg/runtime.Object

// CheckResultList contains a list of CheckResult
type CheckResultList struct {
	metav1.TypeMeta `json:",inline"`
	metav1.ListMeta `json:"metadata,omitempty"`
	Items           []CheckResult `json:"items"`
}

// +genclient
// +k8s:deepcopy-gen:interfaces=k8s.io/apimachinery/pkg/runtime.Object
// +kubebuilder:subresource:status

// ComplianceSuite represents a collection of compliance scans
type ComplianceSuite struct {
	metav1.TypeMeta   `json:",inline"`
	metav1.ObjectMeta `json:"metadata,omitempty"`

	Spec   ComplianceSuiteSpec   `json:"spec,omitempty"`
	Status ComplianceSuiteStatus `json:"status,omitempty"`
}

// ComplianceSuiteSpec defines the desired state of ComplianceSuite
type ComplianceSuiteSpec struct {
	// Scans defines the scans to be executed as part of the suite
	Scans []ComplianceScanSpec `json:"scans"`

	// Schedule defines when to run the compliance suite (cron format)
	// +optional
	Schedule string `json:"schedule,omitempty"`

	// AutoApplyRemediations indicates whether to automatically apply remediations
	// +optional
	AutoApplyRemediations bool `json:"autoApplyRemediations,omitempty"`

	// ScanSettingsBinding allows to bind scan settings to all scans in the suite
	// +optional
	ScanSettingsBinding string `json:"scanSettingsBinding,omitempty"`
}

// ComplianceScanSpec defines a scan within a suite
type ComplianceScanSpec struct {
	// Name of the scan
	Name string `json:"name"`

	// Profile to use for the scan
	Profile string `json:"profile"`

	// ScanType defines the type of scan (node, platform, or both)
	// +kubebuilder:validation:Enum=node;platform;both
	ScanType string `json:"scanType"`

	// NodeSelector selects nodes to scan (for node scans)
	// +optional
	NodeSelector map[string]string `json:"nodeSelector,omitempty"`

	// Schedule for individual scan (overrides suite schedule)
	// +optional
	Schedule string `json:"schedule,omitempty"`
}

// ComplianceSuiteStatus defines the observed state of ComplianceSuite
type ComplianceSuiteStatus struct {
	// Phase indicates the current phase of the compliance suite
	// +kubebuilder:validation:Enum=LAUNCHING;RUNNING;DONE;ERROR
	Phase string `json:"phase,omitempty"`

	// Result indicates the overall result of the compliance suite
	// +kubebuilder:validation:Enum=COMPLIANT;NON-COMPLIANT;INCONSISTENT;ERROR
	Result string `json:"result,omitempty"`

	// Message provides additional information about the current status
	Message string `json:"message,omitempty"`

	// StartTime indicates when the suite started
	// +optional
	StartTime *metav1.Time `json:"startTime,omitempty"`

	// EndTime indicates when the suite completed
	// +optional
	EndTime *metav1.Time `json:"endTime,omitempty"`

	// ScanStatuses contains the status of individual scans
	// +optional
	ScanStatuses []ComplianceScanStatus `json:"scanStatuses,omitempty"`

	// Summary provides aggregated results across all scans
	// +optional
	Summary ComplianceResultSummary `json:"summary,omitempty"`
}

// ComplianceScanStatus represents the status of a scan within a suite
type ComplianceScanStatus struct {
	// Name of the scan
	Name string `json:"name"`

	// Phase of the scan
	Phase string `json:"phase"`

	// Result of the scan
	Result string `json:"result"`

	// Message provides additional information
	Message string `json:"message,omitempty"`

	// StartTime of the scan
	// +optional
	StartTime *metav1.Time `json:"startTime,omitempty"`

	// EndTime of the scan
	// +optional
	EndTime *metav1.Time `json:"endTime,omitempty"`

	// ResultsStorage indicates where the scan results are stored
	// +optional
	ResultsStorage ComplianceResultsStorage `json:"resultsStorage,omitempty"`
}

// ComplianceResultsStorage defines where compliance results are stored
type ComplianceResultsStorage struct {
	// Name of the storage object (e.g., ConfigMap name)
	Name string `json:"name"`

	// Namespace of the storage object
	Namespace string `json:"namespace"`

	// Type of storage (configmap, pvc, etc.)
	// +optional
	Type string `json:"type,omitempty"`
}

// ComplianceResultSummary provides aggregated compliance results
type ComplianceResultSummary struct {
	// Total number of checks
	Total int `json:"total"`

	// Number of passed checks
	Passed int `json:"passed"`

	// Number of failed checks
	Failed int `json:"failed"`

	// Number of checks with errors
	Error int `json:"error"`

	// Number of checks requiring manual review
	Manual int `json:"manual"`

	// Number of inconsistent checks
	Inconsistent int `json:"inconsistent"`

	// Number of not applicable checks
	NotApplicable int `json:"notApplicable"`
}

// +k8s:deepcopy-gen:interfaces=k8s.io/apimachinery/pkg/runtime.Object

// ComplianceSuiteList contains a list of ComplianceSuite
type ComplianceSuiteList struct {
	metav1.TypeMeta `json:",inline"`
	metav1.ListMeta `json:"metadata,omitempty"`
	Items           []ComplianceSuite `json:"items"`
}
