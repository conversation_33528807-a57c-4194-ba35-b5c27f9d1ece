package compliancesuite

import (
	"context"
	"fmt"
	"time"

	ctrl "sigs.k8s.io/controller-runtime"
	"sigs.k8s.io/controller-runtime/pkg/client"

	complianceapi "github.com/alauda/compliance-operator/pkg/apis/compliance/v1alpha1"
)

func (r *ComplianceSuiteReconciler) checkSuiteProgress(ctx context.Context, suite *complianceapi.ComplianceSuite) (ctrl.Result, error) {
	r.Log.Info("Checking suite progress", "suite", suite.Name)

	// Get all scans for this suite
	var scans complianceapi.ScanList
	if err := r.List(ctx, &scans, client.InNamespace(suite.Namespace),
		client.MatchingLabels{"compliance-operator.alauda.io/suite": suite.Name}); err != nil {
		return ctrl.Result{}, err
	}

	// Update scan statuses
	if err := r.updateScanStatuses(ctx, suite, scans.Items); err != nil {
		return ctrl.Result{}, err
	}

	// Check if all scans are complete
	totalScans := len(scans.Items)
	completedScans := 0
	errorScans := 0

	for _, scan := range scans.Items {
		if scan.Status.Phase == "Done" {
			completedScans++
		} else if scan.Status.Phase == "Error" {
			errorScans++
		}
	}

	r.Log.Info("Suite progress", "suite", suite.Name, "total", totalScans, "completed", completedScans, "errors", errorScans)

	// Check if all scans are complete
	if completedScans+errorScans == totalScans {
		// Aggregate results
		aggregatedResult, err := r.aggregateSuiteResults(ctx, suite)
		if err != nil {
			return ctrl.Result{}, err
		}

		return r.updateSuiteStatusWithEndTime(ctx, suite, "DONE", aggregatedResult.Result, aggregatedResult.Message)
	}

	// Requeue to check again
	return ctrl.Result{RequeueAfter: 30 * time.Second}, nil
}

func (r *ComplianceSuiteReconciler) updateScanStatuses(ctx context.Context, suite *complianceapi.ComplianceSuite, scans []complianceapi.Scan) error {
	// Update scan statuses in suite status
	scanStatusMap := make(map[string]*complianceapi.ComplianceScanStatus)
	for i := range suite.Status.ScanStatuses {
		scanStatusMap[suite.Status.ScanStatuses[i].Name] = &suite.Status.ScanStatuses[i]
	}

	for _, scan := range scans {
		if status, exists := scanStatusMap[scan.Name]; exists {
			status.Phase = scan.Status.Phase
			status.Result = scan.Status.Result
			status.Message = scan.Status.Message
			status.StartTime = scan.Status.StartTime
			status.EndTime = scan.Status.EndTime
		}
	}

	return nil
}

// SuiteAggregatedResult represents the aggregated suite result
type SuiteAggregatedResult struct {
	Result  string
	Message string
	Summary complianceapi.ComplianceResultSummary
}

func (r *ComplianceSuiteReconciler) aggregateSuiteResults(ctx context.Context, suite *complianceapi.ComplianceSuite) (*SuiteAggregatedResult, error) {
	// Get all CheckResults for this suite
	var checkResults complianceapi.CheckResultList
	if err := r.List(ctx, &checkResults, client.InNamespace(suite.Namespace),
		client.MatchingLabels{"compliance-operator.alauda.io/suite": suite.Name}); err != nil {
		return nil, err
	}

	summary := complianceapi.ComplianceResultSummary{
		Total: 0, // 将在后面计算总规则数
	}

	// Count results by status
	for _, result := range checkResults.Items {
		// 计算每个 CheckResult 中的规则结果
		for _, ruleResult := range result.Spec.RuleResults {
			summary.Total++ // 增加总规则数

			switch ruleResult.Status {
			case "PASS":
				summary.Passed++
			case "FAIL":
				summary.Failed++
			case "ERROR":
				summary.Error++
			case "MANUAL":
				summary.Manual++
			case "INCONSISTENT":
				summary.Inconsistent++
			case "NOT-APPLICABLE":
				summary.NotApplicable++
			}
		}
	}

	// Determine overall result
	result := "COMPLIANT"
	message := fmt.Sprintf("Suite completed: %d total checks", summary.Total)

	if summary.Error > 0 {
		result = "ERROR"
		message = fmt.Sprintf("Suite completed with errors: %d passed, %d failed, %d errors",
			summary.Passed, summary.Failed, summary.Error)
	} else if summary.Failed > 0 {
		result = "NON-COMPLIANT"
		message = fmt.Sprintf("Suite completed: %d passed, %d failed", summary.Passed, summary.Failed)
	} else if summary.Manual > 0 {
		result = "MANUAL"
		message = fmt.Sprintf("Suite completed: %d passed, %d require manual review",
			summary.Passed, summary.Manual)
	} else {
		message = fmt.Sprintf("Suite completed: %d checks passed", summary.Passed)
	}

	// Update suite status with summary
	suite.Status.Summary = summary

	return &SuiteAggregatedResult{
		Result:  result,
		Message: message,
		Summary: summary,
	}, nil
}
