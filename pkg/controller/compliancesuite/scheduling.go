package compliancesuite

import (
	"context"
	"time"

	"github.com/robfig/cron/v3"
	"k8s.io/apimachinery/pkg/api/errors"
	"k8s.io/apimachinery/pkg/types"
	ctrl "sigs.k8s.io/controller-runtime"
	"sigs.k8s.io/controller-runtime/pkg/client"
	"sigs.k8s.io/controller-runtime/pkg/controller/controllerutil"

	complianceapi "github.com/alauda/compliance-operator/pkg/apis/compliance/v1alpha1"
)

func (r *ComplianceSuiteReconciler) scheduleNextSuite(ctx context.Context, suite *complianceapi.ComplianceSuite) (ctrl.Result, error) {
	if suite.Spec.Schedule == "" {
		return ctrl.Result{}, nil
	}

	// Parse the cron schedule
	schedule, err := cron.ParseStandard(suite.Spec.Schedule)
	if err != nil {
		r.Log.Error(err, "Invalid cron schedule", "suite", suite.Name, "schedule", suite.Spec.Schedule)
		return ctrl.Result{}, err
	}

	// Calculate next run time
	now := time.Now()
	nextRun := schedule.Next(now)
	duration := nextRun.Sub(now)

	r.Log.Info("Scheduling next suite run", "suite", suite.Name, "nextRun", nextRun, "duration", duration)

	// Reset suite for next run
	if err := r.resetSuiteForNextRun(ctx, suite); err != nil {
		return ctrl.Result{}, err
	}

	// Requeue for the next scheduled time
	return ctrl.Result{RequeueAfter: duration}, nil
}

func (r *ComplianceSuiteReconciler) resetSuiteForNextRun(ctx context.Context, suite *complianceapi.ComplianceSuite) error {
	// Clean up previous scan results if configured
	// Note: ResultsStorage is not implemented in current types, skip for now
	// if suite.Spec.ResultsStorage != nil && suite.Spec.ResultsStorage.Rotation.KeepLast > 0 {
	//     if err := r.cleanupOldResults(ctx, suite); err != nil {
	//         r.Log.Error(err, "Failed to cleanup old results", "suite", suite.Name)
	//         // Don't fail the reset, just log the error
	//     }
	// }

	// Reset suite status for next run
	suite.Status.Phase = "LAUNCHING"
	suite.Status.Result = ""
	suite.Status.Message = "Scheduled suite run starting"
	suite.Status.StartTime = nil
	suite.Status.EndTime = nil
	suite.Status.ScanStatuses = nil

	return nil
}

func (r *ComplianceSuiteReconciler) cleanupOldResults(ctx context.Context, suite *complianceapi.ComplianceSuite) error {
	// This is a simplified cleanup - in a real implementation, you would:
	// 1. List all historical results
	// 2. Sort by timestamp
	// 3. Keep only the configured number of recent results
	// 4. Delete older results

	r.Log.Info("Cleaning up old results", "suite", suite.Name)

	// For now, just clean up old CheckResults older than 7 days (default retention)
	cutoffTime := time.Now().Add(-7 * 24 * time.Hour)

	// Get all CheckResults for this suite
	var checkResults complianceapi.CheckResultList
	if err := r.List(ctx, &checkResults, client.InNamespace(suite.Namespace),
		client.MatchingLabels{"compliance-operator.alauda.io/suite": suite.Name}); err != nil {
		return err
	}

	// Delete results older than cutoff time
	for _, result := range checkResults.Items {
		if result.Spec.Timestamp.Time.Before(cutoffTime) {
			if err := r.Delete(ctx, &result); err != nil {
				r.Log.Error(err, "Failed to delete old CheckResult", "result", result.Name)
			} else {
				r.Log.Info("Deleted old CheckResult", "result", result.Name, "timestamp", result.Spec.Timestamp.Time)
			}
		}
	}

	return nil
}

func (r *ComplianceSuiteReconciler) handleDeletion(ctx context.Context, suite *complianceapi.ComplianceSuite) (ctrl.Result, error) {
	// Clean up associated scans and check results
	if err := r.cleanupSuiteScans(ctx, suite); err != nil {
		return ctrl.Result{}, err
	}

	if err := r.cleanupSuiteCheckResults(ctx, suite); err != nil {
		return ctrl.Result{}, err
	}

	// Remove finalizer with retry logic
	for i := 0; i < 3; i++ {
		// Get the latest version of the suite object
		var latestSuite complianceapi.ComplianceSuite
		if err := r.Get(ctx, types.NamespacedName{Name: suite.Name, Namespace: suite.Namespace}, &latestSuite); err != nil {
			if errors.IsNotFound(err) {
				// Object already deleted, nothing to do
				return ctrl.Result{}, nil
			}
			r.Log.Error(err, "Failed to get latest ComplianceSuite object for deletion")
			return ctrl.Result{}, err
		}

		// Remove finalizer from the latest version
		controllerutil.RemoveFinalizer(&latestSuite, "compliance-operator.alauda.io/suite")

		// Try to update
		if err := r.Update(ctx, &latestSuite); err != nil {
			if errors.IsConflict(err) {
				r.Log.Info("Conflict removing finalizer, retrying", "suite", suite.Name, "attempt", i+1)
				continue // Retry
			}
			r.Log.Error(err, "Failed to remove finalizer")
			return ctrl.Result{}, err
		}

		// Success
		r.Log.Info("Successfully removed finalizer", "suite", suite.Name)
		return ctrl.Result{}, nil
	}

	// All retries failed
	r.Log.Error(nil, "Failed to remove finalizer after retries", "suite", suite.Name)
	return ctrl.Result{RequeueAfter: 5 * time.Second}, nil
}

func (r *ComplianceSuiteReconciler) cleanupSuiteScans(ctx context.Context, suite *complianceapi.ComplianceSuite) error {
	var scans complianceapi.ScanList
	if err := r.List(ctx, &scans, client.InNamespace(suite.Namespace),
		client.MatchingLabels{"compliance-operator.alauda.io/suite": suite.Name}); err != nil {
		return err
	}

	for _, scan := range scans.Items {
		if err := r.Delete(ctx, &scan); err != nil {
			return err
		}
		r.Log.Info("Deleted suite scan", "suite", suite.Name, "scan", scan.Name)
	}

	return nil
}

func (r *ComplianceSuiteReconciler) cleanupSuiteCheckResults(ctx context.Context, suite *complianceapi.ComplianceSuite) error {
	var results complianceapi.CheckResultList
	if err := r.List(ctx, &results, client.InNamespace(suite.Namespace),
		client.MatchingLabels{"compliance-operator.alauda.io/suite": suite.Name}); err != nil {
		return err
	}

	for _, result := range results.Items {
		if err := r.Delete(ctx, &result); err != nil {
			return err
		}
		r.Log.Info("Deleted suite check result", "suite", suite.Name, "result", result.Name)
	}

	return nil
}
