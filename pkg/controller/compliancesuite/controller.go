package compliancesuite

import (
	"context"
	"time"

	"github.com/go-logr/logr"
	"k8s.io/apimachinery/pkg/api/errors"
	"k8s.io/apimachinery/pkg/runtime"
	ctrl "sigs.k8s.io/controller-runtime"
	"sigs.k8s.io/controller-runtime/pkg/client"
	"sigs.k8s.io/controller-runtime/pkg/controller/controllerutil"

	complianceapi "github.com/alauda/compliance-operator/pkg/apis/compliance/v1alpha1"
)

// ComplianceSuiteReconciler reconciles a ComplianceSuite object
type ComplianceSuiteReconciler struct {
	client.Client
	Log    logr.Logger
	Scheme *runtime.Scheme
}

// +kubebuilder:rbac:groups=compliance-operator.alauda.io,resources=compliancesuites,verbs=get;list;watch;create;update;patch;delete
// +kubebuilder:rbac:groups=compliance-operator.alauda.io,resources=compliancesuites/status,verbs=get;update;patch
// +kubebuilder:rbac:groups=compliance-operator.alauda.io,resources=scans,verbs=get;list;watch;create;update;patch;delete
// +kubebuilder:rbac:groups=compliance-operator.alauda.io,resources=checkresults,verbs=get;list;watch

// Reconcile handles ComplianceSuite reconciliation
func (r *ComplianceSuiteReconciler) Reconcile(ctx context.Context, req ctrl.Request) (ctrl.Result, error) {
	log := r.Log.WithValues("compliancesuite", req.NamespacedName)

	// Fetch the ComplianceSuite instance
	var suite complianceapi.ComplianceSuite
	if err := r.Get(ctx, req.NamespacedName, &suite); err != nil {
		if errors.IsNotFound(err) {
			log.Info("ComplianceSuite resource not found. Ignoring since object must be deleted")
			return ctrl.Result{}, nil
		}
		log.Error(err, "Failed to get ComplianceSuite")
		return ctrl.Result{}, err
	}

	// Check if the ComplianceSuite is being deleted
	if suite.GetDeletionTimestamp() != nil {
		return r.handleDeletion(ctx, &suite)
	}

	// Add finalizer if not present
	if !controllerutil.ContainsFinalizer(&suite, "compliance-operator.alauda.io/suite") {
		controllerutil.AddFinalizer(&suite, "compliance-operator.alauda.io/suite")
		return ctrl.Result{}, r.Update(ctx, &suite)
	}

	// Handle different suite phases
	switch suite.Status.Phase {
	case "", "LAUNCHING":
		return r.initializeSuite(ctx, &suite)
	case "RUNNING":
		return r.checkSuiteProgress(ctx, &suite)
	case "DONE":
		// Suite is complete, check if we need to reschedule
		if suite.Spec.Schedule != "" {
			return r.scheduleNextSuite(ctx, &suite)
		}
		return ctrl.Result{}, nil
	case "ERROR":
		// Handle error state - could retry or wait for manual intervention
		return ctrl.Result{RequeueAfter: 5 * time.Minute}, nil
	default:
		return ctrl.Result{}, nil
	}
}

// SetupWithManager sets up the controller with the Manager.
func (r *ComplianceSuiteReconciler) SetupWithManager(mgr ctrl.Manager) error {
	return ctrl.NewControllerManagedBy(mgr).
		For(&complianceapi.ComplianceSuite{}).
		Owns(&complianceapi.Scan{}).
		Complete(r)
}
