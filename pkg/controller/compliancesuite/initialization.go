package compliancesuite

import (
	"context"
	"fmt"

	"github.com/robfig/cron/v3"
	"k8s.io/apimachinery/pkg/api/errors"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/types"
	ctrl "sigs.k8s.io/controller-runtime"
	"sigs.k8s.io/controller-runtime/pkg/controller/controllerutil"

	complianceapi "github.com/alauda/compliance-operator/pkg/apis/compliance/v1alpha1"
)

func (r *ComplianceSuiteReconciler) initializeSuite(ctx context.Context, suite *complianceapi.ComplianceSuite) (ctrl.Result, error) {
	r.Log.Info("Initializing compliance suite", "suite", suite.Name)

	// Validate suite configuration
	if err := r.validateSuite(suite); err != nil {
		return r.updateSuiteStatus(ctx, suite, "ERROR", "ERROR", fmt.Sprintf("Suite validation failed: %v", err))
	}

	// Create individual scans
	if err := r.createScans(ctx, suite); err != nil {
		return r.updateSuiteStatus(ctx, suite, "ERROR", "ERROR", fmt.Sprintf("Failed to create scans: %v", err))
	}

	// Initialize scan statuses
	r.initializeScanStatuses(suite)

	// Update status to RUNNING
	return r.updateSuiteStatusWithStartTime(ctx, suite, "RUNNING", "", "Suite scans created and running")
}

func (r *ComplianceSuiteReconciler) validateSuite(suite *complianceapi.ComplianceSuite) error {
	if len(suite.Spec.Scans) == 0 {
		return fmt.Errorf("no scans defined in suite")
	}

	// Validate each scan specification
	for i, scan := range suite.Spec.Scans {
		if scan.Name == "" {
			return fmt.Errorf("scan %d: name is required", i)
		}
		if scan.Profile == "" {
			return fmt.Errorf("scan %s: profile is required", scan.Name)
		}
		if scan.ScanType == "" {
			return fmt.Errorf("scan %s: scanType is required", scan.Name)
		}
		if scan.ScanType != "node" && scan.ScanType != "platform" && scan.ScanType != "both" {
			return fmt.Errorf("scan %s: invalid scanType %s", scan.Name, scan.ScanType)
		}
	}

	// Validate cron schedule if provided
	if suite.Spec.Schedule != "" {
		if _, err := cron.ParseStandard(suite.Spec.Schedule); err != nil {
			return fmt.Errorf("invalid cron schedule: %v", err)
		}
	}

	return nil
}

func (r *ComplianceSuiteReconciler) createScans(ctx context.Context, suite *complianceapi.ComplianceSuite) error {
	for _, scanSpec := range suite.Spec.Scans {
		// Create Scan object
		scan := &complianceapi.Scan{
			ObjectMeta: metav1.ObjectMeta{
				Name:      fmt.Sprintf("%s-%s", suite.Name, scanSpec.Name),
				Namespace: suite.Namespace,
				Labels: map[string]string{
					"compliance-operator.alauda.io/suite":      suite.Name,
					"compliance-operator.alauda.io/suite-scan": scanSpec.Name,
				},
			},
			Spec: complianceapi.ScanSpec{
				Profile:      scanSpec.Profile,
				ScanType:     scanSpec.ScanType,
				NodeSelector: scanSpec.NodeSelector,
				Schedule:     scanSpec.Schedule, // Individual scan schedule overrides suite schedule
			},
		}

		// If no individual schedule, use suite schedule
		if scan.Spec.Schedule == "" {
			scan.Spec.Schedule = suite.Spec.Schedule
		}

		// Set owner reference
		if err := controllerutil.SetControllerReference(suite, scan, r.Scheme); err != nil {
			return err
		}

		// Check if scan already exists
		var existingScan complianceapi.Scan
		err := r.Get(ctx, types.NamespacedName{Name: scan.Name, Namespace: scan.Namespace}, &existingScan)
		if err != nil {
			if errors.IsNotFound(err) {
				// Create new scan
				if err := r.Create(ctx, scan); err != nil {
					return fmt.Errorf("failed to create scan %s: %v", scan.Name, err)
				}
				r.Log.Info("Created scan", "suite", suite.Name, "scan", scan.Name)
			} else {
				return err
			}
		} else {
			// Update existing scan if needed
			existingScan.Spec = scan.Spec
			if err := r.Update(ctx, &existingScan); err != nil {
				return fmt.Errorf("failed to update scan %s: %v", scan.Name, err)
			}
			r.Log.Info("Updated existing scan", "suite", suite.Name, "scan", scan.Name)
		}
	}

	return nil
}

func (r *ComplianceSuiteReconciler) initializeScanStatuses(suite *complianceapi.ComplianceSuite) {
	suite.Status.ScanStatuses = make([]complianceapi.ComplianceScanStatus, len(suite.Spec.Scans))
	for i, scanSpec := range suite.Spec.Scans {
		suite.Status.ScanStatuses[i] = complianceapi.ComplianceScanStatus{
			Name:   fmt.Sprintf("%s-%s", suite.Name, scanSpec.Name),
			Phase:  "Pending",
			Result: "",
		}
	}
}
