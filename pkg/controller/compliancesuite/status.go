package compliancesuite

import (
	"context"

	"k8s.io/apimachinery/pkg/api/errors"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/types"
	"k8s.io/client-go/util/retry"
	ctrl "sigs.k8s.io/controller-runtime"

	complianceapi "github.com/alauda/compliance-operator/pkg/apis/compliance/v1alpha1"
)

func (r *ComplianceSuiteReconciler) updateSuiteStatus(ctx context.Context, suite *complianceapi.ComplianceSuite, phase, result, message string) (ctrl.Result, error) {
	err := retry.RetryOnConflict(retry.DefaultRetry, func() error {
		// Get the latest version of the suite object
		var latestSuite complianceapi.ComplianceSuite
		if err := r.Get(ctx, types.NamespacedName{Name: suite.Name, Namespace: suite.Namespace}, &latestSuite); err != nil {
			if errors.IsNotFound(err) {
				// Object was deleted, nothing to update
				r.Log.Info("ComplianceSuite object not found, likely deleted", "suite", suite.Name)
				return nil
			}
			return err
		}

		// Update the status on the latest version
		latestSuite.Status.Phase = phase
		latestSuite.Status.Result = result
		latestSuite.Status.Message = message

		// Try to update - RetryOnConflict will handle conflict errors automatically
		return r.Status().Update(ctx, &latestSuite)
	})

	if err != nil {
		if errors.IsNotFound(err) {
			// Object was deleted during update, nothing to update
			r.Log.Info("ComplianceSuite object was deleted during status update", "suite", suite.Name)
			return ctrl.Result{}, nil
		}
		r.Log.Error(err, "Failed to update ComplianceSuite status", "suite", suite.Name)
		return ctrl.Result{}, err
	}

	// Success
	r.Log.Info("Successfully updated ComplianceSuite status", "suite", suite.Name, "phase", phase, "result", result)
	return ctrl.Result{}, nil
}

func (r *ComplianceSuiteReconciler) updateSuiteStatusWithStartTime(ctx context.Context, suite *complianceapi.ComplianceSuite, phase, result, message string) (ctrl.Result, error) {
	err := retry.RetryOnConflict(retry.DefaultRetry, func() error {
		// Get the latest version of the suite object
		var latestSuite complianceapi.ComplianceSuite
		if err := r.Get(ctx, types.NamespacedName{Name: suite.Name, Namespace: suite.Namespace}, &latestSuite); err != nil {
			if errors.IsNotFound(err) {
				// Object was deleted, nothing to update
				r.Log.Info("ComplianceSuite object not found, likely deleted", "suite", suite.Name)
				return nil
			}
			return err
		}

		// Update the status on the latest version
		latestSuite.Status.Phase = phase
		latestSuite.Status.Result = result
		latestSuite.Status.Message = message

		// Set start time if not already set
		if latestSuite.Status.StartTime == nil {
			now := metav1.Now()
			latestSuite.Status.StartTime = &now
		}

		// Try to update - RetryOnConflict will handle conflict errors automatically
		return r.Status().Update(ctx, &latestSuite)
	})

	if err != nil {
		if errors.IsNotFound(err) {
			// Object was deleted during update, nothing to update
			r.Log.Info("ComplianceSuite object was deleted during status update with start time", "suite", suite.Name)
			return ctrl.Result{}, nil
		}
		r.Log.Error(err, "Failed to update ComplianceSuite status with start time", "suite", suite.Name)
		return ctrl.Result{}, err
	}

	// Success
	r.Log.Info("Successfully updated ComplianceSuite status with start time", "suite", suite.Name, "phase", phase, "result", result)
	return ctrl.Result{}, nil
}

func (r *ComplianceSuiteReconciler) updateSuiteStatusWithEndTime(ctx context.Context, suite *complianceapi.ComplianceSuite, phase, result, message string) (ctrl.Result, error) {
	err := retry.RetryOnConflict(retry.DefaultRetry, func() error {
		// Get the latest version of the suite object
		var latestSuite complianceapi.ComplianceSuite
		if err := r.Get(ctx, types.NamespacedName{Name: suite.Name, Namespace: suite.Namespace}, &latestSuite); err != nil {
			if errors.IsNotFound(err) {
				// Object was deleted, nothing to update
				r.Log.Info("ComplianceSuite object not found, likely deleted", "suite", suite.Name)
				return nil
			}
			return err
		}

		// Update the status on the latest version
		latestSuite.Status.Phase = phase
		latestSuite.Status.Result = result
		latestSuite.Status.Message = message

		// Set end time
		now := metav1.Now()
		latestSuite.Status.EndTime = &now

		// Try to update - RetryOnConflict will handle conflict errors automatically
		return r.Status().Update(ctx, &latestSuite)
	})

	if err != nil {
		if errors.IsNotFound(err) {
			// Object was deleted during update, nothing to update
			r.Log.Info("ComplianceSuite object was deleted during status update with end time", "suite", suite.Name)
			return ctrl.Result{}, nil
		}
		r.Log.Error(err, "Failed to update ComplianceSuite status with end time", "suite", suite.Name)
		return ctrl.Result{}, err
	}

	// Success
	r.Log.Info("Successfully updated ComplianceSuite status with end time", "suite", suite.Name, "phase", phase, "result", result)
	return ctrl.Result{}, nil
}
