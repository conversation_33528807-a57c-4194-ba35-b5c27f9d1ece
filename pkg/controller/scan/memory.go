package scan

import (
	"context"
	"runtime"
	"sync"
	"sync/atomic"
	"time"

	"github.com/go-logr/logr"
)

// MemoryManager handles memory monitoring and optimization
type MemoryManager struct {
	config          *ScanControllerConfig
	log             logr.Logger
	currentMemoryMB int64
	peakMemoryMB    int64
	gcCount         int64
	lastGCTime      time.Time
	mu              sync.RWMutex

	// Concurrency control
	activeScanCount int64
	maxConcurrency  int64
	throttleActive  int64 // atomic bool (0/1)
}

// NewMemoryManager creates a new memory manager
func NewMemoryManager(config *ScanControllerConfig, log logr.Logger) *MemoryManager {
	mm := &MemoryManager{
		config:         config,
		log:            log,
		maxConcurrency: int64(config.MaxConcurrentScans),
		lastGCTime:     time.Now(),
	}

	// Start memory monitoring if enabled
	if config.MemoryOptimizationEnabled {
		go mm.startMemoryMonitoring()
	}

	return mm
}

// startMemoryMonitoring runs a background goroutine to monitor memory usage
func (mm *MemoryManager) startMemoryMonitoring() {
	ticker := time.NewTicker(30 * time.Second) // Monitor every 30 seconds
	defer ticker.Stop()

	for range ticker.C {
		mm.updateMemoryStats()
		mm.checkMemoryThresholds()
	}
}

// updateMemoryStats updates current memory usage statistics
func (mm *MemoryManager) updateMemoryStats() {
	var m runtime.MemStats
	runtime.ReadMemStats(&m)

	currentMB := int64(m.Alloc / 1024 / 1024)

	mm.mu.Lock()
	mm.currentMemoryMB = currentMB
	if currentMB > mm.peakMemoryMB {
		mm.peakMemoryMB = currentMB
	}
	mm.mu.Unlock()

	// Log memory stats periodically
	mm.log.V(1).Info("Memory stats updated",
		"currentMB", currentMB,
		"peakMB", mm.peakMemoryMB,
		"activeScanCount", atomic.LoadInt64(&mm.activeScanCount),
		"throttleActive", atomic.LoadInt64(&mm.throttleActive) == 1)
}

// checkMemoryThresholds checks if memory usage exceeds thresholds
func (mm *MemoryManager) checkMemoryThresholds() {
	mm.mu.RLock()
	currentMB := mm.currentMemoryMB
	mm.mu.RUnlock()

	// Check if we need to trigger GC
	if currentMB > mm.config.GCThreshold {
		timeSinceLastGC := time.Since(mm.lastGCTime)
		if timeSinceLastGC > 5*time.Minute { // Don't GC too frequently
			mm.log.Info("Memory threshold exceeded, triggering GC",
				"currentMB", currentMB,
				"threshold", mm.config.GCThreshold)

			runtime.GC()
			atomic.AddInt64(&mm.gcCount, 1)
			mm.lastGCTime = time.Now()
		}
	}

	// Check if we need to throttle
	if currentMB > mm.config.MaxMemoryUsage {
		if atomic.CompareAndSwapInt64(&mm.throttleActive, 0, 1) {
			mm.log.Error(nil, "Memory usage critical, enabling throttling",
				"currentMB", currentMB,
				"maxMB", mm.config.MaxMemoryUsage)

			// Reduce max concurrency temporarily
			newMax := mm.maxConcurrency / 2
			if newMax < 1 {
				newMax = 1
			}
			atomic.StoreInt64(&mm.maxConcurrency, newMax)
		}
	} else if currentMB < mm.config.MaxMemoryUsage*3/4 { // 75% of max
		if atomic.CompareAndSwapInt64(&mm.throttleActive, 1, 0) {
			mm.log.Info("Memory usage normalized, disabling throttling",
				"currentMB", currentMB)

			// Restore original max concurrency
			atomic.StoreInt64(&mm.maxConcurrency, int64(mm.config.MaxConcurrentScans))
		}
	}
}

// CanStartScan checks if a new scan can be started based on memory and concurrency limits
func (mm *MemoryManager) CanStartScan() bool {
	if !mm.config.ConcurrencyControlEnabled {
		return true // No limits if disabled
	}

	currentCount := atomic.LoadInt64(&mm.activeScanCount)
	maxAllowed := atomic.LoadInt64(&mm.maxConcurrency)

	return currentCount < maxAllowed
}

// StartScan registers the start of a new scan
func (mm *MemoryManager) StartScan(scanName string) {
	count := atomic.AddInt64(&mm.activeScanCount, 1)
	mm.log.V(1).Info("Scan started",
		"scan", scanName,
		"activeScanCount", count,
		"maxConcurrency", atomic.LoadInt64(&mm.maxConcurrency))
}

// EndScan registers the completion of a scan
func (mm *MemoryManager) EndScan(scanName string) {
	count := atomic.AddInt64(&mm.activeScanCount, -1)
	if count < 0 {
		atomic.StoreInt64(&mm.activeScanCount, 0) // Safety check
		count = 0
	}

	mm.log.V(1).Info("Scan completed",
		"scan", scanName,
		"activeScanCount", count)
}

// GetMemoryStats returns current memory statistics
func (mm *MemoryManager) GetMemoryStats() MemoryStats {
	mm.mu.RLock()
	defer mm.mu.RUnlock()

	return MemoryStats{
		JobPoolSize:         0, // Would need custom pool implementation to track
		ConfigMapPoolSize:   0,
		CheckResultPoolSize: 0,
		StringSlicePoolSize: 0,
		MapPoolSize:         0,
	}
}

// GetRuntimeStats returns runtime memory and concurrency statistics
type RuntimeStats struct {
	CurrentMemoryMB int64
	PeakMemoryMB    int64
	GCCount         int64
	ActiveScanCount int64
	MaxConcurrency  int64
	ThrottleActive  bool
}

// GetRuntimeStats returns current runtime statistics
func (mm *MemoryManager) GetRuntimeStats() RuntimeStats {
	mm.mu.RLock()
	defer mm.mu.RUnlock()

	return RuntimeStats{
		CurrentMemoryMB: mm.currentMemoryMB,
		PeakMemoryMB:    mm.peakMemoryMB,
		GCCount:         atomic.LoadInt64(&mm.gcCount),
		ActiveScanCount: atomic.LoadInt64(&mm.activeScanCount),
		MaxConcurrency:  atomic.LoadInt64(&mm.maxConcurrency),
		ThrottleActive:  atomic.LoadInt64(&mm.throttleActive) == 1,
	}
}

// WaitForCapacity waits until there's capacity to start a new scan
func (mm *MemoryManager) WaitForCapacity(ctx context.Context, timeout time.Duration) error {
	if !mm.config.ConcurrencyControlEnabled {
		return nil // No waiting if disabled
	}

	ctx, cancel := context.WithTimeout(ctx, timeout)
	defer cancel()

	ticker := time.NewTicker(1 * time.Second)
	defer ticker.Stop()

	for {
		if mm.CanStartScan() {
			return nil
		}

		select {
		case <-ctx.Done():
			return ctx.Err()
		case <-ticker.C:
			// Continue checking
		}
	}
}

// ForceGC manually triggers garbage collection if conditions are met
func (mm *MemoryManager) ForceGC() {
	timeSinceLastGC := time.Since(mm.lastGCTime)
	if timeSinceLastGC > 1*time.Minute { // Don't GC too frequently
		mm.log.Info("Manual GC triggered")
		runtime.GC()
		atomic.AddInt64(&mm.gcCount, 1)
		mm.lastGCTime = time.Now()
	}
}

// StreamProcessor handles large data processing with memory-efficient streaming
type StreamProcessor struct {
	batchSize int
	processor func(batch []interface{}) error
}

// NewStreamProcessor creates a new stream processor for large datasets
func NewStreamProcessor(batchSize int, processor func(batch []interface{}) error) *StreamProcessor {
	return &StreamProcessor{
		batchSize: batchSize,
		processor: processor,
	}
}

// Process processes a large dataset in batches to avoid memory issues
func (sp *StreamProcessor) Process(data []interface{}) error {
	for i := 0; i < len(data); i += sp.batchSize {
		end := i + sp.batchSize
		if end > len(data) {
			end = len(data)
		}

		batch := data[i:end]
		if err := sp.processor(batch); err != nil {
			return err
		}

		// Allow GC to clean up processed batch
		runtime.GC()
	}

	return nil
}
