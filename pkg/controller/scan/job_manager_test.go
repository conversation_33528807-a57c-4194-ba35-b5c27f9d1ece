package scan

import (
	"context"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	batchv1 "k8s.io/api/batch/v1"
	corev1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/runtime"
	"sigs.k8s.io/controller-runtime/pkg/client/fake"
	"sigs.k8s.io/controller-runtime/pkg/log/zap"
)

func TestJobManager_CanCreateJob(t *testing.T) {
	config := &ScanControllerConfig{
		MaxConcurrentScans: 2,
	}

	client := fake.NewClientBuilder().Build()
	log := zap.New(zap.UseDevMode(true))

	jm := NewJobManager(client, config, nil, log)

	// Initially should be able to create jobs
	assert.True(t, jm.CanCreateJob())

	// Add jobs to reach limit
	jm.activeJobs["scan1"] = &JobInfo{ScanName: "scan1"}
	jm.activeJobs["scan2"] = &JobInfo{ScanName: "scan2"}

	// Should not be able to create more jobs
	assert.False(t, jm.CanCreateJob())
}

func TestJobManager_CreateJobWithConcurrencyControl(t *testing.T) {
	scheme := runtime.NewScheme()
	require.NoError(t, batchv1.AddToScheme(scheme))
	require.NoError(t, corev1.AddToScheme(scheme))

	config := &ScanControllerConfig{
		MaxConcurrentScans: 1,
	}

	client := fake.NewClientBuilder().WithScheme(scheme).Build()
	log := zap.New(zap.UseDevMode(true))

	jm := NewJobManager(client, config, nil, log)
	ctx := context.Background()

	// Create first job - should succeed immediately
	job1 := &batchv1.Job{
		ObjectMeta: metav1.ObjectMeta{
			Name:      "test-job-1",
			Namespace: "default",
		},
		Spec: batchv1.JobSpec{
			Template: corev1.PodTemplateSpec{
				Spec: corev1.PodSpec{
					Containers: []corev1.Container{
						{
							Name:  "test",
							Image: "test:latest",
						},
					},
					RestartPolicy: corev1.RestartPolicyNever,
				},
			},
		},
	}

	err := jm.CreateJobWithConcurrencyControl(ctx, "scan1", job1, nil, 1)
	assert.NoError(t, err)
	assert.Equal(t, 1, jm.GetActiveJobCount())
	assert.Equal(t, 0, jm.GetQueueLength())

	// Create second job - should be queued
	job2 := &batchv1.Job{
		ObjectMeta: metav1.ObjectMeta{
			Name:      "test-job-2",
			Namespace: "default",
		},
		Spec: batchv1.JobSpec{
			Template: corev1.PodTemplateSpec{
				Spec: corev1.PodSpec{
					Containers: []corev1.Container{
						{
							Name:  "test",
							Image: "test:latest",
						},
					},
					RestartPolicy: corev1.RestartPolicyNever,
				},
			},
		},
	}

	err = jm.CreateJobWithConcurrencyControl(ctx, "scan2", job2, nil, 1)
	assert.NoError(t, err)
	assert.Equal(t, 1, jm.GetActiveJobCount())
	assert.Equal(t, 1, jm.GetQueueLength())
}

func TestJobManager_CompleteJob(t *testing.T) {
	scheme := runtime.NewScheme()
	require.NoError(t, batchv1.AddToScheme(scheme))
	require.NoError(t, corev1.AddToScheme(scheme))

	config := &ScanControllerConfig{
		MaxConcurrentScans:         2,
		JobTTLSecondsAfterFinished: 300,
	}

	client := fake.NewClientBuilder().WithScheme(scheme).Build()
	log := zap.New(zap.UseDevMode(true))

	jm := NewJobManager(client, config, nil, log)
	ctx := context.Background()

	// Add a job manually
	jm.activeJobs["scan1"] = &JobInfo{
		ScanName:  "scan1",
		JobName:   "test-job-1",
		Namespace: "default",
		StartTime: time.Now(),
		Status:    JobStatusRunning,
	}

	// Complete the job
	err := jm.CompleteJob(ctx, "scan1", true)
	assert.NoError(t, err)
	assert.Equal(t, 0, jm.GetActiveJobCount())
}

func TestJobManager_UpdateJobStatus(t *testing.T) {
	config := &ScanControllerConfig{
		MaxConcurrentScans: 2,
	}

	client := fake.NewClientBuilder().Build()
	log := zap.New(zap.UseDevMode(true))

	jm := NewJobManager(client, config, nil, log)
	ctx := context.Background()

	// Add a job manually
	jm.activeJobs["scan1"] = &JobInfo{
		ScanName: "scan1",
		Status:   JobStatusPending,
	}

	// Update status
	err := jm.UpdateJobStatus(ctx, "scan1", JobStatusRunning)
	assert.NoError(t, err)

	jobInfo, exists := jm.GetJobInfo("scan1")
	assert.True(t, exists)
	assert.Equal(t, JobStatusRunning, jobInfo.Status)
}

func TestJobManager_QueuePriority(t *testing.T) {
	config := &ScanControllerConfig{
		MaxConcurrentScans: 1,
	}

	client := fake.NewClientBuilder().Build()
	log := zap.New(zap.UseDevMode(true))

	jm := NewJobManager(client, config, nil, log)

	// Fill up active jobs
	jm.activeJobs["scan1"] = &JobInfo{ScanName: "scan1"}

	// Queue jobs with different priorities
	job1 := &batchv1.Job{ObjectMeta: metav1.ObjectMeta{Name: "job1"}}
	job2 := &batchv1.Job{ObjectMeta: metav1.ObjectMeta{Name: "job2"}}
	job3 := &batchv1.Job{ObjectMeta: metav1.ObjectMeta{Name: "job3"}}

	jm.queueJob("scan2", job1, nil, 1) // Low priority
	jm.queueJob("scan3", job2, nil, 3) // High priority
	jm.queueJob("scan4", job3, nil, 2) // Medium priority

	// Check queue order (should be high to low priority)
	assert.Equal(t, 3, len(jm.jobQueue))
	assert.Equal(t, "scan3", jm.jobQueue[0].ScanName) // Highest priority first
	assert.Equal(t, "scan4", jm.jobQueue[1].ScanName) // Medium priority second
	assert.Equal(t, "scan2", jm.jobQueue[2].ScanName) // Lowest priority last
}

func TestJobManager_GetJobInfo(t *testing.T) {
	config := &ScanControllerConfig{
		MaxConcurrentScans: 2,
	}

	client := fake.NewClientBuilder().Build()
	log := zap.New(zap.UseDevMode(true))

	jm := NewJobManager(client, config, nil, log)

	// Add a job manually
	expectedJobInfo := &JobInfo{
		ScanName:  "scan1",
		JobName:   "test-job-1",
		Namespace: "default",
		StartTime: time.Now(),
		Status:    JobStatusRunning,
	}
	jm.activeJobs["scan1"] = expectedJobInfo

	// Get job info
	jobInfo, exists := jm.GetJobInfo("scan1")
	assert.True(t, exists)
	assert.Equal(t, expectedJobInfo, jobInfo)

	// Get non-existent job info
	_, exists = jm.GetJobInfo("non-existent")
	assert.False(t, exists)
}

func TestJobManager_GetStats(t *testing.T) {
	config := &ScanControllerConfig{
		MaxConcurrentScans: 2,
	}

	client := fake.NewClientBuilder().Build()
	log := zap.New(zap.UseDevMode(true))

	jm := NewJobManager(client, config, nil, log)

	// Initially empty
	assert.Equal(t, 0, jm.GetActiveJobCount())
	assert.Equal(t, 0, jm.GetQueueLength())

	// Add active job
	jm.activeJobs["scan1"] = &JobInfo{ScanName: "scan1"}
	assert.Equal(t, 1, jm.GetActiveJobCount())

	// Add queued job
	jm.jobQueue = append(jm.jobQueue, &QueuedJob{ScanName: "scan2"})
	assert.Equal(t, 1, jm.GetQueueLength())
}
