package scan

import (
	"context"
	"fmt"

	complianceapi "github.com/alauda/compliance-operator/pkg/apis/compliance/v1alpha1"
	"sigs.k8s.io/controller-runtime/pkg/client"
)

// CheckResultQuery provides flexible querying capabilities for CheckResults
type CheckResultQuery struct {
	client    client.Client
	namespace string
}

// NewCheckResultQuery creates a new CheckResultQuery instance
func NewCheckResultQuery(client client.Client, namespace string) *CheckResultQuery {
	return &CheckResultQuery{
		client:    client,
		namespace: namespace,
	}
}

// QueryOptions defines options for querying CheckResults
type QueryOptions struct {
	ScanName    string
	ProfileName string
	Status      string
	NodeName    string
	ScanType    string
	NodeRole    string
}

// GetCheckResultsByScan returns all CheckResults for a specific scan
func (q *CheckResultQuery) GetCheckResultsByScan(ctx context.Context, scanName string) (*complianceapi.CheckResultList, error) {
	var results complianceapi.CheckResultList
	err := q.client.List(ctx, &results,
		client.InNamespace(q.namespace),
		client.MatchingLabels{"compliance-operator.alauda.io/scan": scanName})
	return &results, err
}

// GetCheckResultsByProfile returns all CheckResults for a specific profile
func (q *CheckResultQuery) GetCheckResultsByProfile(ctx context.Context, profileName string) (*complianceapi.CheckResultList, error) {
	var results complianceapi.CheckResultList
	err := q.client.List(ctx, &results,
		client.InNamespace(q.namespace),
		client.MatchingLabels{"compliance-operator.alauda.io/profile": profileName})
	return &results, err
}

// GetCheckResultsByStatus returns all CheckResults with a specific status
func (q *CheckResultQuery) GetCheckResultsByStatus(ctx context.Context, status string) (*complianceapi.CheckResultList, error) {
	var allResults complianceapi.CheckResultList
	if err := q.client.List(ctx, &allResults, client.InNamespace(q.namespace)); err != nil {
		return nil, err
	}

	var filteredResults complianceapi.CheckResultList
	for _, result := range allResults.Items {
		// 检查每个规则结果的状态
		for _, ruleResult := range result.Spec.RuleResults {
			if ruleResult.Status == status {
				filteredResults.Items = append(filteredResults.Items, result)
				break // 找到一个匹配的规则就添加这个结果
			}
		}
	}

	return &filteredResults, nil
}

// GetCheckResultsByNode returns all CheckResults for a specific node
func (q *CheckResultQuery) GetCheckResultsByNode(ctx context.Context, nodeName string) (*complianceapi.CheckResultList, error) {
	var results complianceapi.CheckResultList
	err := q.client.List(ctx, &results,
		client.InNamespace(q.namespace),
		client.MatchingLabels{"compliance-operator.alauda.io/node": nodeName})
	return &results, err
}

// GetCheckResultsByNodeRole returns all CheckResults for nodes with a specific role
func (q *CheckResultQuery) GetCheckResultsByNodeRole(ctx context.Context, nodeRole string) (*complianceapi.CheckResultList, error) {
	var results complianceapi.CheckResultList
	err := q.client.List(ctx, &results,
		client.InNamespace(q.namespace),
		client.MatchingLabels{"compliance-operator.alauda.io/node-role": nodeRole})
	return &results, err
}

// GetCheckResultsByQuery returns CheckResults based on flexible query options
func (q *CheckResultQuery) GetCheckResultsByQuery(ctx context.Context, opts QueryOptions) (*complianceapi.CheckResultList, error) {
	labels := make(map[string]string)

	if opts.ScanName != "" {
		labels["compliance-operator.alauda.io/scan"] = opts.ScanName
	}
	if opts.ProfileName != "" {
		labels["compliance-operator.alauda.io/profile"] = opts.ProfileName
	}
	if opts.NodeName != "" {
		labels["compliance-operator.alauda.io/node"] = opts.NodeName
	}
	if opts.ScanType != "" {
		labels["compliance-operator.alauda.io/scan-type"] = opts.ScanType
	}
	if opts.NodeRole != "" {
		labels["compliance-operator.alauda.io/node-role"] = opts.NodeRole
	}

	var results complianceapi.CheckResultList
	if len(labels) > 0 {
		err := q.client.List(ctx, &results,
			client.InNamespace(q.namespace),
			client.MatchingLabels(labels))
		if err != nil {
			return nil, err
		}
	} else {
		err := q.client.List(ctx, &results, client.InNamespace(q.namespace))
		if err != nil {
			return nil, err
		}
	}

	// Filter by status if specified (since status is not a label)
	if opts.Status != "" {
		var filteredResults complianceapi.CheckResultList
		for _, result := range results.Items {
			// 检查每个规则结果的状态
			for _, ruleResult := range result.Spec.RuleResults {
				if ruleResult.Status == opts.Status {
					filteredResults.Items = append(filteredResults.Items, result)
					break // 找到一个匹配的规则就添加这个结果
				}
			}
		}
		return &filteredResults, nil
	}

	return &results, nil
}

// GetScanStatistics returns statistics for a specific scan
func (q *CheckResultQuery) GetScanStatistics(ctx context.Context, scanName string) (*complianceapi.ScanStats, error) {
	results, err := q.GetCheckResultsByScan(ctx, scanName)
	if err != nil {
		return nil, err
	}

	stats := &complianceapi.ScanStats{
		Total: len(results.Items),
	}

	for _, result := range results.Items {
		// 检查每个规则结果的状态
		for _, ruleResult := range result.Spec.RuleResults {
			switch ruleResult.Status {
			case "PASS":
				stats.Pass++
			case "FAIL":
				stats.Fail++
			case "ERROR":
				stats.Error++
			case "MANUAL":
				stats.Manual++
			case "INCONSISTENT":
				stats.Inconsistent++
			case "NOT-APPLICABLE":
				stats.NotApplicable++
			}
		}
	}

	return stats, nil
}

// PrintScanSummary prints a formatted summary of scan results
func PrintScanSummary(scanName string, stats *complianceapi.ScanStats) string {
	return fmt.Sprintf(`
Scan Summary: %s
================
Total Checks: %d
✅ Pass: %d
❌ Fail: %d
🔴 Error: %d
📋 Manual: %d
⚠️  Inconsistent: %d
➖ Not Applicable: %d
`,
		scanName, stats.Total, stats.Pass, stats.Fail, stats.Error, stats.Manual, stats.Inconsistent, stats.NotApplicable)
}

// listProfileRules 获取指定 Scan 的 Profile 中包含的所有规则
func (r *ScanReconciler) listProfileRules(ctx context.Context, scan *complianceapi.Scan) ([]complianceapi.Rule, error) {
	// 获取 Profile
	profile, err := r.getProfile(ctx, scan)
	if err != nil {
		return nil, fmt.Errorf("failed to get profile: %v", err)
	}

	// 创建规则列表
	rules := make([]complianceapi.Rule, 0, len(profile.Spec.Rules))

	// 获取每个规则
	for _, ruleRef := range profile.Spec.Rules {
		rule, err := r.getRule(ctx, ruleRef.Name, scan.Namespace)
		if err != nil {
			return nil, fmt.Errorf("failed to get rule %s: %v", ruleRef.Name, err)
		}
		rules = append(rules, *rule)
	}

	return rules, nil
}

// getProfile 获取指定 Scan 的 Profile
func (r *ScanReconciler) getProfile(ctx context.Context, scan *complianceapi.Scan) (*complianceapi.Profile, error) {
	var profile complianceapi.Profile
	if err := r.Get(ctx, client.ObjectKey{
		Name:      scan.Spec.Profile,
		Namespace: scan.Namespace,
	}, &profile); err != nil {
		return nil, fmt.Errorf("failed to get profile %s: %v", scan.Spec.Profile, err)
	}
	return &profile, nil
}

// getRule 获取指定名称的规则
func (r *ScanReconciler) getRule(ctx context.Context, name string, namespace string) (*complianceapi.Rule, error) {
	var rule complianceapi.Rule
	if err := r.Get(ctx, client.ObjectKey{
		Name:      name,
		Namespace: namespace,
	}, &rule); err != nil {
		return nil, fmt.Errorf("failed to get rule %s: %v", name, err)
	}
	return &rule, nil
}
