package scan

import (
	"testing"

	batchv1 "k8s.io/api/batch/v1"
	corev1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"

	complianceapi "github.com/alauda/compliance-operator/pkg/apis/compliance/v1alpha1"
)

func TestObjectPools(t *testing.T) {
	pools := NewObjectPools()

	t.Run("Job Pool", func(t *testing.T) {
		// Get a job from the pool
		job1 := pools.GetJob()
		if job1 == nil {
			t.Fatal("Expected non-nil job from pool")
		}

		// Verify it's properly initialized
		if job1.APIVersion != "batch/v1" {
			t.Errorf("Expected APIVersion 'batch/v1', got '%s'", job1.APIVersion)
		}
		if job1.Kind != "Job" {
			t.Errorf("Expected Kind 'Job', got '%s'", job1.Kind)
		}

		// Modify the job
		job1.Name = "test-job"
		job1.Namespace = "test-namespace"

		// Return it to the pool
		pools.PutJob(job1)

		// Get another job and verify it's clean
		job2 := pools.GetJob()
		if job2.Name != "" {
			t.Errorf("Expected clean job name, got '%s'", job2.Name)
		}
		if job2.Namespace != "" {
			t.Errorf("Expected clean job namespace, got '%s'", job2.Namespace)
		}

		pools.PutJob(job2)
	})

	t.Run("ConfigMap Pool", func(t *testing.T) {
		// Get a configmap from the pool
		cm1 := pools.GetConfigMap()
		if cm1 == nil {
			t.Fatal("Expected non-nil configmap from pool")
		}

		// Verify it's properly initialized
		if cm1.APIVersion != "v1" {
			t.Errorf("Expected APIVersion 'v1', got '%s'", cm1.APIVersion)
		}
		if cm1.Kind != "ConfigMap" {
			t.Errorf("Expected Kind 'ConfigMap', got '%s'", cm1.Kind)
		}

		// Modify the configmap
		cm1.Name = "test-cm"
		cm1.Data = map[string]string{"key": "value"}

		// Return it to the pool
		pools.PutConfigMap(cm1)

		// Get another configmap and verify it's clean
		cm2 := pools.GetConfigMap()
		if cm2.Name != "" {
			t.Errorf("Expected clean configmap name, got '%s'", cm2.Name)
		}
		if cm2.Data != nil {
			t.Errorf("Expected clean configmap data, got %v", cm2.Data)
		}

		pools.PutConfigMap(cm2)
	})

	t.Run("CheckResult Pool", func(t *testing.T) {
		// Get a checkresult from the pool
		cr1 := pools.GetCheckResult()
		if cr1 == nil {
			t.Fatal("Expected non-nil checkresult from pool")
		}

		// Verify it's properly initialized
		if cr1.APIVersion != "compliance-operator.alauda.io/v1alpha1" {
			t.Errorf("Expected APIVersion 'compliance-operator.alauda.io/v1alpha1', got '%s'", cr1.APIVersion)
		}
		if cr1.Kind != "CheckResult" {
			t.Errorf("Expected Kind 'CheckResult', got '%s'", cr1.Kind)
		}

		// Modify the checkresult
		cr1.Name = "test-cr"
		cr1.Spec.ScanName = "test-scan"

		// Return it to the pool
		pools.PutCheckResult(cr1)

		// Get another checkresult and verify it's clean
		cr2 := pools.GetCheckResult()
		if cr2.Name != "" {
			t.Errorf("Expected clean checkresult name, got '%s'", cr2.Name)
		}
		if cr2.Spec.ScanName != "" {
			t.Errorf("Expected clean checkresult scan name, got '%s'", cr2.Spec.ScanName)
		}

		pools.PutCheckResult(cr2)
	})

	t.Run("String Slice Pool", func(t *testing.T) {
		// Get a string slice from the pool
		slice1 := pools.GetStringSlice()
		if slice1 == nil {
			t.Fatal("Expected non-nil string slice from pool")
		}

		// Verify it's empty but has capacity
		if len(*slice1) != 0 {
			t.Errorf("Expected empty slice, got length %d", len(*slice1))
		}
		if cap(*slice1) == 0 {
			t.Error("Expected slice with capacity")
		}

		// Add some data
		*slice1 = append(*slice1, "item1", "item2", "item3")

		// Return it to the pool
		pools.PutStringSlice(slice1)

		// Get another slice and verify it's clean
		slice2 := pools.GetStringSlice()
		if len(*slice2) != 0 {
			t.Errorf("Expected clean slice, got length %d", len(*slice2))
		}

		pools.PutStringSlice(slice2)
	})

	t.Run("Map Pool", func(t *testing.T) {
		// Get a map from the pool
		map1 := pools.GetMap()
		if map1 == nil {
			t.Fatal("Expected non-nil map from pool")
		}

		// Verify it's empty
		if len(*map1) != 0 {
			t.Errorf("Expected empty map, got length %d", len(*map1))
		}

		// Add some data
		(*map1)["key1"] = "value1"
		(*map1)["key2"] = "value2"

		// Return it to the pool
		pools.PutMap(map1)

		// Get another map and verify it's clean
		map2 := pools.GetMap()
		if len(*map2) != 0 {
			t.Errorf("Expected clean map, got length %d", len(*map2))
		}

		pools.PutMap(map2)
	})
}

func TestBatchJobResult(t *testing.T) {
	// Get a result from the pool
	result1 := GetBatchJobResult()
	if result1 == nil {
		t.Fatal("Expected non-nil batch job result from pool")
	}

	// Verify it's clean
	if len(result1.ProcessedJobs) != 0 {
		t.Errorf("Expected empty processed jobs, got length %d", len(result1.ProcessedJobs))
	}
	if len(result1.Errors) != 0 {
		t.Errorf("Expected empty errors, got length %d", len(result1.Errors))
	}
	if result1.TotalTime != 0 {
		t.Errorf("Expected zero total time, got %d", result1.TotalTime)
	}

	// Add some data
	result1.ProcessedJobs = append(result1.ProcessedJobs, "job1", "job2")
	result1.Errors = append(result1.Errors, nil)
	result1.TotalTime = 1000

	// Return it to the pool
	PutBatchJobResult(result1)

	// Get another result and verify it's clean
	result2 := GetBatchJobResult()
	if len(result2.ProcessedJobs) != 0 {
		t.Errorf("Expected clean processed jobs, got length %d", len(result2.ProcessedJobs))
	}
	if len(result2.Errors) != 0 {
		t.Errorf("Expected clean errors, got length %d", len(result2.Errors))
	}
	if result2.TotalTime != 0 {
		t.Errorf("Expected zero total time, got %d", result2.TotalTime)
	}

	PutBatchJobResult(result2)
}

func TestObjectPoolsMemoryOptimization(t *testing.T) {
	pools := NewObjectPools()

	// Test that we can reuse objects multiple times
	const iterations = 100

	t.Run("Job Reuse", func(t *testing.T) {
		for i := 0; i < iterations; i++ {
			job := pools.GetJob()
			job.Name = "test-job"
			job.Spec.Template.Spec.Containers = []corev1.Container{
				{Name: "test", Image: "test:latest"},
			}
			pools.PutJob(job)
		}
	})

	t.Run("ConfigMap Reuse", func(t *testing.T) {
		for i := 0; i < iterations; i++ {
			cm := pools.GetConfigMap()
			cm.Name = "test-cm"
			cm.Data = map[string]string{"test": "data"}
			pools.PutConfigMap(cm)
		}
	})

	t.Run("CheckResult Reuse", func(t *testing.T) {
		for i := 0; i < iterations; i++ {
			cr := pools.GetCheckResult()
			cr.Name = "test-cr"
			cr.Spec.ScanName = "test-scan"
			cr.Spec.RuleResults = []complianceapi.RuleResult{
				{RuleID: "test-rule", Status: "PASS"},
			}
			pools.PutCheckResult(cr)
		}
	})

	// Verify memory stats (placeholder test since sync.Pool doesn't expose size)
	stats := pools.GetMemoryStats()
	if stats.JobPoolSize < 0 {
		t.Error("Expected non-negative job pool size")
	}
}

func BenchmarkObjectPools(b *testing.B) {
	pools := NewObjectPools()

	b.Run("Job Pool", func(b *testing.B) {
		b.ResetTimer()
		for i := 0; i < b.N; i++ {
			job := pools.GetJob()
			job.Name = "benchmark-job"
			pools.PutJob(job)
		}
	})

	b.Run("Direct Allocation", func(b *testing.B) {
		b.ResetTimer()
		for i := 0; i < b.N; i++ {
			job := &batchv1.Job{
				TypeMeta: metav1.TypeMeta{
					APIVersion: "batch/v1",
					Kind:       "Job",
				},
			}
			job.Name = "benchmark-job"
			// Simulate cleanup
			job.Name = ""
			job.Spec = batchv1.JobSpec{}
		}
	})
}
