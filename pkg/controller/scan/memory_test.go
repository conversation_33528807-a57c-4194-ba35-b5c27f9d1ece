package scan

import (
	"fmt"
	"testing"
	"time"

	"github.com/go-logr/logr"
)

func TestMemoryManager(t *testing.T) {
	config := &ScanControllerConfig{
		MaxMemoryUsage:            512, // 512MB
		GCThreshold:               256, // 256MB
		ConcurrencyControlEnabled: true,
		MaxConcurrentScans:        10,
		MemoryOptimizationEnabled: true,
	}

	// Create a test logger
	log := logr.Discard()
	manager := NewMemoryManager(config, log)
	if manager == nil {
		t.Fatal("Expected non-nil memory manager")
	}

	t.Run("Initial State", func(t *testing.T) {
		stats := manager.GetRuntimeStats()
		if stats.CurrentMemoryMB < 0 {
			t.<PERSON>rf("Expected non-negative current memory, got %d", stats.CurrentMemoryMB)
		}
		if stats.MaxConcurrency != 10 {
			t.<PERSON><PERSON>("Expected max concurrency 10, got %d", stats.MaxConcurrency)
		}
		if stats.ActiveScanCount != 0 {
			t.<PERSON><PERSON>("Expected active scan count 0, got %d", stats.ActiveScanCount)
		}
	})

	t.<PERSON>("Scan Registration", func(t *testing.T) {
		// Register a scan
		manager.StartScan("test-scan-1")
		stats := manager.GetRuntimeStats()
		if stats.ActiveScanCount != 1 {
			t.Errorf("Expected active scan count 1, got %d", stats.ActiveScanCount)
		}

		// Register another scan
		manager.StartScan("test-scan-2")
		stats = manager.GetRuntimeStats()
		if stats.ActiveScanCount != 2 {
			t.Errorf("Expected active scan count 2, got %d", stats.ActiveScanCount)
		}

		// Complete a scan
		manager.EndScan("test-scan-1")
		stats = manager.GetRuntimeStats()
		if stats.ActiveScanCount != 1 {
			t.Errorf("Expected active scan count 1, got %d", stats.ActiveScanCount)
		}

		// Complete the other scan
		manager.EndScan("test-scan-2")
		stats = manager.GetRuntimeStats()
		if stats.ActiveScanCount != 0 {
			t.Errorf("Expected active scan count 0, got %d", stats.ActiveScanCount)
		}
	})

	t.Run("Concurrency Control", func(t *testing.T) {
		// Test that we can start scans up to the limit
		for i := 0; i < 10; i++ {
			allowed := manager.CanStartScan()
			if !allowed {
				t.Errorf("Expected to allow scan %d, but was denied", i)
			}
			manager.StartScan("test-scan")
		}

		// The 11th scan should be denied
		allowed := manager.CanStartScan()
		if allowed {
			t.Error("Expected to deny 11th scan, but was allowed")
		}

		// Clean up
		for i := 0; i < 10; i++ {
			manager.EndScan("test-scan")
		}
	})

	t.Run("Memory Monitoring", func(t *testing.T) {
		// Get current stats
		stats := manager.GetRuntimeStats()
		if stats.CurrentMemoryMB < 0 {
			t.Errorf("Expected non-negative current memory, got %d", stats.CurrentMemoryMB)
		}
		if stats.PeakMemoryMB < 0 {
			t.Errorf("Expected non-negative peak memory, got %d", stats.PeakMemoryMB)
		}
	})

	t.Run("Memory Threshold Detection", func(t *testing.T) {
		// Test with very low threshold to trigger GC
		lowConfig := &ScanControllerConfig{
			MaxMemoryUsage:            1, // 1MB (very low)
			GCThreshold:               1, // 1MB
			ConcurrencyControlEnabled: true,
			MaxConcurrentScans:        10,
			MemoryOptimizationEnabled: true,
		}

		lowManager := NewMemoryManager(lowConfig, log)

		// Get stats
		stats := lowManager.GetRuntimeStats()
		if stats.CurrentMemoryMB < 0 {
			t.Errorf("Expected non-negative memory, got %d", stats.CurrentMemoryMB)
		}
	})
}

func TestMemoryManagerConcurrency(t *testing.T) {
	config := &ScanControllerConfig{
		MaxMemoryUsage:            1024,
		GCThreshold:               512,
		ConcurrencyControlEnabled: true,
		MaxConcurrentScans:        5,
		MemoryOptimizationEnabled: true,
	}

	log := logr.Discard()
	manager := NewMemoryManager(config, log)

	// Test concurrent scan registration
	t.Run("Concurrent Registration", func(t *testing.T) {
		const numGoroutines = 10
		const scansPerGoroutine = 10

		done := make(chan bool, numGoroutines)

		for i := 0; i < numGoroutines; i++ {
			go func(id int) {
				for j := 0; j < scansPerGoroutine; j++ {
					scanName := fmt.Sprintf("scan-%d-%d", id, j)
					if manager.CanStartScan() {
						manager.StartScan(scanName)
						// Simulate some work
						time.Sleep(time.Millisecond)
						manager.EndScan(scanName)
					}
				}
				done <- true
			}(i)
		}

		// Wait for all goroutines to complete
		for i := 0; i < numGoroutines; i++ {
			<-done
		}

		// Verify final state
		stats := manager.GetRuntimeStats()
		if stats.ActiveScanCount != 0 {
			t.Errorf("Expected active scan count 0 after all goroutines complete, got %d", stats.ActiveScanCount)
		}
	})
}

func TestMemoryManagerBasicOperations(t *testing.T) {
	config := &ScanControllerConfig{
		MaxMemoryUsage:            512,
		GCThreshold:               256,
		ConcurrencyControlEnabled: true,
		MaxConcurrentScans:        10,
		MemoryOptimizationEnabled: true,
	}

	log := logr.Discard()
	manager := NewMemoryManager(config, log)

	// Test basic operations
	t.Run("Basic Stats", func(t *testing.T) {
		stats := manager.GetRuntimeStats()
		if stats.CurrentMemoryMB < 0 {
			t.Errorf("Expected non-negative memory, got %d", stats.CurrentMemoryMB)
		}
	})
}

func BenchmarkMemoryManager(b *testing.B) {
	config := &ScanControllerConfig{
		MaxMemoryUsage:            1024,
		GCThreshold:               512,
		ConcurrencyControlEnabled: true,
		MaxConcurrentScans:        100,
		MemoryOptimizationEnabled: true,
	}

	log := logr.Discard()
	manager := NewMemoryManager(config, log)

	b.Run("CanStartScan", func(b *testing.B) {
		b.ResetTimer()
		for i := 0; i < b.N; i++ {
			manager.CanStartScan()
		}
	})

	b.Run("StartScan", func(b *testing.B) {
		b.ResetTimer()
		for i := 0; i < b.N; i++ {
			manager.StartScan("benchmark-scan")
		}
		// Clean up
		for i := 0; i < b.N; i++ {
			manager.EndScan("benchmark-scan")
		}
	})

	b.Run("GetRuntimeStats", func(b *testing.B) {
		b.ResetTimer()
		for i := 0; i < b.N; i++ {
			manager.GetRuntimeStats()
		}
	})
}

func TestMemoryManagerEdgeCases(t *testing.T) {
	config := &ScanControllerConfig{
		MaxMemoryUsage:            512,
		GCThreshold:               256,
		ConcurrencyControlEnabled: true,
		MaxConcurrentScans:        2,
		MemoryOptimizationEnabled: true,
	}

	log := logr.Discard()
	manager := NewMemoryManager(config, log)

	t.Run("Double Registration", func(t *testing.T) {
		// Register the same scan twice
		manager.StartScan("duplicate-scan")
		manager.StartScan("duplicate-scan")

		stats := manager.GetRuntimeStats()
		if stats.ActiveScanCount != 2 {
			t.Errorf("Expected active scan count 2 for duplicate registration, got %d", stats.ActiveScanCount)
		}

		// End it twice
		manager.EndScan("duplicate-scan")
		manager.EndScan("duplicate-scan")

		stats = manager.GetRuntimeStats()
		if stats.ActiveScanCount != 0 {
			t.Errorf("Expected active scan count 0 after duplicate end, got %d", stats.ActiveScanCount)
		}
	})

	t.Run("End Non-existent Scan", func(t *testing.T) {
		// This should not panic or cause issues
		manager.EndScan("non-existent-scan")

		stats := manager.GetRuntimeStats()
		if stats.ActiveScanCount < 0 {
			t.Errorf("Expected non-negative active scan count, got %d", stats.ActiveScanCount)
		}
	})

	t.Run("Disabled Concurrency Control", func(t *testing.T) {
		disabledConfig := &ScanControllerConfig{
			MaxMemoryUsage:            512,
			GCThreshold:               256,
			ConcurrencyControlEnabled: false,
			MaxConcurrentScans:        2,
			MemoryOptimizationEnabled: true,
		}

		disabledManager := NewMemoryManager(disabledConfig, log)

		// Should always allow scans when concurrency control is disabled
		for i := 0; i < 10; i++ {
			allowed := disabledManager.CanStartScan()
			if !allowed {
				t.Errorf("Expected to allow scan %d when concurrency control disabled, but was denied", i)
			}
		}
	})
}
