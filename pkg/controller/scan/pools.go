package scan

import (
	"sync"

	batchv1 "k8s.io/api/batch/v1"
	corev1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"

	complianceapi "github.com/alauda/compliance-operator/pkg/apis/compliance/v1alpha1"
)

// ObjectPools holds all object pools for memory optimization
type ObjectPools struct {
	JobPool         *sync.Pool
	ConfigMapPool   *sync.Pool
	CheckResultPool *sync.Pool
	StringSlicePool *sync.Pool
	MapPool         *sync.Pool
}

// NewObjectPools creates and initializes all object pools
func NewObjectPools() *ObjectPools {
	return &ObjectPools{
		JobPool: &sync.Pool{
			New: func() interface{} {
				return &batchv1.Job{
					TypeMeta: metav1.TypeMeta{
						APIVersion: "batch/v1",
						Kind:       "Job",
					},
				}
			},
		},
		ConfigMapPool: &sync.Pool{
			New: func() interface{} {
				return &corev1.ConfigMap{
					TypeMeta: metav1.TypeMeta{
						APIVersion: "v1",
						Kind:       "ConfigMap",
					},
				}
			},
		},
		CheckResultPool: &sync.Pool{
			New: func() interface{} {
				return &complianceapi.CheckResult{
					TypeMeta: metav1.TypeMeta{
						APIVersion: "compliance-operator.alauda.io/v1alpha1",
						Kind:       "CheckResult",
					},
				}
			},
		},
		StringSlicePool: &sync.Pool{
			New: func() interface{} {
				// Pre-allocate slice with reasonable capacity
				slice := make([]string, 0, 32)
				return &slice
			},
		},
		MapPool: &sync.Pool{
			New: func() interface{} {
				// Pre-allocate map with reasonable capacity
				m := make(map[string]interface{}, 16)
				return &m
			},
		},
	}
}

// GetJob retrieves a Job from the pool
func (p *ObjectPools) GetJob() *batchv1.Job {
	job := p.JobPool.Get().(*batchv1.Job)
	// Reset the job to clean state
	p.resetJob(job)
	return job
}

// PutJob returns a Job to the pool
func (p *ObjectPools) PutJob(job *batchv1.Job) {
	if job != nil {
		p.JobPool.Put(job)
	}
}

// GetConfigMap retrieves a ConfigMap from the pool
func (p *ObjectPools) GetConfigMap() *corev1.ConfigMap {
	cm := p.ConfigMapPool.Get().(*corev1.ConfigMap)
	// Reset the configmap to clean state
	p.resetConfigMap(cm)
	return cm
}

// PutConfigMap returns a ConfigMap to the pool
func (p *ObjectPools) PutConfigMap(cm *corev1.ConfigMap) {
	if cm != nil {
		p.ConfigMapPool.Put(cm)
	}
}

// GetCheckResult retrieves a CheckResult from the pool
func (p *ObjectPools) GetCheckResult() *complianceapi.CheckResult {
	cr := p.CheckResultPool.Get().(*complianceapi.CheckResult)
	// Reset the checkresult to clean state
	p.resetCheckResult(cr)
	return cr
}

// PutCheckResult returns a CheckResult to the pool
func (p *ObjectPools) PutCheckResult(cr *complianceapi.CheckResult) {
	if cr != nil {
		p.CheckResultPool.Put(cr)
	}
}

// GetStringSlice retrieves a string slice from the pool
func (p *ObjectPools) GetStringSlice() *[]string {
	slice := p.StringSlicePool.Get().(*[]string)
	// Reset slice to empty but keep capacity
	*slice = (*slice)[:0]
	return slice
}

// PutStringSlice returns a string slice to the pool
func (p *ObjectPools) PutStringSlice(slice *[]string) {
	if slice != nil && cap(*slice) <= 1024 { // Prevent memory leaks from very large slices
		p.StringSlicePool.Put(slice)
	}
}

// GetMap retrieves a map from the pool
func (p *ObjectPools) GetMap() *map[string]interface{} {
	m := p.MapPool.Get().(*map[string]interface{})
	// Clear the map but keep capacity
	for k := range *m {
		delete(*m, k)
	}
	return m
}

// PutMap returns a map to the pool
func (p *ObjectPools) PutMap(m *map[string]interface{}) {
	if m != nil && len(*m) <= 256 { // Prevent memory leaks from very large maps
		p.MapPool.Put(m)
	}
}

// resetJob resets a Job to clean state for reuse
func (p *ObjectPools) resetJob(job *batchv1.Job) {
	// Keep TypeMeta, reset everything else
	job.ObjectMeta = metav1.ObjectMeta{}
	job.Spec = batchv1.JobSpec{}
	job.Status = batchv1.JobStatus{}
}

// resetConfigMap resets a ConfigMap to clean state for reuse
func (p *ObjectPools) resetConfigMap(cm *corev1.ConfigMap) {
	// Keep TypeMeta, reset everything else
	cm.ObjectMeta = metav1.ObjectMeta{}
	cm.Data = nil
	cm.BinaryData = nil
}

// resetCheckResult resets a CheckResult to clean state for reuse
func (p *ObjectPools) resetCheckResult(cr *complianceapi.CheckResult) {
	// Keep TypeMeta, reset everything else
	cr.ObjectMeta = metav1.ObjectMeta{}
	cr.Spec = complianceapi.CheckResultSpec{}
}

// BatchJobResult holds the result of processing a batch of jobs
type BatchJobResult struct {
	ProcessedJobs []string
	Errors        []error
	TotalTime     int64 // in milliseconds
}

// GetBatchJobResult retrieves a BatchJobResult from a simple pool
var batchJobResultPool = sync.Pool{
	New: func() interface{} {
		return &BatchJobResult{
			ProcessedJobs: make([]string, 0, 50),
			Errors:        make([]error, 0, 10),
		}
	},
}

// GetBatchJobResult retrieves a BatchJobResult from the pool
func GetBatchJobResult() *BatchJobResult {
	result := batchJobResultPool.Get().(*BatchJobResult)
	// Reset to clean state
	result.ProcessedJobs = result.ProcessedJobs[:0]
	result.Errors = result.Errors[:0]
	result.TotalTime = 0
	return result
}

// PutBatchJobResult returns a BatchJobResult to the pool
func PutBatchJobResult(result *BatchJobResult) {
	if result != nil && cap(result.ProcessedJobs) <= 200 && cap(result.Errors) <= 50 {
		batchJobResultPool.Put(result)
	}
}

// MemoryStats provides memory usage statistics for monitoring
type MemoryStats struct {
	JobPoolSize         int
	ConfigMapPoolSize   int
	CheckResultPoolSize int
	StringSlicePoolSize int
	MapPoolSize         int
}

// GetMemoryStats returns current memory pool statistics
func (p *ObjectPools) GetMemoryStats() MemoryStats {
	// Note: sync.Pool doesn't provide size information directly
	// This is a placeholder for monitoring integration
	return MemoryStats{
		JobPoolSize:         0, // Would need custom implementation to track
		ConfigMapPoolSize:   0,
		CheckResultPoolSize: 0,
		StringSlicePoolSize: 0,
		MapPoolSize:         0,
	}
}
