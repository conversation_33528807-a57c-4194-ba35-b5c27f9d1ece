package scan

import (
	"context"
	"fmt"
	"sort"
	"time"

	batchv1 "k8s.io/api/batch/v1"
	v1 "k8s.io/api/core/v1"
	"k8s.io/apimachinery/pkg/api/errors"
	"k8s.io/apimachinery/pkg/runtime"
	ctrl "sigs.k8s.io/controller-runtime"
	"sigs.k8s.io/controller-runtime/pkg/client"

	complianceapi "github.com/alauda/compliance-operator/pkg/apis/compliance/v1alpha1"
	"github.com/go-logr/logr"
	"github.com/robfig/cron/v3"
)

// ScanReconciler reconciles a Scan object
type ScanReconciler struct {
	client.Client
	Log           logr.Logger
	Scheme        *runtime.Scheme
	Config        *ScanControllerConfig
	Pools         *ObjectPools
	MemoryManager *MemoryManager
}

// +kubebuilder:rbac:groups=compliance-operator.alauda.io,resources=scans,verbs=get;list;watch;create;update;patch;delete
// +kubebuilder:rbac:groups=compliance-operator.alauda.io,resources=scans/status,verbs=get;update;patch
// +kubebuilder:rbac:groups=compliance-operator.alauda.io,resources=checkresults,verbs=get;list;watch;create;update;patch;delete
// +kubebuilder:rbac:groups=batch,resources=jobs,verbs=get;list;watch;create;update;patch;delete
// +kubebuilder:rbac:groups="",resources=pods,verbs=get;list;watch;create;update;patch;delete
// +kubebuilder:rbac:groups="",resources=configmaps,verbs=get;list;watch;create;update;patch;delete

// Reconcile reads the state of the cluster for a Scan object and makes changes based on it
func (r *ScanReconciler) Reconcile(ctx context.Context, req ctrl.Request) (ctrl.Result, error) {
	log := r.Log.WithValues("scan", req.NamespacedName)

	// Fetch the Scan instance
	var scan complianceapi.Scan
	if err := r.Get(ctx, req.NamespacedName, &scan); err != nil {
		if errors.IsNotFound(err) {
			// Object not found, return
			return ctrl.Result{}, nil
		}
		// Error reading the object - requeue the request
		return ctrl.Result{}, err
	}

	// Check memory and concurrency limits before processing
	if r.MemoryManager != nil && scan.Status.Phase == "Pending" {
		if !r.MemoryManager.CanStartScan() {
			log.Info("Scan delayed due to concurrency limits", "activeScanCount", r.MemoryManager.GetRuntimeStats().ActiveScanCount)
			return ctrl.Result{RequeueAfter: 30 * time.Second}, nil
		}
	}

	// Validate scan specification
	if err := validateScanSpec(&scan); err != nil {
		r.logScanError(err, &scan, "Invalid scan specification")
		// Update status to indicate validation error
		if _, updateErr := r.updateScanStatus(ctx, &scan, "Error", "ValidationFailed", err.Error()); updateErr != nil {
			log.Error(updateErr, "Failed to update scan status after validation error")
		}
		return ctrl.Result{}, nil // Don't requeue invalid specs
	}

	// Initialize scan status if needed
	if scan.Status.Phase == "" {
		// 使用带重试机制的辅助方法更新状态
		if _, err := r.updateScanStatus(ctx, &scan, "Pending", "", "Scan initialization"); err != nil {
			log.Error(err, "Failed to update Scan status")
			return ctrl.Result{}, err
		}
		return ctrl.Result{Requeue: true}, nil
	}

	// Check if this is a new scan execution (either initial or triggered by schedule)
	needsNewScanID := false

	// 检查是否需要生成新的 scanID
	if scan.Status.Phase == "Pending" {
		needsNewScanID = true
	} else if scan.Annotations != nil && scan.Annotations["compliance-operator.alauda.io/force-scan"] == "true" {
		// 强制扫描具有最高优先级，优先于定时任务检查
		needsNewScanID = true
		// 移除强制扫描注解
		scan.Annotations["compliance-operator.alauda.io/force-scan"] = "false"
		if err := r.Update(ctx, &scan); err != nil {
			log.Error(err, "Failed to update Scan annotations")
			return ctrl.Result{}, err
		}
		log.Info("Force scan triggered", "scan", scan.Name)
	} else if scan.Status.Phase == "Done" && scan.Spec.Schedule != "" {
		// 对于定时任务，检查是否到了下一次执行时间
		if scan.Status.EndTime != nil {
			lastExecTime := scan.Status.EndTime.Time
			schedule, err := cron.ParseStandard(scan.Spec.Schedule)
			if err != nil {
				log.Error(err, "Failed to parse schedule", "schedule", scan.Spec.Schedule)
			} else {
				nextExecTime := schedule.Next(lastExecTime)
				if time.Now().After(nextExecTime) {
					log.Info("Scheduled execution time reached", "lastExec", lastExecTime, "nextExec", nextExecTime)
					needsNewScanID = true
				}
			}
		}
	}

	// 如果需要新的 scanID，生成并存储
	if needsNewScanID {
		scanID := generateScanID(&scan)
		log.Info("Generated new scanID for scan execution", "scanID", scanID)

		// 将 scanID 存储在注解中
		if scan.Annotations == nil {
			scan.Annotations = make(map[string]string)
		}
		scan.Annotations["compliance-operator.alauda.io/current-scan-id"] = scanID

		// 更新 Scan 对象
		if err := r.Update(ctx, &scan); err != nil {
			log.Error(err, "Failed to update Scan with scanID")
			return ctrl.Result{}, err
		}

		// 重置扫描状态为 Launching，使用带重试机制的辅助方法
		if _, err := r.updateScanStatusWithStartTime(ctx, &scan, "Launching", "", "Starting scan execution"); err != nil {
			log.Error(err, "Failed to update Scan status")
			return ctrl.Result{}, err
		}

		return ctrl.Result{Requeue: true}, nil
	}

	// Handle different phases of the scan
	switch scan.Status.Phase {
	case "Launching":
		return r.initializeScan(ctx, &scan)
	case "Running":
		return r.checkScanProgress(ctx, &scan)
	case "Done", "Error":
		// If scan has a schedule, check if it's time to run again
		if scan.Spec.Schedule != "" {
			// Check if it's time to run again based on schedule
			if scan.Status.EndTime != nil {
				schedule, err := cron.ParseStandard(scan.Spec.Schedule)
				if err != nil {
					log.Error(err, "Failed to parse schedule", "schedule", scan.Spec.Schedule)
					return ctrl.Result{}, nil
				}

				nextRun := schedule.Next(scan.Status.EndTime.Time)
				now := time.Now()

				if now.After(nextRun) {
					// Time to run again
					log.Info("Scheduled scan time reached, restarting scan", "lastRun", scan.Status.EndTime.Time, "nextRun", nextRun)

					// Reset scan status to trigger a new run using helper method
					if _, err := r.updateScanStatus(ctx, &scan, "Pending", "", "Scheduled scan execution"); err != nil {
						log.Error(err, "Failed to update Scan status for scheduled run")
						return ctrl.Result{}, err
					}
					return ctrl.Result{Requeue: true}, nil
				} else {
					// Calculate delay until next run
					delay := nextRun.Sub(now)
					log.Info("Scheduling next scan run", "delay", delay, "nextRun", nextRun)
					return ctrl.Result{RequeueAfter: delay}, nil
				}
			}
		}

		// Clean up old historical results if needed
		if err := r.cleanupHistoricalResults(ctx, &scan); err != nil {
			log.Error(err, "Failed to cleanup historical results")
			// Continue anyway
		}

		return ctrl.Result{}, nil
	default:
		log.Info("Unknown scan phase", "phase", scan.Status.Phase)
		return ctrl.Result{}, nil
	}
}

// scheduleNextScan handles scheduled scan rescheduling
func (r *ScanReconciler) scheduleNextScan(ctx context.Context, scan *complianceapi.Scan) (ctrl.Result, error) {
	// For now, just requeue after a day for scheduled scans
	// In a real implementation, you would parse the cron schedule
	return ctrl.Result{RequeueAfter: 24 * time.Hour}, nil
}

// SetupWithManager sets up the controller with the Manager.
func (r *ScanReconciler) SetupWithManager(mgr ctrl.Manager) error {
	return ctrl.NewControllerManagedBy(mgr).
		For(&complianceapi.Scan{}).
		Owns(&complianceapi.CheckResult{}).
		Owns(&batchv1.Job{}).
		Complete(r)
}

// cleanupHistoricalResults 清理过期的历史扫描结果
func (r *ScanReconciler) cleanupHistoricalResults(ctx context.Context, scan *complianceapi.Scan) error {
	log := r.Log.WithValues("scan", scan.Name, "namespace", scan.Namespace)

	// 如果未设置历史结果保留数量，则使用默认值
	maxHistoricalResults := 5
	if scan.Spec.MaxHistoricalResults > 0 {
		maxHistoricalResults = int(scan.Spec.MaxHistoricalResults)
	}

	// 获取与此扫描相关的所有 CheckResult
	var results complianceapi.CheckResultList
	if err := r.List(ctx, &results, client.InNamespace(scan.Namespace), client.MatchingLabels{
		"compliance-operator.alauda.io/scan": scan.Name,
	}); err != nil {
		return fmt.Errorf("failed to list CheckResults: %v", err)
	}

	// 按创建时间排序
	sort.Slice(results.Items, func(i, j int) bool {
		if results.Items[i].CreationTimestamp.IsZero() || results.Items[j].CreationTimestamp.IsZero() {
			return false
		}
		return results.Items[i].CreationTimestamp.After(results.Items[j].CreationTimestamp.Time)
	})

	// 保留最新的 maxHistoricalResults 个结果，删除其余的
	if len(results.Items) > maxHistoricalResults {
		for i := maxHistoricalResults; i < len(results.Items); i++ {
			result := &results.Items[i]
			log.Info("Deleting old CheckResult", "checkResult", result.Name, "creationTime", result.CreationTimestamp)
			if err := r.Delete(ctx, result); err != nil {
				if !errors.IsNotFound(err) {
					log.Error(err, "Failed to delete old CheckResult", "checkResult", result.Name)
				} else {
					log.V(1).Info("CheckResult already deleted", "checkResult", result.Name)
				}
				// 继续处理其他结果
			}
		}
	}

	// 获取与此扫描相关的所有临时 ConfigMap
	var configMaps v1.ConfigMapList
	if err := r.List(ctx, &configMaps, client.InNamespace(scan.Namespace), client.MatchingLabels{
		"compliance-operator.alauda.io/scan":      scan.Name,
		"compliance-operator.alauda.io/temporary": "true",
	}); err != nil {
		return fmt.Errorf("failed to list ConfigMaps: %v", err)
	}

	// 删除所有临时 ConfigMap
	for i := range configMaps.Items {
		configMap := &configMaps.Items[i]

		// 检查是否是当前扫描的 ConfigMap
		if configMap.Labels["compliance-operator.alauda.io/scan-id"] == scan.Annotations["compliance-operator.alauda.io/current-scan-id"] {
			// 跳过当前扫描的 ConfigMap
			continue
		}

		log.Info("Deleting temporary ConfigMap", "configMap", configMap.Name)
		if err := r.Delete(ctx, configMap); err != nil {
			if !errors.IsNotFound(err) {
				log.Error(err, "Failed to delete temporary ConfigMap", "configMap", configMap.Name)
			} else {
				log.V(1).Info("ConfigMap already deleted", "configMap", configMap.Name)
			}
			// 继续处理其他 ConfigMap
		}
	}

	return nil
}
