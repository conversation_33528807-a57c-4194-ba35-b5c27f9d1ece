package scan

import (
	"context"
	"fmt"
	"sync"
	"time"

	batchv1 "k8s.io/api/batch/v1"
	corev1 "k8s.io/api/core/v1"
	"k8s.io/apimachinery/pkg/api/errors"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"sigs.k8s.io/controller-runtime/pkg/client"

	"github.com/go-logr/logr"
)

// JobManager manages scan job concurrency and lifecycle
type JobManager struct {
	client        client.Client
	config        *ScanControllerConfig
	memoryManager *MemoryManager
	log           logr.Logger
	mu            sync.RWMutex

	// Job tracking
	activeJobs     map[string]*JobInfo // scanName -> JobInfo
	jobQueue       []*QueuedJob        // Jobs waiting to be created
	maxConcurrent  int
	activeJobCount int // Total number of active jobs across all scans
}

// JobInfo tracks information about an active job
type JobInfo struct {
	ScanName      string
	JobName       string
	Namespace     string
	StartTime     time.Time
	Status        JobStatus
	ConfigMapName string
}

// QueuedJob represents a job waiting to be created
type QueuedJob struct {
	ScanName  string
	JobSpec   *batchv1.Job
	ConfigMap *corev1.ConfigMap
	Priority  int
	QueueTime time.Time
}

// JobStatus represents the status of a job
type JobStatus string

const (
	JobStatusPending   JobStatus = "Pending"
	JobStatusRunning   JobStatus = "Running"
	JobStatusCompleted JobStatus = "Completed"
	JobStatusFailed    JobStatus = "Failed"
)

// NewJobManager creates a new job manager
func NewJobManager(client client.Client, config *ScanControllerConfig, memoryManager *MemoryManager, log logr.Logger) *JobManager {
	return &JobManager{
		client:        client,
		config:        config,
		memoryManager: memoryManager,
		log:           log,
		activeJobs:    make(map[string]*JobInfo),
		jobQueue:      make([]*QueuedJob, 0),
		maxConcurrent: config.MaxConcurrentScans,
	}
}

// CanCreateJob checks if a new job can be created based on concurrency limits
func (jm *JobManager) CanCreateJob() bool {
	jm.mu.RLock()
	defer jm.mu.RUnlock()

	// Check job-level concurrency using the accurate job count
	if jm.activeJobCount >= jm.maxConcurrent {
		return false
	}

	// Check memory manager constraints
	if jm.memoryManager != nil && !jm.memoryManager.CanStartScan() {
		return false
	}

	return true
}

// CreateJobWithConcurrencyControl creates a job with concurrency control
func (jm *JobManager) CreateJobWithConcurrencyControl(ctx context.Context, scanName string, job *batchv1.Job, configMap *corev1.ConfigMap, priority int) error {
	// First, sync job status to get accurate concurrency count
	if err := jm.syncJobStatus(ctx); err != nil {
		jm.log.Error(err, "Failed to sync job status before creating job")
	}

	if jm.CanCreateJob() {
		// Create job immediately
		return jm.createJobNow(ctx, scanName, job, configMap)
	}

	// Queue the job
	jm.queueJob(scanName, job, configMap, priority)
	jm.log.Info("Job queued due to concurrency limits",
		"scanName", scanName,
		"activeJobs", len(jm.activeJobs),
		"queueLength", len(jm.jobQueue))

	return nil
}

// createJobNow creates a job immediately
func (jm *JobManager) createJobNow(ctx context.Context, scanName string, job *batchv1.Job, configMap *corev1.ConfigMap) error {
	jm.mu.Lock()
	defer jm.mu.Unlock()

	// Create ConfigMap first
	if configMap != nil {
		if err := jm.client.Create(ctx, configMap); err != nil && !errors.IsAlreadyExists(err) {
			return fmt.Errorf("failed to create ConfigMap: %w", err)
		}
	}

	// Create Job
	if err := jm.client.Create(ctx, job); err != nil {
		// If job creation fails, clean up the ConfigMap
		if configMap != nil {
			_ = jm.client.Delete(ctx, configMap)
		}
		return fmt.Errorf("failed to create Job: %w", err)
	}

	// Track the job - use a unique key for each job
	jobKey := fmt.Sprintf("%s-%s", scanName, job.Name)
	jobInfo := &JobInfo{
		ScanName:      scanName,
		JobName:       job.Name,
		Namespace:     job.Namespace,
		StartTime:     time.Now(),
		Status:        JobStatusPending,
		ConfigMapName: "",
	}

	if configMap != nil {
		jobInfo.ConfigMapName = configMap.Name
	}

	jm.activeJobs[jobKey] = jobInfo
	jm.activeJobCount++

	// Register with memory manager
	if jm.memoryManager != nil {
		jm.memoryManager.StartScan(scanName)
	}

	jm.log.Info("Job created successfully",
		"scanName", scanName,
		"jobName", job.Name,
		"activeJobs", len(jm.activeJobs))

	return nil
}

// queueJob adds a job to the queue
func (jm *JobManager) queueJob(scanName string, job *batchv1.Job, configMap *corev1.ConfigMap, priority int) {
	jm.mu.Lock()
	defer jm.mu.Unlock()

	queuedJob := &QueuedJob{
		ScanName:  scanName,
		JobSpec:   job,
		ConfigMap: configMap,
		Priority:  priority,
		QueueTime: time.Now(),
	}

	// Insert job in priority order (higher priority first)
	inserted := false
	for i, existingJob := range jm.jobQueue {
		if priority > existingJob.Priority {
			// Insert at position i
			jm.jobQueue = append(jm.jobQueue[:i], append([]*QueuedJob{queuedJob}, jm.jobQueue[i:]...)...)
			inserted = true
			break
		}
	}

	if !inserted {
		jm.jobQueue = append(jm.jobQueue, queuedJob)
	}
}

// syncJobStatus synchronizes job status with Kubernetes to detect completed jobs
func (jm *JobManager) syncJobStatus(ctx context.Context) error {
	jm.mu.Lock()
	defer jm.mu.Unlock()

	// Get all active jobs from our tracking
	var jobsToCheck []string
	var jobInfos []*JobInfo
	for jobKey, jobInfo := range jm.activeJobs {
		jobsToCheck = append(jobsToCheck, jobKey)
		jobInfos = append(jobInfos, jobInfo)
	}

	// Check each job's status in Kubernetes
	for i, jobKey := range jobsToCheck {
		jobInfo := jobInfos[i]

		// Get the job from Kubernetes
		job := &batchv1.Job{}
		err := jm.client.Get(ctx, client.ObjectKey{
			Name:      jobInfo.JobName,
			Namespace: jobInfo.Namespace,
		}, job)

		if err != nil {
			if errors.IsNotFound(err) {
				// Job was deleted, consider it completed
				jm.log.Info("Job not found in cluster, marking as completed",
					"scanName", jobInfo.ScanName,
					"jobName", jobInfo.JobName)
				delete(jm.activeJobs, jobKey)
				jm.activeJobCount--
				continue
			}
			// Other errors, skip this job
			jm.log.Error(err, "Failed to get job status", "jobName", jobInfo.JobName)
			continue
		}

		// Check if job is completed
		if job.Status.Succeeded > 0 {
			jm.log.Info("Job completed successfully, removing from active tracking",
				"scanName", jobInfo.ScanName,
				"jobName", jobInfo.JobName)
			delete(jm.activeJobs, jobKey)
			jm.activeJobCount--
		} else if job.Status.Failed > 0 {
			jm.log.Info("Job failed, removing from active tracking",
				"scanName", jobInfo.ScanName,
				"jobName", jobInfo.JobName)
			delete(jm.activeJobs, jobKey)
			jm.activeJobCount--
		}
	}

	return nil
}

// ProcessQueue processes queued jobs when slots become available
func (jm *JobManager) ProcessQueue(ctx context.Context) error {
	// First, sync job status with Kubernetes to detect completed jobs
	if err := jm.syncJobStatus(ctx); err != nil {
		jm.log.Error(err, "Failed to sync job status")
	}

	jm.mu.Lock()
	defer jm.mu.Unlock()

	processed := 0
	for len(jm.jobQueue) > 0 && jm.activeJobCount < jm.maxConcurrent {
		// Check memory constraints
		if jm.memoryManager != nil && !jm.memoryManager.CanStartScan() {
			break
		}

		// Get the highest priority job
		queuedJob := jm.jobQueue[0]
		jm.jobQueue = jm.jobQueue[1:]

		// Create the job (unlock during creation to avoid deadlock)
		jm.mu.Unlock()
		err := jm.createJobNow(ctx, queuedJob.ScanName, queuedJob.JobSpec, queuedJob.ConfigMap)
		jm.mu.Lock()

		if err != nil {
			jm.log.Error(err, "Failed to create queued job", "scanName", queuedJob.ScanName)
			// Re-queue the job at the end for retry
			jm.jobQueue = append(jm.jobQueue, queuedJob)
			break
		}

		processed++
		waitTime := time.Since(queuedJob.QueueTime)
		jm.log.Info("Processed queued job",
			"scanName", queuedJob.ScanName,
			"waitTime", waitTime,
			"processed", processed)
	}

	return nil
}

// CompleteScan marks all jobs for a scan as completed and triggers cleanup
func (jm *JobManager) CompleteScan(ctx context.Context, scanName string, success bool) error {
	jm.mu.Lock()

	// Find all jobs for this scan
	var jobsToComplete []string
	for _, jobInfo := range jm.activeJobs {
		if jobInfo.ScanName == scanName {
			jobsToComplete = append(jobsToComplete, jobInfo.JobName)
		}
	}
	jm.mu.Unlock()

	// Complete each job
	for _, jobName := range jobsToComplete {
		if err := jm.CompleteJob(ctx, scanName, jobName, success); err != nil {
			jm.log.Error(err, "Failed to complete job", "jobName", jobName, "scanName", scanName)
		}
	}

	return nil
}

// UpdateJobStatus updates the status of a job
func (jm *JobManager) UpdateJobStatus(ctx context.Context, scanName, jobName string, status JobStatus) error {
	jm.mu.Lock()
	defer jm.mu.Unlock()

	jobKey := fmt.Sprintf("%s-%s", scanName, jobName)
	jobInfo, exists := jm.activeJobs[jobKey]
	if !exists {
		return fmt.Errorf("job not found: %s", jobKey)
	}

	oldStatus := jobInfo.Status
	jobInfo.Status = status

	jm.log.Info("Job status updated",
		"scanName", scanName,
		"jobName", jobName,
		"oldStatus", oldStatus,
		"newStatus", status)

	// If job is completed or failed, trigger queue processing
	if status == JobStatusCompleted || status == JobStatusFailed {
		go func() {
			// Process queue in background to avoid blocking
			if err := jm.ProcessQueue(ctx); err != nil {
				jm.log.Error(err, "Failed to process job queue after completion")
			}
		}()
	}

	return nil
}

// CompleteJob marks a job as completed and triggers cleanup
func (jm *JobManager) CompleteJob(ctx context.Context, scanName, jobName string, success bool) error {
	jm.mu.Lock()
	jobKey := fmt.Sprintf("%s-%s", scanName, jobName)
	jobInfo, exists := jm.activeJobs[jobKey]
	if !exists {
		jm.mu.Unlock()
		return fmt.Errorf("job not found: %s", jobKey)
	}

	// Update status
	if success {
		jobInfo.Status = JobStatusCompleted
	} else {
		jobInfo.Status = JobStatusFailed
	}

	duration := time.Since(jobInfo.StartTime)
	jm.log.Info("Job completed",
		"scanName", scanName,
		"jobName", jobName,
		"success", success,
		"duration", duration)

	// Remove from active jobs
	delete(jm.activeJobs, jobKey)
	jm.activeJobCount--
	jm.mu.Unlock()

	// Unregister from memory manager
	if jm.memoryManager != nil {
		jm.memoryManager.EndScan(jobInfo.ScanName)
	}

	// Trigger cleanup if configured
	if jm.config.JobTTLSecondsAfterFinished > 0 {
		go jm.scheduleCleanup(ctx, jobInfo, time.Duration(jm.config.JobTTLSecondsAfterFinished)*time.Second)
	}

	// Process queue
	return jm.ProcessQueue(ctx)
}

// scheduleCleanup schedules cleanup of job resources after TTL
func (jm *JobManager) scheduleCleanup(ctx context.Context, jobInfo *JobInfo, ttl time.Duration) {
	time.Sleep(ttl)

	jm.log.Info("Starting scheduled cleanup",
		"scanName", jobInfo.ScanName,
		"jobName", jobInfo.JobName)

	// Clean up Job
	job := &batchv1.Job{
		ObjectMeta: metav1.ObjectMeta{
			Name:      jobInfo.JobName,
			Namespace: jobInfo.Namespace,
		},
	}

	if err := jm.client.Delete(ctx, job); err != nil && !errors.IsNotFound(err) {
		jm.log.Error(err, "Failed to cleanup job", "jobName", jobInfo.JobName)
	}

	// Clean up ConfigMap
	if jobInfo.ConfigMapName != "" {
		configMap := &corev1.ConfigMap{
			ObjectMeta: metav1.ObjectMeta{
				Name:      jobInfo.ConfigMapName,
				Namespace: jobInfo.Namespace,
			},
		}

		if err := jm.client.Delete(ctx, configMap); err != nil && !errors.IsNotFound(err) {
			jm.log.Error(err, "Failed to cleanup ConfigMap", "configMapName", jobInfo.ConfigMapName)
		}
	}

	jm.log.Info("Scheduled cleanup completed", "scanName", jobInfo.ScanName)
}

// GetActiveJobCount returns the number of active jobs
func (jm *JobManager) GetActiveJobCount() int {
	jm.mu.RLock()
	defer jm.mu.RUnlock()
	return jm.activeJobCount
}

// GetActiveJobs returns the active jobs map (for testing)
func (jm *JobManager) GetActiveJobs() map[string]*JobInfo {
	jm.mu.RLock()
	defer jm.mu.RUnlock()
	return jm.activeJobs
}

// SetActiveJobCount sets the active job count (for testing)
func (jm *JobManager) SetActiveJobCount(count int) {
	jm.mu.Lock()
	defer jm.mu.Unlock()
	jm.activeJobCount = count
}

// GetQueueLength returns the number of queued jobs
func (jm *JobManager) GetQueueLength() int {
	jm.mu.RLock()
	defer jm.mu.RUnlock()
	return len(jm.jobQueue)
}

// GetJobInfo returns information about a specific job
func (jm *JobManager) GetJobInfo(scanName, jobName string) (*JobInfo, bool) {
	jm.mu.RLock()
	defer jm.mu.RUnlock()
	jobKey := fmt.Sprintf("%s-%s", scanName, jobName)
	jobInfo, exists := jm.activeJobs[jobKey]
	return jobInfo, exists
}
