package scan

import (
	"context"
	"strings"
	"testing"

	"github.com/stretchr/testify/assert"
	corev1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/types"

	complianceapi "github.com/alauda/compliance-operator/pkg/apis/compliance/v1alpha1"
)

func TestGenerateJobName(t *testing.T) {
	testCases := []struct {
		name          string
		checkType     string
		ruleName      string
		nodeName      string
		expectedStart string
		maxLength     int
	}{
		{
			name:          "Platform check",
			checkType:     "platform",
			ruleName:      "stig-k8s-api-server-anonymous-auth-disabled",
			nodeName:      "",
			expectedStart: "stig-k8s-api-server-anonymous-auth-disabled-",
			maxLength:     63,
		},
		{
			name:          "Node check",
			checkType:     "node",
			ruleName:      "stig-k8s-kubelet-anonymous-auth-disabled",
			nodeName:      "worker-node-1",
			expectedStart: "stig-k8s-kubelet-anonymous-auth-disabled-worker-node-1-",
			maxLength:     63,
		},
		{
			name:          "Very long rule name",
			checkType:     "platform",
			ruleName:      "this-is-a-very-long-rule-name-that-exceeds-the-kubernetes-resource-name-length-limit-and-needs-to-be-truncated",
			nodeName:      "",
			expectedStart: "", // 不检查前缀，因为会被截断
			maxLength:     63,
		},
		{
			name:          "Special characters",
			checkType:     "platform",
			ruleName:      "rule_with.special@characters!",
			nodeName:      "",
			expectedStart: "rule-with-special-characters-",
			maxLength:     63,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			jobName := generateJobName(tc.checkType, tc.ruleName, tc.nodeName)

			// 检查名称长度
			assert.LessOrEqual(t, len(jobName), tc.maxLength)

			// 检查名称前缀（如果有期望的前缀）
			if tc.expectedStart != "" {
				assert.True(t, strings.HasPrefix(jobName, tc.expectedStart))
			}

			// 检查是否包含随机后缀
			parts := strings.Split(jobName, "-")
			suffix := parts[len(parts)-1]
			assert.Equal(t, 5, len(suffix))

			// 检查是否符合 Kubernetes 命名规范
			assert.Regexp(t, "^[a-z0-9]([-a-z0-9]*[a-z0-9])?$", jobName)
		})
	}
}

func TestCleanKubernetesName(t *testing.T) {
	testCases := []struct {
		name     string
		input    string
		expected string
	}{
		{
			name:     "Simple name",
			input:    "simple-name",
			expected: "simple-name",
		},
		{
			name:     "Uppercase letters",
			input:    "UPPERCASE",
			expected: "uppercase",
		},
		{
			name:     "Special characters",
			input:    "name_with.special@chars!",
			expected: "name-with-special-chars",
		},
		{
			name:     "Leading special characters",
			input:    "-_leading",
			expected: "leading",
		},
		{
			name:     "Trailing special characters",
			input:    "trailing-_",
			expected: "trailing",
		},
		{
			name:     "Multiple consecutive dashes",
			input:    "multiple---dashes",
			expected: "multiple-dashes",
		},
		{
			name:     "Empty string",
			input:    "",
			expected: "default",
		},
		{
			name:     "Only special characters",
			input:    "-_@!.",
			expected: "default",
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			result := cleanKubernetesName(tc.input)
			assert.Equal(t, tc.expected, result)
		})
	}
}

func TestInitializeScan(t *testing.T) {
	// 创建一个带有 scanID 的 Scan
	scanID := "test-scan-12345678-20230101-120000-abcd"
	scan := &complianceapi.Scan{
		ObjectMeta: metav1.ObjectMeta{
			Name:      "test-scan",
			Namespace: "default",
			Annotations: map[string]string{
				"compliance-operator.alauda.io/current-scan-id": scanID,
			},
		},
		Spec: complianceapi.ScanSpec{
			Profile: "test-profile",
		},
	}

	// 创建 Profile
	profile := &complianceapi.Profile{
		ObjectMeta: metav1.ObjectMeta{
			Name:      "test-profile",
			Namespace: "default",
		},
		Spec: complianceapi.ProfileSpec{
			Rules: []complianceapi.RuleReference{
				{
					Name: "test-rule-1",
				},
				{
					Name: "test-rule-2",
				},
			},
		},
	}

	// 创建规则
	rule1 := &complianceapi.Rule{
		ObjectMeta: metav1.ObjectMeta{
			Name:      "test-rule-1",
			Namespace: "default",
		},
		Spec: complianceapi.RuleSpec{
			Title:       "Test Rule 1",
			Description: "Rule for testing",
			CheckType:   "platform",
			CheckScript: "echo 'Test script 1'",
		},
	}

	rule2 := &complianceapi.Rule{
		ObjectMeta: metav1.ObjectMeta{
			Name:      "test-rule-2",
			Namespace: "default",
		},
		Spec: complianceapi.RuleSpec{
			Title:       "Test Rule 2",
			Description: "Rule for testing",
			CheckType:   "platform",
			CheckScript: "echo 'Test script 2'",
		},
	}

	// 设置 reconciler
	r := setupReconciler(t, scan, profile, rule1, rule2)

	// 执行 initializeScan
	result, err := r.initializeScan(context.Background(), scan)

	// 验证结果
	assert.NoError(t, err)
	assert.NotNil(t, result)

	// 由于 fake client 的限制，我们可能无法直接更新 Status 子资源
	// 这里我们只验证 initializeScan 是否成功执行
	updatedScan := &complianceapi.Scan{}
	err = r.Get(context.Background(), types.NamespacedName{Name: "test-scan", Namespace: "default"}, updatedScan)
	assert.NoError(t, err)
	assert.NotNil(t, updatedScan)
}

func TestCreatePlatformScanJobs(t *testing.T) {
	// 创建测试资源
	scan := &complianceapi.Scan{
		ObjectMeta: metav1.ObjectMeta{
			Name:      "test-scan",
			Namespace: "default",
		},
		Spec: complianceapi.ScanSpec{
			Profile:  "test-profile",
			ScanType: "platform",
		},
	}

	rule1 := &complianceapi.Rule{
		ObjectMeta: metav1.ObjectMeta{
			Name:      "test-rule-1",
			Namespace: "default",
		},
		Spec: complianceapi.RuleSpec{
			Title:       "Test Rule 1",
			Description: "Rule for testing",
			CheckType:   "platform",
			CheckScript: "echo 'Test script 1'",
		},
	}

	rule2 := &complianceapi.Rule{
		ObjectMeta: metav1.ObjectMeta{
			Name:      "test-rule-2",
			Namespace: "default",
		},
		Spec: complianceapi.RuleSpec{
			Title:       "Test Rule 2",
			Description: "Rule for testing",
			CheckType:   "node", // 这个规则不应该被处理
			CheckScript: "echo 'Test script 2'",
		},
	}

	// 设置 reconciler
	r := setupReconciler(t, scan, rule1, rule2)

	// 执行 createPlatformScanJobs
	scanID := "test-scan-id"
	rules := []complianceapi.Rule{*rule1, *rule2}
	err := r.createPlatformScanJobs(context.Background(), scan, rules, scanID)

	// 验证结果
	assert.NoError(t, err)

	// 由于 fake client 的限制，我们可能无法直接验证 Job 创建
	// 这里我们只验证 createPlatformScanJobs 是否成功执行
}

func TestCreateNodeScanJobs(t *testing.T) {
	// 创建测试资源
	scan := &complianceapi.Scan{
		ObjectMeta: metav1.ObjectMeta{
			Name:      "test-scan",
			Namespace: "default",
		},
		Spec: complianceapi.ScanSpec{
			Profile:      "test-profile",
			ScanType:     "node",
			NodeSelector: map[string]string{"role": "worker"},
		},
	}

	rule1 := &complianceapi.Rule{
		ObjectMeta: metav1.ObjectMeta{
			Name:      "test-rule-1",
			Namespace: "default",
		},
		Spec: complianceapi.RuleSpec{
			Title:       "Test Rule 1",
			Description: "Rule for testing",
			CheckType:   "node",
			CheckScript: "echo 'Test script 1'",
		},
	}

	rule2 := &complianceapi.Rule{
		ObjectMeta: metav1.ObjectMeta{
			Name:      "test-rule-2",
			Namespace: "default",
		},
		Spec: complianceapi.RuleSpec{
			Title:       "Test Rule 2",
			Description: "Rule for testing",
			CheckType:   "platform", // 这个规则不应该被处理
			CheckScript: "echo 'Test script 2'",
		},
	}

	node1 := &corev1.Node{
		ObjectMeta: metav1.ObjectMeta{
			Name: "worker-node-1",
			Labels: map[string]string{
				"role": "worker",
			},
		},
	}

	node2 := &corev1.Node{
		ObjectMeta: metav1.ObjectMeta{
			Name: "worker-node-2",
			Labels: map[string]string{
				"role": "worker",
			},
		},
	}

	// 设置 reconciler
	r := setupReconciler(t, scan, rule1, rule2, node1, node2)

	// 执行 createNodeScanJobs
	scanID := "test-scan-id"
	rules := []complianceapi.Rule{*rule1, *rule2}
	err := r.createNodeScanJobs(context.Background(), scan, rules, scanID)

	// 验证结果
	assert.NoError(t, err)

	// 由于 fake client 的限制，我们可能无法直接验证 Job 创建
	// 这里我们只验证 createNodeScanJobs 是否成功执行
}
