package scan

import (
	"os"
	"strconv"
	"time"
)

// ScanControllerConfig holds configuration for the scan controller
type ScanControllerConfig struct {
	// MaxConcurrentScans limits the number of concurrent scans
	MaxConcurrentScans int

	// DefaultScanTimeout is the default timeout for scan operations
	DefaultScanTimeout time.Duration

	// RetryBackoff is the backoff duration for retries
	RetryBackoff time.Duration

	// MaxHistoricalResults is the default number of historical results to keep
	MaxHistoricalResults int32

	// ReportSizeLimit is the maximum size for reports stored in ConfigMaps
	ReportSizeLimit int64

	// CleanupInterval is how often to run cleanup operations
	CleanupInterval time.Duration

	// JobTTLSecondsAfterFinished is the TTL for completed jobs
	JobTTLSecondsAfterFinished int32

	// BatchOperationEnabled enables batch API operations for better performance
	BatchOperationEnabled bool

	// BatchSize is the maximum number of items to process in a single batch
	BatchSize int

	// BatchTimeout is the timeout for batch operations
	BatchTimeout time.Duration
}

// DefaultConfig returns the default configuration
func DefaultConfig() *ScanControllerConfig {
	return &ScanControllerConfig{
		MaxConcurrentScans:         getEnvInt("SCAN_MAX_CONCURRENT", 5),
		DefaultScanTimeout:         getEnvDuration("SCAN_DEFAULT_TIMEOUT", 30*time.Minute),
		RetryBackoff:               getEnvDuration("SCAN_RETRY_BACKOFF", 5*time.Second),
		MaxHistoricalResults:       int32(getEnvInt("SCAN_MAX_HISTORICAL_RESULTS", 5)),
		ReportSizeLimit:            int64(getEnvInt("SCAN_REPORT_SIZE_LIMIT", 1024*1024)), // 1MB
		CleanupInterval:            getEnvDuration("SCAN_CLEANUP_INTERVAL", 1*time.Hour),
		JobTTLSecondsAfterFinished: int32(getEnvInt("SCAN_JOB_TTL_SECONDS", 3600)), // 1 hour
		BatchOperationEnabled:      getEnvBool("SCAN_BATCH_OPERATION_ENABLED", true),
		BatchSize:                  getEnvInt("SCAN_BATCH_SIZE", 50),
		BatchTimeout:               getEnvDuration("SCAN_BATCH_TIMEOUT", 10*time.Second),
	}
}

// getEnvInt gets an integer value from environment variable with default
func getEnvInt(key string, defaultValue int) int {
	if value := os.Getenv(key); value != "" {
		if intValue, err := strconv.Atoi(value); err == nil {
			return intValue
		}
	}
	return defaultValue
}

// getEnvDuration gets a duration value from environment variable with default
func getEnvDuration(key string, defaultValue time.Duration) time.Duration {
	if value := os.Getenv(key); value != "" {
		if duration, err := time.ParseDuration(value); err == nil {
			return duration
		}
	}
	return defaultValue
}

// getEnvBool gets a boolean value from environment variable with default
func getEnvBool(key string, defaultValue bool) bool {
	if value := os.Getenv(key); value != "" {
		if boolValue, err := strconv.ParseBool(value); err == nil {
			return boolValue
		}
	}
	return defaultValue
}

// ShouldUsePVC determines if a PVC should be used instead of ConfigMap for reports
func (c *ScanControllerConfig) ShouldUsePVC(reportSize int64) bool {
	return reportSize > c.ReportSizeLimit
}

// GetJobTTL returns the TTL for jobs as a pointer (for Kubernetes Job spec)
func (c *ScanControllerConfig) GetJobTTL() *int32 {
	return &c.JobTTLSecondsAfterFinished
}
