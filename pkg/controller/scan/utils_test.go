package scan

import (
	"context"
	"fmt"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	batchv1 "k8s.io/api/batch/v1"
	corev1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/runtime"
	"k8s.io/apimachinery/pkg/types"
	"sigs.k8s.io/controller-runtime/pkg/client"
	"sigs.k8s.io/controller-runtime/pkg/client/fake"
	"sigs.k8s.io/controller-runtime/pkg/log/zap"

	complianceapi "github.com/alauda/compliance-operator/pkg/apis/compliance/v1alpha1"
)

func TestValidateScanSpec(t *testing.T) {
	tests := []struct {
		name    string
		scan    *complianceapi.Scan
		wantErr bool
	}{
		{
			name: "valid scan",
			scan: &complianceapi.Scan{
				Spec: complianceapi.ScanSpec{
					Profile: "test-profile",
				},
			},
			wantErr: false,
		},
		{
			name: "missing profile",
			scan: &complianceapi.Scan{
				Spec: complianceapi.ScanSpec{},
			},
			wantErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := validateScanSpec(tt.scan)
			if tt.wantErr {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
			}
		})
	}
}

func TestUpdateScanStatusBatch(t *testing.T) {
	scheme := runtime.NewScheme()
	require.NoError(t, complianceapi.AddToScheme(scheme))

	scan := &complianceapi.Scan{
		ObjectMeta: metav1.ObjectMeta{
			Name:      "test-scan",
			Namespace: "test-namespace",
		},
		Spec: complianceapi.ScanSpec{
			Profile: "test-profile",
		},
		Status: complianceapi.ScanStatus{
			Phase: "Pending",
		},
	}

	client := fake.NewClientBuilder().
		WithScheme(scheme).
		WithObjects(scan).
		WithStatusSubresource(scan).
		Build()

	reconciler := &ScanReconciler{
		Client: client,
		Log:    zap.New(zap.UseDevMode(true)),
		Scheme: scheme,
		Config: DefaultConfig(),
	}

	ctx := context.Background()
	now := time.Now()
	updates := StatusUpdate{
		Phase:     "Running",
		Result:    "InProgress",
		Message:   "Scan is running",
		StartTime: &now,
	}

	err := reconciler.updateScanStatusBatch(ctx, scan, updates)
	require.NoError(t, err)

	// Verify the update
	var updatedScan complianceapi.Scan
	err = client.Get(ctx, types.NamespacedName{
		Name:      scan.Name,
		Namespace: scan.Namespace,
	}, &updatedScan)
	require.NoError(t, err)

	assert.Equal(t, "Running", updatedScan.Status.Phase)
	assert.Equal(t, "InProgress", updatedScan.Status.Result)
	assert.Equal(t, "Scan is running", updatedScan.Status.Message)
	assert.NotNil(t, updatedScan.Status.StartTime)
}

func TestDefaultConfig(t *testing.T) {
	config := DefaultConfig()

	assert.Greater(t, config.MaxConcurrentScans, 0)
	assert.Greater(t, config.DefaultScanTimeout, time.Duration(0))
	assert.Greater(t, config.RetryBackoff, time.Duration(0))
	assert.Greater(t, config.MaxHistoricalResults, int32(0))
	assert.Greater(t, config.ReportSizeLimit, int64(0))
	assert.Greater(t, config.CleanupInterval, time.Duration(0))
	assert.Greater(t, config.JobTTLSecondsAfterFinished, int32(0))
}

func TestShouldUsePVC(t *testing.T) {
	config := DefaultConfig()

	// Small report should use ConfigMap
	assert.False(t, config.ShouldUsePVC(1024)) // 1KB

	// Large report should use PVC
	assert.True(t, config.ShouldUsePVC(2*1024*1024)) // 2MB
}

func TestGetRulesBatch(t *testing.T) {
	scheme := runtime.NewScheme()
	require.NoError(t, complianceapi.AddToScheme(scheme))

	// Create test rules
	rule1 := &complianceapi.Rule{
		ObjectMeta: metav1.ObjectMeta{
			Name:      "test-rule-1",
			Namespace: "test-namespace",
		},
		Spec: complianceapi.RuleSpec{
			Severity: "high",
		},
	}

	rule2 := &complianceapi.Rule{
		ObjectMeta: metav1.ObjectMeta{
			Name:      "test-rule-2",
			Namespace: "test-namespace",
		},
		Spec: complianceapi.RuleSpec{
			Severity: "medium",
		},
	}

	client := fake.NewClientBuilder().
		WithScheme(scheme).
		WithObjects(rule1, rule2).
		Build()

	reconciler := &ScanReconciler{
		Client: client,
		Log:    zap.New(zap.UseDevMode(true)),
		Scheme: scheme,
		Config: DefaultConfig(),
	}

	ctx := context.Background()
	ruleNames := []string{"test-rule-1", "test-rule-2", "non-existent-rule"}

	result, err := reconciler.getRulesBatch(ctx, ruleNames, "test-namespace")
	require.NoError(t, err)

	// Verify results
	assert.Len(t, result, 3)

	// Check found rule 1
	assert.True(t, result["test-rule-1"].Found)
	assert.Equal(t, "high", result["test-rule-1"].Severity)
	assert.NotNil(t, result["test-rule-1"].Rule)

	// Check found rule 2
	assert.True(t, result["test-rule-2"].Found)
	assert.Equal(t, "medium", result["test-rule-2"].Severity)
	assert.NotNil(t, result["test-rule-2"].Rule)

	// Check non-existent rule
	assert.False(t, result["non-existent-rule"].Found)
	assert.Equal(t, "medium", result["non-existent-rule"].Severity) // default
	assert.Nil(t, result["non-existent-rule"].Rule)
}

func TestGetConfigMapsBatch(t *testing.T) {
	scheme := runtime.NewScheme()
	require.NoError(t, corev1.AddToScheme(scheme))
	require.NoError(t, batchv1.AddToScheme(scheme))
	require.NoError(t, complianceapi.AddToScheme(scheme))

	// Create test scan
	scan := &complianceapi.Scan{
		ObjectMeta: metav1.ObjectMeta{
			Name:      "test-scan",
			Namespace: "test-namespace",
		},
	}

	// Create test jobs
	job1 := batchv1.Job{
		ObjectMeta: metav1.ObjectMeta{
			Name:      "test-job-1",
			Namespace: "test-namespace",
			Labels: map[string]string{
				"compliance-operator.alauda.io/scan": "test-scan",
				"compliance-operator.alauda.io/rule": "test-rule-1",
			},
		},
	}

	job2 := batchv1.Job{
		ObjectMeta: metav1.ObjectMeta{
			Name:      "test-job-2",
			Namespace: "test-namespace",
			Labels: map[string]string{
				"compliance-operator.alauda.io/scan": "test-scan",
				"compliance-operator.alauda.io/rule": "test-rule-2",
			},
		},
	}

	// Create corresponding ConfigMaps
	configMap1 := &corev1.ConfigMap{
		ObjectMeta: metav1.ObjectMeta{
			Name:      "test-job-1", // Same name as job for direct lookup
			Namespace: "test-namespace",
			Labels: map[string]string{
				"compliance-operator.alauda.io/scan": "test-scan",
				"compliance-operator.alauda.io/rule": "test-rule-1",
				"compliance-operator.alauda.io/job":  "test-job-1",
			},
		},
		Data: map[string]string{
			"scan_name": "test-scan",
			"rule_id":   "test-rule-1",
			"job_name":  "test-job-1",
		},
	}

	configMap2 := &corev1.ConfigMap{
		ObjectMeta: metav1.ObjectMeta{
			Name:      "different-name", // Different name to test label-based lookup
			Namespace: "test-namespace",
			Labels: map[string]string{
				"compliance-operator.alauda.io/scan": "test-scan",
				"compliance-operator.alauda.io/rule": "test-rule-2",
				"compliance-operator.alauda.io/job":  "test-job-2",
			},
		},
		Data: map[string]string{
			"scan_name": "test-scan",
			"rule_id":   "test-rule-2",
			"job_name":  "test-job-2",
		},
	}

	client := fake.NewClientBuilder().
		WithScheme(scheme).
		WithObjects(scan, configMap1, configMap2).
		Build()

	reconciler := &ScanReconciler{
		Client: client,
		Log:    zap.New(zap.UseDevMode(true)),
		Scheme: scheme,
		Config: DefaultConfig(),
	}

	ctx := context.Background()
	jobs := []batchv1.Job{job1, job2}

	result, err := reconciler.getConfigMapsBatch(ctx, jobs, scan)
	require.NoError(t, err)

	// Verify results
	assert.Len(t, result, 2)

	// Check job1 result (direct lookup should succeed)
	assert.True(t, result["test-job-1"].Found)
	assert.NoError(t, result["test-job-1"].Error)
	assert.NotNil(t, result["test-job-1"].ConfigMap)
	assert.Equal(t, "test-job-1", result["test-job-1"].ConfigMap.Name)

	// Check job2 result (label-based lookup should succeed)
	assert.True(t, result["test-job-2"].Found)
	assert.NoError(t, result["test-job-2"].Error)
	assert.NotNil(t, result["test-job-2"].ConfigMap)
	assert.Equal(t, "different-name", result["test-job-2"].ConfigMap.Name)
}

func TestBatchVsIndividualPerformance(t *testing.T) {
	scheme := runtime.NewScheme()
	require.NoError(t, complianceapi.AddToScheme(scheme))
	require.NoError(t, corev1.AddToScheme(scheme))
	require.NoError(t, batchv1.AddToScheme(scheme))

	// Create multiple test rules (simulate a larger scan)
	numRules := 20
	rules := make([]client.Object, numRules)
	ruleNames := make([]string, numRules)

	for i := 0; i < numRules; i++ {
		ruleName := fmt.Sprintf("test-rule-%d", i)
		ruleNames[i] = ruleName
		rules[i] = &complianceapi.Rule{
			ObjectMeta: metav1.ObjectMeta{
				Name:      ruleName,
				Namespace: "test-namespace",
			},
			Spec: complianceapi.RuleSpec{
				Severity: "medium",
			},
		}
	}

	client := fake.NewClientBuilder().
		WithScheme(scheme).
		WithObjects(rules...).
		Build()

	reconciler := &ScanReconciler{
		Client: client,
		Log:    zap.New(zap.UseDevMode(true)),
		Scheme: scheme,
		Config: DefaultConfig(),
	}

	ctx := context.Background()

	// Test batch approach
	start := time.Now()
	batchResult, err := reconciler.getRulesBatch(ctx, ruleNames, "test-namespace")
	batchDuration := time.Since(start)
	require.NoError(t, err)
	assert.Len(t, batchResult, numRules)

	// Test individual approach (simulating old behavior)
	start = time.Now()
	individualResults := make(map[string]*BatchRuleInfo)
	for _, ruleName := range ruleNames {
		var rule complianceapi.Rule
		err := reconciler.Get(ctx, types.NamespacedName{Name: ruleName, Namespace: "test-namespace"}, &rule)
		if err == nil {
			severity := "medium"
			if rule.Spec.Severity != "" {
				severity = rule.Spec.Severity
			}
			individualResults[ruleName] = &BatchRuleInfo{
				Rule:     &rule,
				Severity: severity,
				Found:    true,
			}
		}
	}
	individualDuration := time.Since(start)

	// Verify both approaches return the same results
	assert.Len(t, individualResults, numRules)
	for ruleName := range batchResult {
		assert.Equal(t, batchResult[ruleName].Severity, individualResults[ruleName].Severity)
		assert.Equal(t, batchResult[ruleName].Found, individualResults[ruleName].Found)
	}

	// Log performance comparison
	t.Logf("Performance comparison for %d rules:", numRules)
	t.Logf("  Batch approach:      %v", batchDuration)
	t.Logf("  Individual approach: %v", individualDuration)
	t.Logf("  Improvement ratio:   %.2fx", float64(individualDuration)/float64(batchDuration))

	// Batch should be faster (though in fake client it might not be significant)
	// This is more about demonstrating the concept
}

func TestBatchOperationConfiguration(t *testing.T) {
	scheme := runtime.NewScheme()
	require.NoError(t, complianceapi.AddToScheme(scheme))
	require.NoError(t, corev1.AddToScheme(scheme))
	require.NoError(t, batchv1.AddToScheme(scheme))

	client := fake.NewClientBuilder().WithScheme(scheme).Build()

	// Test with batch operations enabled
	configEnabled := DefaultConfig()
	configEnabled.BatchOperationEnabled = true

	reconcilerEnabled := &ScanReconciler{
		Client: client,
		Log:    zap.New(zap.UseDevMode(true)),
		Scheme: scheme,
		Config: configEnabled,
	}

	// Test with batch operations disabled
	configDisabled := DefaultConfig()
	configDisabled.BatchOperationEnabled = false

	reconcilerDisabled := &ScanReconciler{
		Client: client,
		Log:    zap.New(zap.UseDevMode(true)),
		Scheme: scheme,
		Config: configDisabled,
	}

	// Verify configuration
	assert.True(t, reconcilerEnabled.Config.BatchOperationEnabled)
	assert.False(t, reconcilerDisabled.Config.BatchOperationEnabled)
	assert.Equal(t, 50, reconcilerEnabled.Config.BatchSize)
	assert.Equal(t, 10*time.Second, reconcilerEnabled.Config.BatchTimeout)

	t.Logf("Batch operation configuration test passed")
	t.Logf("  Enabled reconciler batch setting: %v", reconcilerEnabled.Config.BatchOperationEnabled)
	t.Logf("  Disabled reconciler batch setting: %v", reconcilerDisabled.Config.BatchOperationEnabled)
	t.Logf("  Default batch size: %d", reconcilerEnabled.Config.BatchSize)
	t.Logf("  Default batch timeout: %v", reconcilerEnabled.Config.BatchTimeout)
}
