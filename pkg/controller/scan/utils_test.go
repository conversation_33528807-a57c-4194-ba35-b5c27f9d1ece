package scan

import (
	"context"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/runtime"
	"k8s.io/apimachinery/pkg/types"
	"sigs.k8s.io/controller-runtime/pkg/client/fake"
	"sigs.k8s.io/controller-runtime/pkg/log/zap"

	complianceapi "github.com/alauda/compliance-operator/pkg/apis/compliance/v1alpha1"
)

func TestValidateScanSpec(t *testing.T) {
	tests := []struct {
		name    string
		scan    *complianceapi.Scan
		wantErr bool
	}{
		{
			name: "valid scan",
			scan: &complianceapi.Scan{
				Spec: complianceapi.ScanSpec{
					Profile: "test-profile",
				},
			},
			wantErr: false,
		},
		{
			name: "missing profile",
			scan: &complianceapi.Scan{
				Spec: complianceapi.ScanSpec{},
			},
			wantErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := validateScanSpec(tt.scan)
			if tt.wantErr {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
			}
		})
	}
}

func TestUpdateScanStatusBatch(t *testing.T) {
	scheme := runtime.NewScheme()
	require.NoError(t, complianceapi.AddToScheme(scheme))

	scan := &complianceapi.Scan{
		ObjectMeta: metav1.ObjectMeta{
			Name:      "test-scan",
			Namespace: "test-namespace",
		},
		Spec: complianceapi.ScanSpec{
			Profile: "test-profile",
		},
		Status: complianceapi.ScanStatus{
			Phase: "Pending",
		},
	}

	client := fake.NewClientBuilder().
		WithScheme(scheme).
		WithObjects(scan).
		WithStatusSubresource(scan).
		Build()

	reconciler := &ScanReconciler{
		Client: client,
		Log:    zap.New(zap.UseDevMode(true)),
		Scheme: scheme,
		Config: DefaultConfig(),
	}

	ctx := context.Background()
	now := time.Now()
	updates := StatusUpdate{
		Phase:     "Running",
		Result:    "InProgress",
		Message:   "Scan is running",
		StartTime: &now,
	}

	err := reconciler.updateScanStatusBatch(ctx, scan, updates)
	require.NoError(t, err)

	// Verify the update
	var updatedScan complianceapi.Scan
	err = client.Get(ctx, types.NamespacedName{
		Name:      scan.Name,
		Namespace: scan.Namespace,
	}, &updatedScan)
	require.NoError(t, err)

	assert.Equal(t, "Running", updatedScan.Status.Phase)
	assert.Equal(t, "InProgress", updatedScan.Status.Result)
	assert.Equal(t, "Scan is running", updatedScan.Status.Message)
	assert.NotNil(t, updatedScan.Status.StartTime)
}

func TestDefaultConfig(t *testing.T) {
	config := DefaultConfig()
	
	assert.Greater(t, config.MaxConcurrentScans, 0)
	assert.Greater(t, config.DefaultScanTimeout, time.Duration(0))
	assert.Greater(t, config.RetryBackoff, time.Duration(0))
	assert.Greater(t, config.MaxHistoricalResults, int32(0))
	assert.Greater(t, config.ReportSizeLimit, int64(0))
	assert.Greater(t, config.CleanupInterval, time.Duration(0))
	assert.Greater(t, config.JobTTLSecondsAfterFinished, int32(0))
}

func TestShouldUsePVC(t *testing.T) {
	config := DefaultConfig()
	
	// Small report should use ConfigMap
	assert.False(t, config.ShouldUsePVC(1024)) // 1KB
	
	// Large report should use PVC
	assert.True(t, config.ShouldUsePVC(2*1024*1024)) // 2MB
}
