package scan

import (
	"context"
	"fmt"
	"time"

	"k8s.io/apimachinery/pkg/api/errors"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/types"
	"k8s.io/client-go/util/retry"
	ctrl "sigs.k8s.io/controller-runtime"

	complianceapi "github.com/alauda/compliance-operator/pkg/apis/compliance/v1alpha1"
	"github.com/robfig/cron/v3"
)

// updateScanStatus updates the scan status with retry logic
func (r *ScanReconciler) updateScanStatus(ctx context.Context, scan *complianceapi.Scan, phase, result, message string) (ctrl.Result, error) {
	r.Log.Info("Attempting to update scan status", "scan", scan.Name, "phase", phase, "result", result, "message", message)

	err := retry.RetryOnConflict(retry.DefaultRetry, func() error {
		// Get the latest version of the scan object
		var latestScan complianceapi.Scan
		if err := r.Get(ctx, types.NamespacedName{Name: scan.Name, Namespace: scan.Namespace}, &latestScan); err != nil {
			if errors.IsNotFound(err) {
				// Object was deleted, nothing to update
				r.Log.Info("Scan object not found during status update", "scan", scan.Name)
				return nil
			}
			r.Log.Error(err, "Failed to get latest scan object for status update", "scan", scan.Name)
			return err
		}

		r.Log.Info("Retrieved latest scan for status update", "scan", scan.Name, "currentPhase", latestScan.Status.Phase, "resourceVersion", latestScan.ResourceVersion)

		// Update the status on the latest version
		latestScan.Status.Phase = phase
		latestScan.Status.Result = result
		latestScan.Status.Message = message

		r.Log.Info("Attempting status update", "scan", scan.Name, "newPhase", phase, "resourceVersion", latestScan.ResourceVersion)

		// Try to update - RetryOnConflict will handle conflict errors automatically
		updateErr := r.Status().Update(ctx, &latestScan)
		if updateErr != nil {
			r.Log.Error(updateErr, "Status update failed", "scan", scan.Name, "errorType", errors.ReasonForError(updateErr))
		}
		return updateErr
	})

	if err != nil {
		if errors.IsNotFound(err) {
			// Object was deleted during update, nothing to update
			r.Log.Info("Scan object was deleted during status update", "scan", scan.Name)
			return ctrl.Result{}, nil
		}
		r.Log.Error(err, "Failed to update Scan status after retry", "scan", scan.Name)
		return ctrl.Result{}, err
	}

	// Success
	r.Log.Info("Successfully updated Scan status", "scan", scan.Name, "phase", phase, "result", result)
	return ctrl.Result{}, nil
}

// updateScanStatusWithStartTime updates the scan status and sets start time
func (r *ScanReconciler) updateScanStatusWithStartTime(ctx context.Context, scan *complianceapi.Scan, phase, result, message string) (ctrl.Result, error) {
	r.Log.Info("Attempting to update scan status with start time", "scan", scan.Name, "phase", phase, "result", result, "message", message)

	err := retry.RetryOnConflict(retry.DefaultRetry, func() error {
		// Get the latest version of the scan object
		var latestScan complianceapi.Scan
		if err := r.Get(ctx, types.NamespacedName{Name: scan.Name, Namespace: scan.Namespace}, &latestScan); err != nil {
			if errors.IsNotFound(err) {
				// Object was deleted, nothing to update
				r.Log.Info("Scan object not found during status update with start time", "scan", scan.Name)
				return nil
			}
			r.Log.Error(err, "Failed to get latest scan object for status update with start time", "scan", scan.Name)
			return err
		}

		r.Log.Info("Retrieved latest scan for status update with start time", "scan", scan.Name, "currentPhase", latestScan.Status.Phase, "resourceVersion", latestScan.ResourceVersion)

		// Update the status on the latest version
		latestScan.Status.Phase = phase
		latestScan.Status.Result = result
		latestScan.Status.Message = message

		// Set start time if not already set
		if latestScan.Status.StartTime == nil {
			now := metav1.Now()
			latestScan.Status.StartTime = &now
			r.Log.Info("Setting start time", "scan", scan.Name, "startTime", now)
		}

		r.Log.Info("Attempting status update with start time", "scan", scan.Name, "newPhase", phase, "resourceVersion", latestScan.ResourceVersion)

		// Try to update - RetryOnConflict will handle conflict errors automatically
		updateErr := r.Status().Update(ctx, &latestScan)
		if updateErr != nil {
			r.Log.Error(updateErr, "Status update with start time failed", "scan", scan.Name, "errorType", errors.ReasonForError(updateErr))
		}
		return updateErr
	})

	if err != nil {
		if errors.IsNotFound(err) {
			// Object was deleted during update, nothing to update
			r.Log.Info("Scan object was deleted during status update with start time", "scan", scan.Name)
			return ctrl.Result{}, nil
		}
		r.Log.Error(err, "Failed to update Scan status with start time after retry", "scan", scan.Name)
		return ctrl.Result{}, err
	}

	// Success
	r.Log.Info("Successfully updated Scan status with start time", "scan", scan.Name, "phase", phase, "result", result)
	return ctrl.Result{}, nil
}

// updateScanStatusWithEndTime updates the scan status and sets end time
func (r *ScanReconciler) updateScanStatusWithEndTime(ctx context.Context, scan *complianceapi.Scan, phase, result, message string) (ctrl.Result, error) {
	r.Log.Info("Attempting to update scan status with end time", "scan", scan.Name, "phase", phase, "result", result, "message", message)

	err := retry.RetryOnConflict(retry.DefaultRetry, func() error {
		// Get the latest version of the scan object
		var latestScan complianceapi.Scan
		if err := r.Get(ctx, types.NamespacedName{Name: scan.Name, Namespace: scan.Namespace}, &latestScan); err != nil {
			if errors.IsNotFound(err) {
				// Object was deleted, nothing to update
				r.Log.Info("Scan object not found during status update with end time", "scan", scan.Name)
				return nil
			}
			r.Log.Error(err, "Failed to get latest scan object for status update with end time", "scan", scan.Name)
			return err
		}

		r.Log.Info("Retrieved latest scan for status update with end time", "scan", scan.Name, "currentPhase", latestScan.Status.Phase, "resourceVersion", latestScan.ResourceVersion)

		// Update the status on the latest version
		latestScan.Status.Phase = phase
		latestScan.Status.Result = result
		latestScan.Status.Message = message

		// Set end time
		now := metav1.Now()
		latestScan.Status.EndTime = &now
		r.Log.Info("Setting end time", "scan", scan.Name, "endTime", now)

		r.Log.Info("Attempting status update with end time", "scan", scan.Name, "newPhase", phase, "resourceVersion", latestScan.ResourceVersion)

		// Try to update - RetryOnConflict will handle conflict errors automatically
		updateErr := r.Status().Update(ctx, &latestScan)
		if updateErr != nil {
			r.Log.Error(updateErr, "Status update with end time failed", "scan", scan.Name, "errorType", errors.ReasonForError(updateErr))
		}
		return updateErr
	})

	if err != nil {
		if errors.IsNotFound(err) {
			// Object was deleted during update, nothing to update
			r.Log.Info("Scan object was deleted during status update with end time", "scan", scan.Name)
			return ctrl.Result{}, nil
		}
		r.Log.Error(err, "Failed to update Scan status with end time after retry", "scan", scan.Name)
		return ctrl.Result{}, err
	}

	// Success
	r.Log.Info("Successfully updated Scan status with end time", "scan", scan.Name, "phase", phase, "result", result)
	return ctrl.Result{}, nil
}

// handleScanCompletion 处理扫描完成后的状态更新
func (r *ScanReconciler) handleScanCompletion(ctx context.Context, scan *complianceapi.Scan, scanID string, aggregatedResult *AggregatedResult) error {
	r.Log.Info("Handling scan completion", "scan", scan.Name, "scanID", scanID, "result", aggregatedResult.Result)

	// 更新扫描状态
	_, err := r.updateScanStatusWithEndTime(ctx, scan, "Done", aggregatedResult.Result, aggregatedResult.Message)
	return err
}

// initializeScanStatus 初始化扫描状态
func (r *ScanReconciler) initializeScanStatus(ctx context.Context, scan *complianceapi.Scan) error {
	r.Log.Info("Initializing scan status", "scan", scan.Name)

	// 更新扫描状态
	_, err := r.updateScanStatus(ctx, scan, "Pending", "Initializing", "Scan is being initialized")
	return err
}

// calculateNextScheduledTime 计算下一次定时扫描的时间
func (r *ScanReconciler) calculateNextScheduledTime(scan *complianceapi.Scan, now time.Time) (time.Time, error) {
	if scan.Spec.Schedule == "" {
		return time.Time{}, fmt.Errorf("scan does not have a schedule")
	}

	schedule, err := cron.ParseStandard(scan.Spec.Schedule)
	if err != nil {
		return time.Time{}, fmt.Errorf("failed to parse schedule %s: %v", scan.Spec.Schedule, err)
	}

	return schedule.Next(now), nil
}
