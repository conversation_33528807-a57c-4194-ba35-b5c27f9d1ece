package scan

import (
	"context"
	"fmt"
	"time"

	"k8s.io/apimachinery/pkg/api/errors"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	ctrl "sigs.k8s.io/controller-runtime"
	"sigs.k8s.io/controller-runtime/pkg/client"

	complianceapi "github.com/alauda/compliance-operator/pkg/apis/compliance/v1alpha1"
	"github.com/robfig/cron/v3"
)

// updateScanStatus updates the scan status with retry logic
func (r *ScanReconciler) updateScanStatus(ctx context.Context, scan *complianceapi.Scan, phase, result, message string) (ctrl.Result, error) {
	updates := StatusUpdate{
		Phase:   phase,
		Result:  result,
		Message: message,
	}

	if err := r.updateScanStatusBatch(ctx, scan, updates); err != nil {
		if errors.IsNotFound(err) {
			r.logScanEvent(scan, "Scan object was deleted during status update")
			return ctrl.Result{}, nil
		}
		r.logScanError(err, scan, "Failed to update scan status after retry")
		return ctrl.Result{}, err
	}

	r.logScanEvent(scan, "Successfully updated scan status", "phase", phase, "result", result)
	return ctrl.Result{}, nil
}

// updateScanStatusWithStartTime updates the scan status and sets start time
func (r *ScanReconciler) updateScanStatusWithStartTime(ctx context.Context, scan *complianceapi.Scan, phase, result, message string) (ctrl.Result, error) {
	now := time.Now()
	updates := StatusUpdate{
		Phase:     phase,
		Result:    result,
		Message:   message,
		StartTime: &now,
	}

	// Only set start time if not already set
	err := r.executeWithRetry(ctx, scan, func(ctx context.Context, obj client.Object) error {
		latestScan := obj.(*complianceapi.Scan)

		// Apply basic updates
		if updates.Phase != "" {
			latestScan.Status.Phase = updates.Phase
		}
		if updates.Result != "" {
			latestScan.Status.Result = updates.Result
		}
		if updates.Message != "" {
			latestScan.Status.Message = updates.Message
		}

		// Set start time only if not already set
		if latestScan.Status.StartTime == nil && updates.StartTime != nil {
			latestScan.Status.StartTime = &metav1.Time{Time: *updates.StartTime}
			r.logScanEvent(scan, "Setting start time", "startTime", updates.StartTime)
		}

		return r.Status().Update(ctx, latestScan)
	})

	if err != nil {
		if errors.IsNotFound(err) {
			r.logScanEvent(scan, "Scan object was deleted during status update with start time")
			return ctrl.Result{}, nil
		}
		r.logScanError(err, scan, "Failed to update scan status with start time after retry")
		return ctrl.Result{}, err
	}

	r.logScanEvent(scan, "Successfully updated scan status with start time", "phase", phase, "result", result)
	return ctrl.Result{}, nil
}

// updateScanStatusWithEndTime updates the scan status and sets end time
func (r *ScanReconciler) updateScanStatusWithEndTime(ctx context.Context, scan *complianceapi.Scan, phase, result, message string) (ctrl.Result, error) {
	now := time.Now()
	updates := StatusUpdate{
		Phase:   phase,
		Result:  result,
		Message: message,
		EndTime: &now,
	}

	if err := r.updateScanStatusBatch(ctx, scan, updates); err != nil {
		if errors.IsNotFound(err) {
			r.logScanEvent(scan, "Scan object was deleted during status update with end time")
			return ctrl.Result{}, nil
		}
		r.logScanError(err, scan, "Failed to update scan status with end time after retry")
		return ctrl.Result{}, err
	}

	// Register scan completion with memory manager and job manager
	if phase == "Done" || phase == "Error" {
		if r.MemoryManager != nil {
			r.MemoryManager.EndScan(scan.Name)
		}

		// Complete scan in job manager
		if r.JobManager != nil {
			success := (phase == "Done" && result != "Error")
			if err := r.JobManager.CompleteScan(ctx, scan.Name, success); err != nil {
				r.Log.Error(err, "Failed to complete scan in job manager", "scanName", scan.Name)
			}
		}

		// Schedule resource cleanup
		if r.ResourceCleanupManager != nil {
			immediate := (phase == "Error") // Clean up immediately for failed scans
			if err := r.ResourceCleanupManager.CleanupScanResources(ctx, scan.Name, scan.Namespace, immediate); err != nil {
				r.Log.Error(err, "Failed to schedule resource cleanup", "scanName", scan.Name)
			}
		}
	}

	r.logScanEvent(scan, "Successfully updated scan status with end time", "phase", phase, "result", result)
	return ctrl.Result{}, nil
}

// handleScanCompletion 处理扫描完成后的状态更新
func (r *ScanReconciler) handleScanCompletion(ctx context.Context, scan *complianceapi.Scan, scanID string, aggregatedResult *AggregatedResult) error {
	r.Log.Info("Handling scan completion", "scan", scan.Name, "scanID", scanID, "result", aggregatedResult.Result)

	// 更新扫描状态
	_, err := r.updateScanStatusWithEndTime(ctx, scan, "Done", aggregatedResult.Result, aggregatedResult.Message)
	return err
}

// initializeScanStatus 初始化扫描状态
func (r *ScanReconciler) initializeScanStatus(ctx context.Context, scan *complianceapi.Scan) error {
	r.Log.Info("Initializing scan status", "scan", scan.Name)

	// 更新扫描状态
	_, err := r.updateScanStatus(ctx, scan, "Pending", "Initializing", "Scan is being initialized")
	return err
}

// calculateNextScheduledTime 计算下一次定时扫描的时间
func (r *ScanReconciler) calculateNextScheduledTime(scan *complianceapi.Scan, now time.Time) (time.Time, error) {
	if scan.Spec.Schedule == "" {
		return time.Time{}, fmt.Errorf("scan does not have a schedule")
	}

	schedule, err := cron.ParseStandard(scan.Spec.Schedule)
	if err != nil {
		return time.Time{}, fmt.Errorf("failed to parse schedule %s: %v", scan.Spec.Schedule, err)
	}

	return schedule.Next(now), nil
}
