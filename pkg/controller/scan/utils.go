package scan

import (
	"context"
	"time"

	"k8s.io/apimachinery/pkg/api/errors"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/client-go/util/retry"
	"sigs.k8s.io/controller-runtime/pkg/client"

	complianceapi "github.com/alauda/compliance-operator/pkg/apis/compliance/v1alpha1"
)

// RetryableOperation defines a function that can be retried
type RetryableOperation func(ctx context.Context, obj client.Object) error

// executeWithRetry provides unified retry logic for object updates
func (r *ScanReconciler) executeWithRetry(ctx context.Context, obj client.Object, operation RetryableOperation) error {
	return retry.RetryOnConflict(retry.DefaultRetry, func() error {
		// Get the latest version of the object
		latest := obj.DeepCopyObject().(client.Object)
		if err := r.Get(ctx, client.ObjectKeyFromObject(obj), latest); err != nil {
			if errors.IsNotFound(err) {
				r.Log.Info("Object not found during retry operation", "object", client.ObjectKeyFromObject(obj))
				return nil
			}
			return err
		}
		return operation(ctx, latest)
	})
}

// logScanEvent provides structured logging for scan events
func (r *ScanReconciler) logScanEvent(scan *complianceapi.Scan, event string, fields ...interface{}) {
	baseFields := []interface{}{
		"scan", scan.Name,
		"profile", scan.Spec.Profile,
		"phase", scan.Status.Phase,
		"namespace", scan.Namespace,
	}
	allFields := append(baseFields, fields...)
	r.Log.Info(event, allFields...)
}

// logScanError provides structured error logging for scan events
func (r *ScanReconciler) logScanError(err error, scan *complianceapi.Scan, event string, fields ...interface{}) {
	baseFields := []interface{}{
		"scan", scan.Name,
		"profile", scan.Spec.Profile,
		"phase", scan.Status.Phase,
		"namespace", scan.Namespace,
	}
	allFields := append(baseFields, fields...)
	r.Log.Error(err, event, allFields...)
}

// StatusUpdate represents a batch of status updates
type StatusUpdate struct {
	Phase     string
	Result    string
	Message   string
	StartTime *time.Time
	EndTime   *time.Time
}

// updateScanStatusBatch provides unified status update with retry logic
func (r *ScanReconciler) updateScanStatusBatch(ctx context.Context, scan *complianceapi.Scan, updates StatusUpdate) error {
	r.logScanEvent(scan, "Attempting batch status update",
		"phase", updates.Phase,
		"result", updates.Result,
		"message", updates.Message)

	return r.executeWithRetry(ctx, scan, func(ctx context.Context, obj client.Object) error {
		latestScan := obj.(*complianceapi.Scan)

		// Apply updates
		if updates.Phase != "" {
			latestScan.Status.Phase = updates.Phase
		}
		if updates.Result != "" {
			latestScan.Status.Result = updates.Result
		}
		if updates.Message != "" {
			latestScan.Status.Message = updates.Message
		}
		if updates.StartTime != nil {
			latestScan.Status.StartTime = &metav1.Time{Time: *updates.StartTime}
		}
		if updates.EndTime != nil {
			latestScan.Status.EndTime = &metav1.Time{Time: *updates.EndTime}
		}

		return r.Status().Update(ctx, latestScan)
	})
}

// isRetryableError determines if an error should trigger a retry
func isRetryableError(err error) bool {
	if errors.IsConflict(err) || errors.IsServerTimeout(err) || errors.IsTimeout(err) {
		return true
	}
	// Add more retryable error conditions as needed
	return false
}

// validateScanSpec validates scan specification
func validateScanSpec(scan *complianceapi.Scan) error {
	if scan.Spec.Profile == "" {
		return errors.NewBadRequest("profile is required")
	}

	if scan.Spec.Schedule != "" {
		// Basic cron validation could be added here
		// For now, we'll rely on the cron library validation
	}

	return nil
}
