package scan

import (
	"context"
	"fmt"
	"time"

	batchv1 "k8s.io/api/batch/v1"
	corev1 "k8s.io/api/core/v1"
	"k8s.io/apimachinery/pkg/api/errors"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/client-go/util/retry"
	"sigs.k8s.io/controller-runtime/pkg/client"

	complianceapi "github.com/alauda/compliance-operator/pkg/apis/compliance/v1alpha1"
)

// RetryableOperation defines a function that can be retried
type RetryableOperation func(ctx context.Context, obj client.Object) error

// executeWithRetry provides unified retry logic for object updates
func (r *ScanReconciler) executeWithRetry(ctx context.Context, obj client.Object, operation RetryableOperation) error {
	return retry.RetryOnConflict(retry.DefaultRetry, func() error {
		// Get the latest version of the object
		latest := obj.DeepCopyObject().(client.Object)
		if err := r.Get(ctx, client.ObjectKeyFromObject(obj), latest); err != nil {
			if errors.IsNotFound(err) {
				r.Log.Info("Object not found during retry operation", "object", client.ObjectKeyFromObject(obj))
				return nil
			}
			return err
		}
		return operation(ctx, latest)
	})
}

// logScanEvent provides structured logging for scan events
func (r *ScanReconciler) logScanEvent(scan *complianceapi.Scan, event string, fields ...interface{}) {
	baseFields := []interface{}{
		"scan", scan.Name,
		"profile", scan.Spec.Profile,
		"phase", scan.Status.Phase,
		"namespace", scan.Namespace,
	}
	allFields := append(baseFields, fields...)
	r.Log.Info(event, allFields...)
}

// logScanError provides structured error logging for scan events
func (r *ScanReconciler) logScanError(err error, scan *complianceapi.Scan, event string, fields ...interface{}) {
	baseFields := []interface{}{
		"scan", scan.Name,
		"profile", scan.Spec.Profile,
		"phase", scan.Status.Phase,
		"namespace", scan.Namespace,
	}
	allFields := append(baseFields, fields...)
	r.Log.Error(err, event, allFields...)
}

// StatusUpdate represents a batch of status updates
type StatusUpdate struct {
	Phase     string
	Result    string
	Message   string
	StartTime *time.Time
	EndTime   *time.Time
}

// updateScanStatusBatch provides unified status update with retry logic
func (r *ScanReconciler) updateScanStatusBatch(ctx context.Context, scan *complianceapi.Scan, updates StatusUpdate) error {
	r.logScanEvent(scan, "Attempting batch status update",
		"phase", updates.Phase,
		"result", updates.Result,
		"message", updates.Message)

	return r.executeWithRetry(ctx, scan, func(ctx context.Context, obj client.Object) error {
		latestScan := obj.(*complianceapi.Scan)

		// Apply updates
		if updates.Phase != "" {
			latestScan.Status.Phase = updates.Phase
		}
		if updates.Result != "" {
			latestScan.Status.Result = updates.Result
		}
		if updates.Message != "" {
			latestScan.Status.Message = updates.Message
		}
		if updates.StartTime != nil {
			latestScan.Status.StartTime = &metav1.Time{Time: *updates.StartTime}
		}
		if updates.EndTime != nil {
			latestScan.Status.EndTime = &metav1.Time{Time: *updates.EndTime}
		}

		return r.Status().Update(ctx, latestScan)
	})
}

// isRetryableError determines if an error should trigger a retry
func isRetryableError(err error) bool {
	if errors.IsConflict(err) || errors.IsServerTimeout(err) || errors.IsTimeout(err) {
		return true
	}
	// Add more retryable error conditions as needed
	return false
}

// validateScanSpec validates scan specification
func validateScanSpec(scan *complianceapi.Scan) error {
	if scan.Spec.Profile == "" {
		return errors.NewBadRequest("profile is required")
	}

	if scan.Spec.Schedule != "" {
		// Basic cron validation could be added here
		// For now, we'll rely on the cron library validation
	}

	return nil
}

// BatchRuleInfo holds rule information for batch processing
type BatchRuleInfo struct {
	Rule     *complianceapi.Rule
	Severity string
	Found    bool
}

// getRulesBatch retrieves multiple rules in a single API call
func (r *ScanReconciler) getRulesBatch(ctx context.Context, ruleNames []string, namespace string) (map[string]*BatchRuleInfo, error) {
	if len(ruleNames) == 0 {
		return make(map[string]*BatchRuleInfo), nil
	}

	r.logScanEvent(&complianceapi.Scan{ObjectMeta: metav1.ObjectMeta{Namespace: namespace}},
		"Fetching rules in batch", "ruleCount", len(ruleNames))

	// Get all rules in the namespace
	var ruleList complianceapi.RuleList
	if err := r.List(ctx, &ruleList, client.InNamespace(namespace)); err != nil {
		return nil, fmt.Errorf("failed to list rules: %v", err)
	}

	// Create a map for quick lookup (use object pool if available)
	var ruleMap map[string]*complianceapi.Rule
	if r.Config != nil && r.Config.MemoryOptimizationEnabled && r.Pools != nil {
		// Use string slice pool for rule names processing
		pooledSlice := r.Pools.GetStringSlice()
		defer r.Pools.PutStringSlice(pooledSlice)

		ruleMap = make(map[string]*complianceapi.Rule, len(ruleList.Items))
	} else {
		ruleMap = make(map[string]*complianceapi.Rule)
	}

	for i := range ruleList.Items {
		ruleMap[ruleList.Items[i].Name] = &ruleList.Items[i]
	}

	// Build result map
	result := make(map[string]*BatchRuleInfo, len(ruleNames))
	for _, ruleName := range ruleNames {
		if rule, exists := ruleMap[ruleName]; exists {
			severity := "medium" // default
			if rule.Spec.Severity != "" {
				severity = rule.Spec.Severity
			}
			result[ruleName] = &BatchRuleInfo{
				Rule:     rule,
				Severity: severity,
				Found:    true,
			}
		} else {
			result[ruleName] = &BatchRuleInfo{
				Rule:     nil,
				Severity: "medium", // default
				Found:    false,
			}
		}
	}

	r.logScanEvent(&complianceapi.Scan{ObjectMeta: metav1.ObjectMeta{Namespace: namespace}},
		"Batch rule fetch completed", "requested", len(ruleNames), "found", len(result))

	return result, nil
}

// BatchConfigMapResult holds ConfigMap lookup result
type BatchConfigMapResult struct {
	ConfigMap *corev1.ConfigMap
	Found     bool
	Error     error
}

// getConfigMapsBatch retrieves multiple ConfigMaps efficiently
func (r *ScanReconciler) getConfigMapsBatch(ctx context.Context, jobs []batchv1.Job, scan *complianceapi.Scan) (map[string]*BatchConfigMapResult, error) {
	if len(jobs) == 0 {
		return make(map[string]*BatchConfigMapResult), nil
	}

	r.logScanEvent(scan, "Fetching ConfigMaps in batch", "jobCount", len(jobs))

	result := make(map[string]*BatchConfigMapResult)

	// Strategy 1: Try direct lookup for all jobs first (most efficient)
	directLookupSuccess := 0
	for _, job := range jobs {
		configMap := &corev1.ConfigMap{}
		err := r.Get(ctx, client.ObjectKey{
			Name:      job.Name,
			Namespace: job.Namespace,
		}, configMap)

		if err == nil {
			// Verify the ConfigMap belongs to this job
			expectedScan := job.Labels["compliance-operator.alauda.io/scan"]
			expectedRule := job.Labels["compliance-operator.alauda.io/rule"]

			if r.verifyConfigMapMatch(configMap, &job, expectedScan, expectedRule) {
				result[job.Name] = &BatchConfigMapResult{
					ConfigMap: configMap,
					Found:     true,
					Error:     nil,
				}
				directLookupSuccess++
				continue
			}
		}

		// Mark for label-based lookup
		result[job.Name] = &BatchConfigMapResult{
			ConfigMap: nil,
			Found:     false,
			Error:     err,
		}
	}

	r.logScanEvent(scan, "Direct ConfigMap lookup completed",
		"total", len(jobs), "directSuccess", directLookupSuccess)

	// Strategy 2: For failed direct lookups, use label-based search
	if directLookupSuccess < len(jobs) {
		// Get all ConfigMaps with scan label for batch processing
		var allConfigMaps corev1.ConfigMapList
		if err := r.List(ctx, &allConfigMaps,
			client.InNamespace(scan.Namespace),
			client.MatchingLabels{"compliance-operator.alauda.io/scan": scan.Name}); err != nil {
			return nil, fmt.Errorf("failed to list ConfigMaps for batch processing: %v", err)
		}

		// Create lookup map by job name and labels
		configMapsByJob := make(map[string]*corev1.ConfigMap)
		for i := range allConfigMaps.Items {
			cm := &allConfigMaps.Items[i]
			if jobName := cm.Labels["compliance-operator.alauda.io/job"]; jobName != "" {
				configMapsByJob[jobName] = cm
			}
		}

		// Update results for jobs that failed direct lookup
		labelLookupSuccess := 0
		for _, job := range jobs {
			if result[job.Name].Found {
				continue // Already found via direct lookup
			}

			if configMap, exists := configMapsByJob[job.Name]; exists {
				expectedScan := job.Labels["compliance-operator.alauda.io/scan"]
				expectedRule := job.Labels["compliance-operator.alauda.io/rule"]

				if r.verifyConfigMapMatch(configMap, &job, expectedScan, expectedRule) {
					result[job.Name] = &BatchConfigMapResult{
						ConfigMap: configMap,
						Found:     true,
						Error:     nil,
					}
					labelLookupSuccess++
				}
			}
		}

		r.logScanEvent(scan, "Label-based ConfigMap lookup completed",
			"labelSuccess", labelLookupSuccess,
			"totalFound", directLookupSuccess+labelLookupSuccess)
	}

	return result, nil
}
