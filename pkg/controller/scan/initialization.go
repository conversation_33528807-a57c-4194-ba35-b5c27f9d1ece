package scan

import (
	"context"
	"fmt"
	"math/rand"
	"os"
	"regexp"
	"strings"
	"time"

	batchv1 "k8s.io/api/batch/v1"
	corev1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/utils/pointer"
	ctrl "sigs.k8s.io/controller-runtime"
	"sigs.k8s.io/controller-runtime/pkg/client"
	"sigs.k8s.io/controller-runtime/pkg/controller/controllerutil"

	complianceapi "github.com/alauda/compliance-operator/pkg/apis/compliance/v1alpha1"
)

// generateJobName generates a complete job name with random suffix
func generateJobName(checkType, ruleName, nodeName string) string {
	// Kubernetes resource names must be <= 63 characters
	// We need to leave space for our own random suffix (6 chars: -xxxxx)
	maxLength := 57 // 63 - 6 for suffix

	// Clean rule name: remove invalid characters
	cleanRuleName := cleanKubernetesName(ruleName)

	var baseName string
	switch checkType {
	case "platform":
		baseName = cleanRuleName
	case "node":
		if nodeName != "" {
			// Clean node name: remove invalid characters
			cleanNodeName := cleanKubernetesName(nodeName)
			baseName = fmt.Sprintf("%s-%s", cleanRuleName, cleanNodeName)
		} else {
			baseName = cleanRuleName
		}
	default:
		baseName = cleanRuleName
	}

	// Truncate if too long
	if len(baseName) > maxLength {
		baseName = baseName[:maxLength]
	}

	// Ensure it ends with alphanumeric character (Kubernetes requirement)
	baseName = strings.TrimRight(baseName, "-")

	// Generate random suffix
	rand.Seed(time.Now().UnixNano())
	const charset = "abcdefghijklmnopqrstuvwxyz0123456789"
	suffix := make([]byte, 5)
	for i := range suffix {
		suffix[i] = charset[rand.Intn(len(charset))]
	}

	return fmt.Sprintf("%s-%s", baseName, string(suffix))
}

// cleanKubernetesName cleans a string to be valid for Kubernetes resource names
// Kubernetes names must be lowercase alphanumeric characters or '-', and must start and end with alphanumeric
func cleanKubernetesName(name string) string {
	// Convert to lowercase
	cleaned := strings.ToLower(name)

	// Replace invalid characters with dash
	cleaned = regexp.MustCompile(`[^a-z0-9-]`).ReplaceAllString(cleaned, "-")

	// Remove consecutive dashes
	cleaned = regexp.MustCompile(`-+`).ReplaceAllString(cleaned, "-")

	// Ensure it starts with alphanumeric
	cleaned = regexp.MustCompile(`^[^a-z0-9]*`).ReplaceAllString(cleaned, "")

	// Ensure it ends with alphanumeric
	cleaned = regexp.MustCompile(`[^a-z0-9]*$`).ReplaceAllString(cleaned, "")

	// If empty after cleaning, provide a default
	if cleaned == "" {
		cleaned = "default"
	}

	return cleaned
}

// getImageRegistry returns the image registry from environment variable or default
func (r *ScanReconciler) getImageRegistry() string {
	if registry := os.Getenv("IMAGE_REGISTRY"); registry != "" {
		return registry
	}
	// Use proxy registry for K8s cluster access
	return "registry.alauda.cn:60070/test/compliance"
}

// getUnifiedScannerImage returns the unified scanner image path
func (r *ScanReconciler) getUnifiedScannerImage() string {
	if image := os.Getenv("UNIFIED_SCANNER_IMAGE"); image != "" {
		return image
	}
	// Fallback to constructing from registry + default tag
	return r.getImageRegistry() + "/unified-scanner:exit-code-fix6"
}

// getScannerImage returns the unified scanner image (backward compatibility)
func (r *ScanReconciler) getScannerImage() string {
	// Try SCANNER_IMAGE first for backward compatibility
	if image := os.Getenv("SCANNER_IMAGE"); image != "" {
		return image
	}
	return r.getUnifiedScannerImage()
}

// getNodeScannerImage returns the unified scanner image (backward compatibility)
func (r *ScanReconciler) getNodeScannerImage() string {
	return r.getUnifiedScannerImage()
}

// getOpenSCAPScannerImage returns the OpenSCAP scanner image path
func (r *ScanReconciler) getOpenSCAPScannerImage() string {
	if image := os.Getenv("OPENSCAP_SCANNER_IMAGE"); image != "" {
		return image
	}
	// Fallback to constructing from registry + default tag
	return r.getImageRegistry() + "/openscap-scanner:latest"
}

// getContentImage returns the OS content image path
func (r *ScanReconciler) getContentImage() string {
	if image := os.Getenv("CONTENT_IMAGE"); image != "" {
		return image
	}
	// Fallback to constructing from registry + default tag
	return r.getImageRegistry() + "/os-content:latest"
}

// getContentExtractorImage returns the content extractor image path
func (r *ScanReconciler) getContentExtractorImage() string {
	if image := os.Getenv("CONTENT_EXTRACTOR_IMAGE"); image != "" {
		return image
	}
	// Fallback to constructing from registry + default tag
	return r.getImageRegistry() + "/content-extractor:latest"
}

// getReportServiceImage returns the report service image path
func (r *ScanReconciler) getReportServiceImage() string {
	if image := os.Getenv("REPORT_SERVICE_IMAGE"); image != "" {
		return image
	}
	// Fallback to constructing from registry + default tag
	return r.getImageRegistry() + "/openscap-report-service:latest"
}

// initializeScan initializes a new scan
func (r *ScanReconciler) initializeScan(ctx context.Context, scan *complianceapi.Scan) (ctrl.Result, error) {
	log := r.Log.WithValues("scan", scan.Name, "namespace", scan.Namespace)
	log.Info("Initializing scan")

	// Register scan start with memory manager
	if r.MemoryManager != nil {
		r.MemoryManager.StartScan(scan.Name)
	}

	// 获取当前的 scanID
	scanID := ""
	if scan.Annotations != nil {
		scanID = scan.Annotations["compliance-operator.alauda.io/current-scan-id"]
	}

	if scanID == "" {
		// 如果没有 scanID，生成一个新的
		scanID = generateScanID(scan)
		log.Info("Generated new scanID for scan", "scanID", scanID)

		// 将 scanID 存储在注解中
		if scan.Annotations == nil {
			scan.Annotations = make(map[string]string)
		}
		scan.Annotations["compliance-operator.alauda.io/current-scan-id"] = scanID

		// 更新 Scan 对象
		if err := r.Update(ctx, scan); err != nil {
			log.Error(err, "Failed to update Scan with scanID")
			return ctrl.Result{}, err
		}

		// 重新入队等待更新后的对象
		return ctrl.Result{Requeue: true}, nil
	}

	log.Info("Using scanID for scan initialization", "scanID", scanID)

	// Get the profile
	var profile complianceapi.Profile
	if err := r.Get(ctx, client.ObjectKey{
		Name:      scan.Spec.Profile,
		Namespace: scan.Namespace,
	}, &profile); err != nil {
		scan.Status.Phase = "Error"
		scan.Status.Message = fmt.Sprintf("Failed to get profile: %v", err)
		if updateErr := r.Status().Update(ctx, scan); updateErr != nil {
			log.Error(updateErr, "Failed to update Scan status")
		}
		return ctrl.Result{}, err
	}

	// Check if jobs already exist (avoid duplicate creation)
	var existingJobs batchv1.JobList
	if err := r.List(ctx, &existingJobs, client.InNamespace(scan.Namespace), client.MatchingLabels{
		"compliance-operator.alauda.io/scan":    scan.Name,
		"compliance-operator.alauda.io/scan-id": scanID,
	}); err != nil {
		log.Error(err, "Failed to list existing jobs")
		return ctrl.Result{}, fmt.Errorf("failed to check existing jobs: %v", err)
	}

	if len(existingJobs.Items) > 0 {
		log.Info("Jobs already exist for scan, skipping creation", "scan", scan.Name, "scanID", scanID, "existingJobs", len(existingJobs.Items))
		// Update status to RUNNING since jobs already exist
		// 使用带重试机制的辅助方法更新状态
		if _, err := r.updateScanStatusWithStartTime(ctx, scan, "Running", "", "Scan jobs already created and running"); err != nil {
			log.Error(err, "Failed to update Scan status")
			return ctrl.Result{}, err
		}
		return ctrl.Result{RequeueAfter: 10 * time.Second}, nil
	}

	// Start the scan (create jobs)
	if err := r.startScan(ctx, scan, &profile, scanID); err != nil {
		log.Error(err, "Failed to start scan")
		// 使用带重试机制的辅助方法更新错误状态
		if _, updateErr := r.updateScanStatus(ctx, scan, "Error", "Error", fmt.Sprintf("Failed to start scan: %v", err)); updateErr != nil {
			log.Error(updateErr, "Failed to update Scan status")
		}
		return ctrl.Result{}, err
	}

	// Update status to RUNNING only after jobs are successfully created
	log.Info("Scan jobs created successfully, updating status to Running", "scan", scan.Name, "scanID", scanID)
	// 使用带重试机制的辅助方法更新状态为运行中
	if _, err := r.updateScanStatusWithStartTime(ctx, scan, "Running", "", "Scan jobs created and running"); err != nil {
		log.Error(err, "Failed to update Scan status")
		return ctrl.Result{}, err
	}

	return ctrl.Result{RequeueAfter: 10 * time.Second}, nil
}

// startScan starts the scan by creating appropriate jobs
func (r *ScanReconciler) startScan(ctx context.Context, scan *complianceapi.Scan, profile *complianceapi.Profile, scanID string) error {
	log := r.Log.WithValues("scan", scan.Name, "namespace", scan.Namespace, "scanID", scanID)
	log.Info("Starting scan")

	// Check if this is an OpenSCAP datastream profile
	if profile.Spec.DataStream != nil {
		log.Info("Detected OpenSCAP datastream profile, using OpenSCAP scanner", "contentFile", profile.Spec.DataStream.ContentFile)
		return r.createOpenSCAPScanJobs(ctx, scan, profile, scanID)
	}

	// Original logic for rule-based profiles
	// Separate rules by type
	var platformRules, nodeRules []complianceapi.Rule

	for _, ruleRef := range profile.Spec.Rules {
		var rule complianceapi.Rule
		if err := r.Get(ctx, client.ObjectKey{
			Name:      ruleRef.Name,
			Namespace: scan.Namespace,
		}, &rule); err != nil {
			log.Error(err, "Failed to get rule", "rule", ruleRef.Name)
			continue
		}

		switch rule.Spec.CheckType {
		case "platform":
			platformRules = append(platformRules, rule)
		case "node":
			nodeRules = append(nodeRules, rule)
		default:
			log.Info("Unknown check type, defaulting to platform", "rule", rule.Name, "checkType", rule.Spec.CheckType)
			platformRules = append(platformRules, rule)
		}
	}

	log.Info("Scan rules categorized", "platformRules", len(platformRules), "nodeRules", len(nodeRules))

	// 创建平台扫描任务
	if len(platformRules) > 0 && (scan.Spec.ScanType == "platform" || scan.Spec.ScanType == "all") {
		if err := r.createPlatformScanJobs(ctx, scan, platformRules, scanID); err != nil {
			return fmt.Errorf("failed to create platform scan jobs: %v", err)
		}
	}

	// 创建节点扫描任务
	if len(nodeRules) > 0 && (scan.Spec.ScanType == "node" || scan.Spec.ScanType == "all") {
		if err := r.createNodeScanJobs(ctx, scan, nodeRules, scanID); err != nil {
			return fmt.Errorf("failed to create node scan jobs: %v", err)
		}
	}

	return nil
}

// createPlatformScanJobs creates jobs for platform-level compliance checks
func (r *ScanReconciler) createPlatformScanJobs(ctx context.Context, scan *complianceapi.Scan, rules []complianceapi.Rule, scanID string) error {
	r.Log.Info("Creating platform scan jobs", "scan", scan.Name, "totalRules", len(rules))

	jobsCreated := 0
	for _, rule := range rules {
		if rule.Spec.CheckType != "platform" {
			continue
		}

		// Generate complete job name with random suffix
		jobName := generateJobName("platform", rule.Name, "")

		job := &batchv1.Job{
			ObjectMeta: metav1.ObjectMeta{
				Name:      jobName,
				Namespace: scan.Namespace,
				Labels: map[string]string{
					"compliance-operator.alauda.io/scan":      scan.Name,
					"compliance-operator.alauda.io/scan-type": "platform",
					"compliance-operator.alauda.io/rule":      rule.Name,
					"compliance-operator.alauda.io/scan-id":   scanID,
					"compliance-operator.alauda.io/temporary": "true",
				},
			},
			Spec: batchv1.JobSpec{
				// TTLSecondsAfterFinished: pointer.Int32Ptr(300), // Disabled for debugging
				Template: corev1.PodTemplateSpec{
					ObjectMeta: metav1.ObjectMeta{
						Labels: map[string]string{
							"compliance-operator.alauda.io/scan":      scan.Name,
							"compliance-operator.alauda.io/scan-type": "platform",
							"compliance-operator.alauda.io/rule":      rule.Name,
							"compliance-operator.alauda.io/scan-id":   scanID,
							"compliance-operator.alauda.io/temporary": "true",
						},
					},
					Spec: corev1.PodSpec{
						RestartPolicy:      corev1.RestartPolicyNever,
						ServiceAccountName: "compliance-scanner",
						Containers: []corev1.Container{
							{
								Name:            "scanner",
								Image:           r.getScannerImage(),
								ImagePullPolicy: corev1.PullAlways,
								Command:         []string{"/usr/local/bin/unified-scanner.sh"},
								Args:            []string{rule.Spec.CheckScript},
								Env: []corev1.EnvVar{
									{Name: "RULE_ID", Value: rule.Name},
									{Name: "CHECK_TYPE", Value: "platform"},
									{Name: "SCAN_NAME", Value: scan.Name},
									{Name: "NAMESPACE", Value: scan.Namespace},
									{Name: "JOB_NAME", Value: jobName},
									{Name: "SCAN_ID", Value: scanID},
								},
								VolumeMounts: []corev1.VolumeMount{
									{
										Name:      "results",
										MountPath: "/tmp/results",
									},
									{
										Name:      "host-proc",
										MountPath: "/host/proc",
										ReadOnly:  true,
									},
									{
										Name:      "host-etc-kubernetes",
										MountPath: "/host/etc/kubernetes",
										ReadOnly:  true,
									},
								},
							},
						},
						Volumes: []corev1.Volume{
							{
								Name: "results",
								VolumeSource: corev1.VolumeSource{
									EmptyDir: &corev1.EmptyDirVolumeSource{},
								},
							},
							{
								Name: "host-proc",
								VolumeSource: corev1.VolumeSource{
									HostPath: &corev1.HostPathVolumeSource{
										Path: "/proc",
									},
								},
							},
							{
								Name: "host-etc-kubernetes",
								VolumeSource: corev1.VolumeSource{
									HostPath: &corev1.HostPathVolumeSource{
										Path: "/etc/kubernetes",
									},
								},
							},
						},
					},
				},
			},
		}

		// Set owner reference
		if err := controllerutil.SetControllerReference(scan, job, r.Scheme); err != nil {
			return err
		}

		if err := r.Create(ctx, job); err != nil {
			return err
		}

		jobsCreated++
		r.Log.Info("Created platform scan job", "scan", scan.Name, "rule", rule.Name, "jobName", job.Name, "scanID", scanID)
	}

	r.Log.Info("Platform scan jobs creation completed", "scan", scan.Name, "jobsCreated", jobsCreated)
	return nil
}

// createNodeScanJobs creates jobs for node-level compliance checks
func (r *ScanReconciler) createNodeScanJobs(ctx context.Context, scan *complianceapi.Scan, rules []complianceapi.Rule, scanID string) error {
	// Get nodes that match the selector
	var nodes corev1.NodeList
	if err := r.List(ctx, &nodes, client.MatchingLabels(scan.Spec.NodeSelector)); err != nil {
		return err
	}

	r.Log.Info("Creating node scan jobs", "scan", scan.Name, "totalRules", len(rules), "matchingNodes", len(nodes.Items))

	jobsCreated := 0

	for _, rule := range rules {
		if rule.Spec.CheckType != "node" {
			continue
		}

		for _, node := range nodes.Items {
			// Generate complete job name with random suffix
			jobName := generateJobName("node", rule.Name, node.Name)

			job := &batchv1.Job{
				ObjectMeta: metav1.ObjectMeta{
					Name:      jobName,
					Namespace: scan.Namespace,
					Labels: map[string]string{
						"compliance-operator.alauda.io/scan":      scan.Name,
						"compliance-operator.alauda.io/scan-type": "node",
						"compliance-operator.alauda.io/rule":      rule.Name,
						"compliance-operator.alauda.io/node":      node.Name,
						"compliance-operator.alauda.io/scan-id":   scanID,
						"compliance-operator.alauda.io/temporary": "true",
					},
				},
				Spec: batchv1.JobSpec{
					// TTLSecondsAfterFinished: pointer.Int32Ptr(300), // Disabled for debugging
					Template: corev1.PodTemplateSpec{
						ObjectMeta: metav1.ObjectMeta{
							Labels: map[string]string{
								"compliance-operator.alauda.io/scan":      scan.Name,
								"compliance-operator.alauda.io/scan-type": "node",
								"compliance-operator.alauda.io/rule":      rule.Name,
								"compliance-operator.alauda.io/node":      node.Name,
								"compliance-operator.alauda.io/scan-id":   scanID,
								"compliance-operator.alauda.io/temporary": "true",
							},
						},
						Spec: corev1.PodSpec{
							RestartPolicy:      corev1.RestartPolicyNever,
							ServiceAccountName: "compliance-scanner",
							NodeName:           node.Name,
							HostNetwork:        true,
							HostPID:            true,
							HostIPC:            true,
							SecurityContext: &corev1.PodSecurityContext{
								RunAsUser: pointer.Int64Ptr(0),
							},
							Containers: []corev1.Container{
								{
									Name:            "node-scanner",
									Image:           r.getNodeScannerImage(),
									ImagePullPolicy: corev1.PullAlways,
									Command:         []string{"/usr/local/bin/unified-scanner.sh"},
									Args:            []string{rule.Spec.CheckScript},
									SecurityContext: &corev1.SecurityContext{
										Privileged: pointer.BoolPtr(true),
									},
									VolumeMounts: []corev1.VolumeMount{
										{
											Name:      "host-root",
											MountPath: "/host",
											ReadOnly:  true,
										},
										{
											Name:      "results",
											MountPath: "/tmp/results",
										},
									},
									Env: []corev1.EnvVar{
										{Name: "NODE_NAME", Value: node.Name},
										{Name: "RULE_ID", Value: rule.Name},
										{Name: "CHECK_TYPE", Value: "node"},
										{Name: "SCAN_NAME", Value: scan.Name},
										{Name: "NAMESPACE", Value: scan.Namespace},
										{Name: "JOB_NAME", Value: jobName},
										{Name: "SCAN_ID", Value: scanID},
									},
								},
							},
							Volumes: []corev1.Volume{
								{
									Name: "host-root",
									VolumeSource: corev1.VolumeSource{
										HostPath: &corev1.HostPathVolumeSource{
											Path: "/",
										},
									},
								},
								{
									Name: "results",
									VolumeSource: corev1.VolumeSource{
										EmptyDir: &corev1.EmptyDirVolumeSource{},
									},
								},
							},
							Tolerations: []corev1.Toleration{
								{
									Operator: corev1.TolerationOpExists,
								},
							},
						},
					},
				},
			}

			// Set owner reference
			if err := controllerutil.SetControllerReference(scan, job, r.Scheme); err != nil {
				return err
			}

			if err := r.Create(ctx, job); err != nil {
				return err
			}

			jobsCreated++
			r.Log.Info("Created node scan job", "scan", scan.Name, "node", node.Name, "rule", rule.Name, "jobName", job.Name, "scanID", scanID)
		}
	}

	r.Log.Info("Node scan jobs creation completed", "scan", scan.Name, "jobsCreated", jobsCreated)
	return nil
}

// createOpenSCAPScanJobs creates OpenSCAP scanner jobs based on datastream profile
func (r *ScanReconciler) createOpenSCAPScanJobs(ctx context.Context, scan *complianceapi.Scan, profile *complianceapi.Profile, scanID string) error {
	log := r.Log.WithValues("scan", scan.Name, "namespace", scan.Namespace, "scanID", scanID)
	log.Info("Creating OpenSCAP scanner jobs", "dataStream", profile.Spec.DataStream.ContentFile)

	// Determine scan type from profile or scan spec
	scanType := profile.Spec.DataStream.ScanType
	if scanType == "" {
		scanType = scan.Spec.ScanType
	}
	if scanType == "" {
		scanType = "both" // Default to both if not specified
	}

	jobsCreated := 0

	// Create platform scan job if needed
	if scanType == "platform" || scanType == "both" {
		if err := r.createOpenSCAPPlatformJob(ctx, scan, profile, scanID); err != nil {
			return fmt.Errorf("failed to create OpenSCAP platform job: %v", err)
		}
		jobsCreated++
	}

	// Create node scan jobs if needed
	if scanType == "node" || scanType == "both" {
		nodeJobsCount, err := r.createOpenSCAPNodeJobs(ctx, scan, profile, scanID)
		if err != nil {
			return fmt.Errorf("failed to create OpenSCAP node jobs: %v", err)
		}
		jobsCreated += nodeJobsCount
	}

	log.Info("OpenSCAP scanner jobs creation completed", "scan", scan.Name, "jobsCreated", jobsCreated)
	return nil
}

// createOpenSCAPPlatformJob creates a platform-level OpenSCAP scan job
func (r *ScanReconciler) createOpenSCAPPlatformJob(ctx context.Context, scan *complianceapi.Scan, profile *complianceapi.Profile, scanID string) error {
	jobName := generateJobName("openscap-platform", scan.Name, "")

	job := &batchv1.Job{
		ObjectMeta: metav1.ObjectMeta{
			Name:      jobName,
			Namespace: scan.Namespace,
			Labels: map[string]string{
				"compliance-operator.alauda.io/scan":      scan.Name,
				"compliance-operator.alauda.io/scan-type": "platform",
				"compliance-operator.alauda.io/scan-id":   scanID,
				"compliance-operator.alauda.io/scanner":   "openscap",
				"compliance-operator.alauda.io/profile":   profile.Name,
				"compliance-operator.alauda.io/temporary": "true",
			},
		},
		Spec: batchv1.JobSpec{
			Template: corev1.PodTemplateSpec{
				ObjectMeta: metav1.ObjectMeta{
					Labels: map[string]string{
						"compliance-operator.alauda.io/scan":      scan.Name,
						"compliance-operator.alauda.io/scan-type": "platform",
						"compliance-operator.alauda.io/scan-id":   scanID,
						"compliance-operator.alauda.io/scanner":   "openscap",
						"compliance-operator.alauda.io/profile":   profile.Name,
						"compliance-operator.alauda.io/temporary": "true",
					},
				},
				Spec: corev1.PodSpec{
					RestartPolicy:      corev1.RestartPolicyNever,
					ServiceAccountName: "compliance-scanner",
					InitContainers: []corev1.Container{
						{
							Name:            "content-extractor",
							Image:           r.getContentImage(),
							ImagePullPolicy: corev1.PullAlways,
							Command:         []string{"/bin/sh", "-c"},
							Args: []string{
								"echo 'Extracting content files...' && " +
									"cp -v /content/*.xml /shared-content/ && " +
									"ls -la /shared-content/ && " +
									"echo 'Content extraction completed'",
							},
							VolumeMounts: []corev1.VolumeMount{
								{
									Name:      "shared-content",
									MountPath: "/shared-content",
								},
							},
							SecurityContext: &corev1.SecurityContext{
								RunAsUser:                pointer.Int64Ptr(0),
								RunAsGroup:               pointer.Int64Ptr(0),
								AllowPrivilegeEscalation: pointer.BoolPtr(false),
								ReadOnlyRootFilesystem:   pointer.BoolPtr(false),
								Capabilities: &corev1.Capabilities{
									Drop: []corev1.Capability{"ALL"},
								},
							},
						},
					},
					Containers: []corev1.Container{
						{
							Name:            "openscap-scanner",
							Image:           r.getOpenSCAPScannerImage(),
							ImagePullPolicy: corev1.PullAlways,
							Env: []corev1.EnvVar{
								{Name: "PROFILE", Value: profile.Spec.DataStream.ProfileID},
								{Name: "CONTENT", Value: profile.Spec.DataStream.ContentFile},
								{Name: "NODE_NAME", Value: "platform"},
								{Name: "SCAN_ID", Value: scanID},
								{Name: "SCAN_NAME", Value: scan.Name},
								{Name: "JOB_NAME", Value: jobName},
								{Name: "NAMESPACE", Value: scan.Namespace},
								{Name: "HOSTROOT", Value: "/host"},
								{Name: "REPORT_DIR", Value: "/reports"},
								{Name: "CONTENT_IMAGE", Value: r.getContentImage()},
								{Name: "OPENSCAP_REPORT_SERVICE_URL", Value: "http://openscap-report-service.compliance-system.svc.cluster.local:8080"},
							},
							VolumeMounts: []corev1.VolumeMount{
								{
									Name:      "shared-content",
									MountPath: "/shared-content",
									ReadOnly:  true,
								},
								{
									Name:      "host-proc",
									MountPath: "/host/proc",
									ReadOnly:  true,
								},
								{
									Name:      "host-etc-kubernetes",
									MountPath: "/host/etc/kubernetes",
									ReadOnly:  true,
								},
								{
									Name:      "reports",
									MountPath: "/reports",
								},
							},
							SecurityContext: &corev1.SecurityContext{
								RunAsUser:                pointer.Int64Ptr(0),
								RunAsGroup:               pointer.Int64Ptr(0),
								Privileged:               pointer.BoolPtr(false),
								AllowPrivilegeEscalation: pointer.BoolPtr(false),
								ReadOnlyRootFilesystem:   pointer.BoolPtr(false),
								Capabilities: &corev1.Capabilities{
									Add: []corev1.Capability{"DAC_OVERRIDE", "FOWNER"},
								},
							},
						},
					},
					Volumes: []corev1.Volume{
						{
							Name: "shared-content",
							VolumeSource: corev1.VolumeSource{
								EmptyDir: &corev1.EmptyDirVolumeSource{},
							},
						},
						{
							Name: "host-proc",
							VolumeSource: corev1.VolumeSource{
								HostPath: &corev1.HostPathVolumeSource{
									Path: "/proc",
								},
							},
						},
						{
							Name: "host-etc-kubernetes",
							VolumeSource: corev1.VolumeSource{
								HostPath: &corev1.HostPathVolumeSource{
									Path: "/etc/kubernetes",
								},
							},
						},
						{
							Name: "reports",
							VolumeSource: corev1.VolumeSource{
								EmptyDir: &corev1.EmptyDirVolumeSource{},
							},
						},
					},
				},
			},
		},
	}

	// Set owner reference
	if err := controllerutil.SetControllerReference(scan, job, r.Scheme); err != nil {
		return err
	}

	if err := r.Create(ctx, job); err != nil {
		return err
	}

	r.Log.Info("Created OpenSCAP platform scan job", "scan", scan.Name, "jobName", job.Name, "scanID", scanID)
	return nil
}

// createOpenSCAPNodeJobs creates node-level OpenSCAP scan jobs
func (r *ScanReconciler) createOpenSCAPNodeJobs(ctx context.Context, scan *complianceapi.Scan, profile *complianceapi.Profile, scanID string) (int, error) {
	// Get nodes that match the selector
	var nodes corev1.NodeList
	if err := r.List(ctx, &nodes, client.MatchingLabels(scan.Spec.NodeSelector)); err != nil {
		return 0, err
	}

	r.Log.Info("Creating OpenSCAP node scan jobs", "scan", scan.Name, "matchingNodes", len(nodes.Items))

	jobsCreated := 0

	for _, node := range nodes.Items {
		jobName := generateJobName("openscap-node", scan.Name, node.Name)

		job := &batchv1.Job{
			ObjectMeta: metav1.ObjectMeta{
				Name:      jobName,
				Namespace: scan.Namespace,
				Labels: map[string]string{
					"compliance-operator.alauda.io/scan":      scan.Name,
					"compliance-operator.alauda.io/scan-type": "node",
					"compliance-operator.alauda.io/scan-id":   scanID,
					"compliance-operator.alauda.io/scanner":   "openscap",
					"compliance-operator.alauda.io/profile":   profile.Name,
					"compliance-operator.alauda.io/node":      node.Name,
					"compliance-operator.alauda.io/temporary": "true",
				},
			},
			Spec: batchv1.JobSpec{
				Template: corev1.PodTemplateSpec{
					ObjectMeta: metav1.ObjectMeta{
						Labels: map[string]string{
							"compliance-operator.alauda.io/scan":      scan.Name,
							"compliance-operator.alauda.io/scan-type": "node",
							"compliance-operator.alauda.io/scan-id":   scanID,
							"compliance-operator.alauda.io/scanner":   "openscap",
							"compliance-operator.alauda.io/profile":   profile.Name,
							"compliance-operator.alauda.io/node":      node.Name,
							"compliance-operator.alauda.io/temporary": "true",
						},
					},
					Spec: corev1.PodSpec{
						RestartPolicy:      corev1.RestartPolicyNever,
						ServiceAccountName: "compliance-scanner",
						NodeName:           node.Name,
						HostNetwork:        false,
						HostPID:            false,
						SecurityContext: &corev1.PodSecurityContext{
							RunAsUser:  pointer.Int64Ptr(0),
							RunAsGroup: pointer.Int64Ptr(0),
							FSGroup:    pointer.Int64Ptr(0),
						},
						InitContainers: []corev1.Container{
							{
								Name:            "content-extractor",
								Image:           r.getContentImage(),
								ImagePullPolicy: corev1.PullAlways,
								Command:         []string{"/bin/sh", "-c"},
								Args: []string{
									"echo 'Extracting content files...' && " +
										"cp -v /content/*.xml /shared-content/ && " +
										"ls -la /shared-content/ && " +
										"echo 'Content extraction completed'",
								},
								VolumeMounts: []corev1.VolumeMount{
									{
										Name:      "shared-content",
										MountPath: "/shared-content",
									},
								},
								SecurityContext: &corev1.SecurityContext{
									RunAsUser:                pointer.Int64Ptr(0),
									RunAsGroup:               pointer.Int64Ptr(0),
									AllowPrivilegeEscalation: pointer.BoolPtr(false),
									ReadOnlyRootFilesystem:   pointer.BoolPtr(false),
									Capabilities: &corev1.Capabilities{
										Drop: []corev1.Capability{"ALL"},
									},
								},
							},
						},
						Containers: []corev1.Container{
							{
								Name:            "openscap-scanner",
								Image:           r.getOpenSCAPScannerImage(),
								ImagePullPolicy: corev1.PullAlways,
								Env: []corev1.EnvVar{
									{Name: "PROFILE", Value: profile.Spec.DataStream.ProfileID},
									{Name: "CONTENT", Value: profile.Spec.DataStream.ContentFile},
									{Name: "NODE_NAME", Value: node.Name},
									{Name: "SCAN_ID", Value: scanID},
									{Name: "SCAN_NAME", Value: scan.Name},
									{Name: "JOB_NAME", Value: jobName},
									{Name: "NAMESPACE", Value: scan.Namespace},
									{Name: "HOSTROOT", Value: "/host"},
									{Name: "REPORT_DIR", Value: "/reports"},
									{Name: "CONTENT_IMAGE", Value: r.getContentImage()},
									{Name: "OPENSCAP_REPORT_SERVICE_URL", Value: "http://openscap-report-service.compliance-system.svc.cluster.local:8080"},
								},
								VolumeMounts: []corev1.VolumeMount{
									{
										Name:      "shared-content",
										MountPath: "/shared-content",
										ReadOnly:  true,
									},
									{
										Name:      "host-root",
										MountPath: "/host",
										ReadOnly:  true,
									},
									{
										Name:      "reports",
										MountPath: "/reports",
									},
								},
								SecurityContext: &corev1.SecurityContext{
									RunAsUser:                pointer.Int64Ptr(0),
									RunAsGroup:               pointer.Int64Ptr(0),
									Privileged:               pointer.BoolPtr(true),
									AllowPrivilegeEscalation: pointer.BoolPtr(true),
									ReadOnlyRootFilesystem:   pointer.BoolPtr(false),
									Capabilities: &corev1.Capabilities{
										Add: []corev1.Capability{"SYS_ADMIN", "DAC_OVERRIDE", "FOWNER", "SETUID", "SETGID"},
									},
								},
							},
						},
						Volumes: []corev1.Volume{
							{
								Name: "shared-content",
								VolumeSource: corev1.VolumeSource{
									EmptyDir: &corev1.EmptyDirVolumeSource{},
								},
							},
							{
								Name: "host-root",
								VolumeSource: corev1.VolumeSource{
									HostPath: &corev1.HostPathVolumeSource{
										Path: "/",
										Type: (*corev1.HostPathType)(pointer.StringPtr("Directory")),
									},
								},
							},
							{
								Name: "reports",
								VolumeSource: corev1.VolumeSource{
									EmptyDir: &corev1.EmptyDirVolumeSource{},
								},
							},
						},
					},
				},
			},
		}

		// Set owner reference
		if err := controllerutil.SetControllerReference(scan, job, r.Scheme); err != nil {
			return jobsCreated, err
		}

		if err := r.Create(ctx, job); err != nil {
			return jobsCreated, err
		}

		jobsCreated++
		r.Log.Info("Created OpenSCAP node scan job", "scan", scan.Name, "node", node.Name, "jobName", job.Name, "scanID", scanID)
	}

	r.Log.Info("OpenSCAP node scan jobs creation completed", "scan", scan.Name, "jobsCreated", jobsCreated)
	return jobsCreated, nil
}
