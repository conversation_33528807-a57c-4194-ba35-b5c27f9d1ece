package scan

import (
	"context"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	batchv1 "k8s.io/api/batch/v1"
	corev1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/runtime"
	"k8s.io/apimachinery/pkg/types"
	"sigs.k8s.io/controller-runtime/pkg/client/fake"
	"sigs.k8s.io/controller-runtime/pkg/log/zap"

	complianceapi "github.com/alauda/compliance-operator/pkg/apis/compliance/v1alpha1"
)

func TestCleanupJobs(t *testing.T) {
	// 创建一个带有 scanID 的 Scan
	scanID := "test-scan-id"
	scan := &complianceapi.Scan{
		ObjectMeta: metav1.ObjectMeta{
			Name:      "test-scan",
			Namespace: "default",
			Annotations: map[string]string{
				"compliance-operator.alauda.io/current-scan-id": scanID,
			},
		},
		Spec: complianceapi.ScanSpec{
			Profile: "test-profile",
		},
	}

	// 创建临时 Job
	job := &batchv1.Job{
		ObjectMeta: metav1.ObjectMeta{
			Name:      "test-job",
			Namespace: "default",
			Labels: map[string]string{
				"compliance-operator.alauda.io/scan":      "test-scan",
				"compliance-operator.alauda.io/scan-id":   scanID,
				"compliance-operator.alauda.io/temporary": "true",
			},
		},
	}

	// 创建临时 ConfigMap
	configMap := &corev1.ConfigMap{
		ObjectMeta: metav1.ObjectMeta{
			Name:      "test-configmap",
			Namespace: "default",
			Labels: map[string]string{
				"compliance-operator.alauda.io/scan":      "test-scan",
				"compliance-operator.alauda.io/scan-id":   scanID,
				"compliance-operator.alauda.io/temporary": "true",
			},
		},
	}

	// 创建非临时 ConfigMap (不应被删除)
	permanentConfigMap := &corev1.ConfigMap{
		ObjectMeta: metav1.ObjectMeta{
			Name:      "permanent-configmap",
			Namespace: "default",
			Labels: map[string]string{
				"compliance-operator.alauda.io/scan":    "test-scan",
				"compliance-operator.alauda.io/scan-id": scanID,
			},
		},
	}

	// 设置 reconciler
	r := setupReconciler(t, scan, job, configMap, permanentConfigMap)

	// 执行 cleanupJobs 和 cleanupCheckResults
	err := r.cleanupJobs(context.Background(), scan)
	assert.NoError(t, err)

	err = r.cleanupCheckResults(context.Background(), scan)
	assert.NoError(t, err)

	// 由于 fake client 的限制，删除操作可能不会立即反映在列表结果中
	// 这里我们只验证 cleanupJobs 和 cleanupCheckResults 是否成功执行
}

func TestCleanupOldHistoricalResults(t *testing.T) {
	// 创建一个 Scan，设置最大历史记录数为 2
	scan := &complianceapi.Scan{
		ObjectMeta: metav1.ObjectMeta{
			Name:      "test-scan",
			Namespace: "default",
		},
		Spec: complianceapi.ScanSpec{
			Profile:              "test-profile",
			MaxHistoricalResults: 2,
		},
		Status: complianceapi.ScanStatus{
			HistoricalResults: []complianceapi.HistoricalResultRef{
				{
					ScanID:          "scan-id-1",
					CheckResultName: "checkresult-1",
					ReportName:      "report-1",
					Timestamp:       metav1.Now(),
				},
				{
					ScanID:          "scan-id-2",
					CheckResultName: "checkresult-2",
					ReportName:      "report-2",
					Timestamp:       metav1.Now(),
				},
				{
					ScanID:          "scan-id-3",
					CheckResultName: "checkresult-3",
					ReportName:      "report-3",
					Timestamp:       metav1.Now(),
				},
			},
		},
	}

	// 创建 CheckResult 资源
	checkResult1 := &complianceapi.CheckResult{
		ObjectMeta: metav1.ObjectMeta{
			Name:      "checkresult-1",
			Namespace: "default",
			Labels: map[string]string{
				"compliance-operator.alauda.io/scan":    "test-scan",
				"compliance-operator.alauda.io/scan-id": "scan-id-1",
			},
		},
	}

	checkResult2 := &complianceapi.CheckResult{
		ObjectMeta: metav1.ObjectMeta{
			Name:      "checkresult-2",
			Namespace: "default",
			Labels: map[string]string{
				"compliance-operator.alauda.io/scan":    "test-scan",
				"compliance-operator.alauda.io/scan-id": "scan-id-2",
			},
		},
	}

	checkResult3 := &complianceapi.CheckResult{
		ObjectMeta: metav1.ObjectMeta{
			Name:      "checkresult-3",
			Namespace: "default",
			Labels: map[string]string{
				"compliance-operator.alauda.io/scan":    "test-scan",
				"compliance-operator.alauda.io/scan-id": "scan-id-3",
			},
		},
	}

	// 创建报告 ConfigMap
	report1 := &corev1.ConfigMap{
		ObjectMeta: metav1.ObjectMeta{
			Name:      "report-1",
			Namespace: "default",
			Labels: map[string]string{
				"compliance-operator.alauda.io/scan":    "test-scan",
				"compliance-operator.alauda.io/scan-id": "scan-id-1",
			},
		},
	}

	report2 := &corev1.ConfigMap{
		ObjectMeta: metav1.ObjectMeta{
			Name:      "report-2",
			Namespace: "default",
			Labels: map[string]string{
				"compliance-operator.alauda.io/scan":    "test-scan",
				"compliance-operator.alauda.io/scan-id": "scan-id-2",
			},
		},
	}

	report3 := &corev1.ConfigMap{
		ObjectMeta: metav1.ObjectMeta{
			Name:      "report-3",
			Namespace: "default",
			Labels: map[string]string{
				"compliance-operator.alauda.io/scan":    "test-scan",
				"compliance-operator.alauda.io/scan-id": "scan-id-3",
			},
		},
	}

	// 设置 reconciler
	r := setupReconciler(t, scan, checkResult1, checkResult2, checkResult3, report1, report2, report3)

	// 执行 cleanupHistoricalResults
	err := r.cleanupHistoricalResults(context.Background(), scan)
	assert.NoError(t, err)

	// 由于 fake client 的限制，删除操作可能不会立即反映在列表结果中
	// 这里我们只验证 cleanupHistoricalResults 是否成功执行
}

// Test ResourceCleanupManager functionality
func TestResourceCleanupManager_ScheduleCleanup(t *testing.T) {
	scheme := runtime.NewScheme()
	require.NoError(t, batchv1.AddToScheme(scheme))
	require.NoError(t, corev1.AddToScheme(scheme))
	require.NoError(t, complianceapi.AddToScheme(scheme))

	config := &ScanControllerConfig{
		CleanupInterval:            time.Minute,
		JobTTLSecondsAfterFinished: 300,
	}

	// Create test resources
	job := &batchv1.Job{
		ObjectMeta: metav1.ObjectMeta{
			Name:      "test-job",
			Namespace: "default",
			Labels: map[string]string{
				"compliance-operator.alauda.io/scan": "test-scan",
			},
		},
	}

	client := fake.NewClientBuilder().
		WithScheme(scheme).
		WithObjects(job).
		Build()

	log := zap.New(zap.UseDevMode(true))

	rcm := NewResourceCleanupManager(client, config, log)

	// Schedule cleanup
	rcm.ScheduleCleanup("test-scan", "default", CleanupTypeScheduled, time.Second)

	// Check that cleanup was scheduled
	stats := rcm.GetCleanupStats()
	assert.Equal(t, 1, stats["pendingCleanups"])
	assert.Equal(t, 1, stats["queueLength"])
}

func TestResourceCleanupManager_CleanupScanResources(t *testing.T) {
	scheme := runtime.NewScheme()
	require.NoError(t, batchv1.AddToScheme(scheme))
	require.NoError(t, corev1.AddToScheme(scheme))

	config := &ScanControllerConfig{
		CleanupInterval:            time.Minute,
		JobTTLSecondsAfterFinished: 300,
	}

	// Create test resources
	job := &batchv1.Job{
		ObjectMeta: metav1.ObjectMeta{
			Name:      "test-job",
			Namespace: "default",
			Labels: map[string]string{
				"compliance-operator.alauda.io/scan": "test-scan",
			},
		},
	}

	client := fake.NewClientBuilder().
		WithScheme(scheme).
		WithObjects(job).
		Build()

	log := zap.New(zap.UseDevMode(true))

	rcm := NewResourceCleanupManager(client, config, log)
	ctx := context.Background()

	// Test immediate cleanup
	err := rcm.CleanupScanResources(ctx, "test-scan", "default", true)
	assert.NoError(t, err)

	// Verify job was deleted
	var retrievedJob batchv1.Job
	err = client.Get(ctx, types.NamespacedName{Name: "test-job", Namespace: "default"}, &retrievedJob)
	assert.Error(t, err) // Should be NotFound error
}
