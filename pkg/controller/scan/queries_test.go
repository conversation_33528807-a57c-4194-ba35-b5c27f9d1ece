package scan

import (
	"context"
	"testing"

	"github.com/stretchr/testify/assert"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"

	complianceapi "github.com/alauda/compliance-operator/pkg/apis/compliance/v1alpha1"
)

func TestListProfileRules(t *testing.T) {
	// 创建测试资源
	profile := &complianceapi.Profile{
		ObjectMeta: metav1.ObjectMeta{
			Name:      "test-profile",
			Namespace: "default",
		},
		Spec: complianceapi.ProfileSpec{
			Title:       "Test Profile",
			Description: "Profile for testing",
			Rules: []complianceapi.RuleReference{
				{Name: "test-rule-1"},
				{Name: "test-rule-2"},
				{Name: "test-rule-3"},
			},
		},
	}

	rule1 := &complianceapi.Rule{
		ObjectMeta: metav1.ObjectMeta{
			Name:      "test-rule-1",
			Namespace: "default",
		},
		Spec: complianceapi.RuleSpec{
			Title:       "Test Rule 1",
			Description: "Rule for testing",
			CheckType:   "platform",
			CheckScript: "echo 'Test script 1'",
		},
	}

	rule2 := &complianceapi.Rule{
		ObjectMeta: metav1.ObjectMeta{
			Name:      "test-rule-2",
			Namespace: "default",
		},
		Spec: complianceapi.RuleSpec{
			Title:       "Test Rule 2",
			Description: "Rule for testing",
			CheckType:   "node",
			CheckScript: "echo 'Test script 2'",
		},
	}

	rule3 := &complianceapi.Rule{
		ObjectMeta: metav1.ObjectMeta{
			Name:      "test-rule-3",
			Namespace: "default",
		},
		Spec: complianceapi.RuleSpec{
			Title:       "Test Rule 3",
			Description: "Rule for testing",
			CheckType:   "platform",
			CheckScript: "echo 'Test script 3'",
		},
	}

	// 创建一个 Scan
	scan := &complianceapi.Scan{
		ObjectMeta: metav1.ObjectMeta{
			Name:      "test-scan",
			Namespace: "default",
		},
		Spec: complianceapi.ScanSpec{
			Profile: "test-profile",
		},
	}

	// 设置 reconciler
	r := setupReconciler(t, scan, profile, rule1, rule2, rule3)

	// 执行 listProfileRules
	rules, err := r.listProfileRules(context.Background(), scan)

	// 验证结果
	assert.NoError(t, err)
	assert.Equal(t, 3, len(rules))

	// 验证规则内容
	ruleNames := make(map[string]bool)
	for _, rule := range rules {
		ruleNames[rule.Name] = true

		// 验证规则内容
		if rule.Name == "test-rule-1" {
			assert.Equal(t, "Test Rule 1", rule.Spec.Title)
			assert.Equal(t, "platform", rule.Spec.CheckType)
		} else if rule.Name == "test-rule-2" {
			assert.Equal(t, "Test Rule 2", rule.Spec.Title)
			assert.Equal(t, "node", rule.Spec.CheckType)
		} else if rule.Name == "test-rule-3" {
			assert.Equal(t, "Test Rule 3", rule.Spec.Title)
			assert.Equal(t, "platform", rule.Spec.CheckType)
		}
	}

	// 验证所有规则都被找到
	assert.True(t, ruleNames["test-rule-1"])
	assert.True(t, ruleNames["test-rule-2"])
	assert.True(t, ruleNames["test-rule-3"])
}

func TestGetProfile(t *testing.T) {
	// 创建测试资源
	profile := &complianceapi.Profile{
		ObjectMeta: metav1.ObjectMeta{
			Name:      "test-profile",
			Namespace: "default",
		},
		Spec: complianceapi.ProfileSpec{
			Title:       "Test Profile",
			Description: "Profile for testing",
			Rules: []complianceapi.RuleReference{
				{Name: "test-rule-1"},
				{Name: "test-rule-2"},
			},
		},
	}

	// 创建一个 Scan
	scan := &complianceapi.Scan{
		ObjectMeta: metav1.ObjectMeta{
			Name:      "test-scan",
			Namespace: "default",
		},
		Spec: complianceapi.ScanSpec{
			Profile: "test-profile",
		},
	}

	// 设置 reconciler
	r := setupReconciler(t, scan, profile)

	// 执行 getProfile
	retrievedProfile, err := r.getProfile(context.Background(), scan)

	// 验证结果
	assert.NoError(t, err)
	assert.NotNil(t, retrievedProfile)
	assert.Equal(t, "test-profile", retrievedProfile.Name)
	assert.Equal(t, "Test Profile", retrievedProfile.Spec.Title)
	assert.Equal(t, "Profile for testing", retrievedProfile.Spec.Description)
	assert.Equal(t, 2, len(retrievedProfile.Spec.Rules))
}

func TestGetRule(t *testing.T) {
	// 创建测试资源
	rule := &complianceapi.Rule{
		ObjectMeta: metav1.ObjectMeta{
			Name:      "test-rule",
			Namespace: "default",
		},
		Spec: complianceapi.RuleSpec{
			Title:       "Test Rule",
			Description: "Rule for testing",
			CheckType:   "platform",
			CheckScript: "echo 'Test script'",
			Severity:    "high",
		},
	}

	// 设置 reconciler
	r := setupReconciler(t, rule)

	// 执行 getRule
	retrievedRule, err := r.getRule(context.Background(), "test-rule", "default")

	// 验证结果
	assert.NoError(t, err)
	assert.NotNil(t, retrievedRule)
	assert.Equal(t, "test-rule", retrievedRule.Name)
	assert.Equal(t, "Test Rule", retrievedRule.Spec.Title)
	assert.Equal(t, "Rule for testing", retrievedRule.Spec.Description)
	assert.Equal(t, "platform", retrievedRule.Spec.CheckType)
	assert.Equal(t, "echo 'Test script'", retrievedRule.Spec.CheckScript)
	assert.Equal(t, "high", retrievedRule.Spec.Severity)
}
