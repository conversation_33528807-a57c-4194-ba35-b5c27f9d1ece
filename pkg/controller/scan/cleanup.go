package scan

import (
	"context"
	"fmt"
	"sync"
	"time"

	batchv1 "k8s.io/api/batch/v1"
	corev1 "k8s.io/api/core/v1"
	"k8s.io/apimachinery/pkg/api/errors"
	"k8s.io/apimachinery/pkg/types"
	ctrl "sigs.k8s.io/controller-runtime"
	"sigs.k8s.io/controller-runtime/pkg/client"
	"sigs.k8s.io/controller-runtime/pkg/controller/controllerutil"

	complianceapi "github.com/alauda/compliance-operator/pkg/apis/compliance/v1alpha1"
	"github.com/go-logr/logr"
)

// ResourceCleanupManager manages automatic cleanup of scan resources
type ResourceCleanupManager struct {
	client client.Client
	config *ScanControllerConfig
	log    logr.Logger
	mu     sync.RWMutex

	// Cleanup tracking
	pendingCleanups map[string]*CleanupTask
	cleanupQueue    []*CleanupTask
}

// CleanupTask represents a scheduled cleanup task
type CleanupTask struct {
	ScanName      string
	ScanNamespace string
	ScheduledTime time.Time
	TaskType      CleanupType
	Resources     []ResourceRef
	Retries       int
	MaxRetries    int
}

// CleanupType represents the type of cleanup
type CleanupType string

const (
	CleanupTypeImmediate CleanupType = "Immediate" // Clean up immediately after scan completion
	CleanupTypeScheduled CleanupType = "Scheduled" // Clean up after TTL
	CleanupTypeOrphaned  CleanupType = "Orphaned"  // Clean up orphaned resources
)

// ResourceRef represents a reference to a resource to be cleaned up
type ResourceRef struct {
	Kind      string
	Name      string
	Namespace string
}

// NewResourceCleanupManager creates a new resource cleanup manager
func NewResourceCleanupManager(client client.Client, config *ScanControllerConfig, log logr.Logger) *ResourceCleanupManager {
	rcm := &ResourceCleanupManager{
		client:          client,
		config:          config,
		log:             log,
		pendingCleanups: make(map[string]*CleanupTask),
		cleanupQueue:    make([]*CleanupTask, 0),
	}

	// Start cleanup scheduler
	go rcm.startCleanupScheduler()

	return rcm
}

// startCleanupScheduler runs the cleanup scheduler
func (rcm *ResourceCleanupManager) startCleanupScheduler() {
	ticker := time.NewTicker(rcm.config.CleanupInterval)
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			rcm.processCleanupQueue()
			rcm.cleanupOrphanedResources()
		}
	}
}

// ScheduleCleanup schedules cleanup for a scan
func (rcm *ResourceCleanupManager) ScheduleCleanup(scanName, scanNamespace string, cleanupType CleanupType, delay time.Duration) {
	rcm.mu.Lock()
	defer rcm.mu.Unlock()

	task := &CleanupTask{
		ScanName:      scanName,
		ScanNamespace: scanNamespace,
		ScheduledTime: time.Now().Add(delay),
		TaskType:      cleanupType,
		Resources:     []ResourceRef{},
		Retries:       0,
		MaxRetries:    3,
	}

	// Discover resources to clean up
	rcm.discoverResources(task)

	// Add to pending cleanups
	rcm.pendingCleanups[scanName] = task
	rcm.cleanupQueue = append(rcm.cleanupQueue, task)

	rcm.log.Info("Cleanup scheduled",
		"scanName", scanName,
		"cleanupType", cleanupType,
		"scheduledTime", task.ScheduledTime,
		"resourceCount", len(task.Resources))
}

// discoverResources discovers resources associated with a scan
func (rcm *ResourceCleanupManager) discoverResources(task *CleanupTask) {
	ctx := context.Background()

	// Find Jobs
	var jobs batchv1.JobList
	if err := rcm.client.List(ctx, &jobs,
		client.InNamespace(task.ScanNamespace),
		client.MatchingLabels{"compliance-operator.alauda.io/scan": task.ScanName}); err == nil {

		for _, job := range jobs.Items {
			task.Resources = append(task.Resources, ResourceRef{
				Kind:      "Job",
				Name:      job.Name,
				Namespace: job.Namespace,
			})
		}
	}

	// Find ConfigMaps
	var configMaps corev1.ConfigMapList
	if err := rcm.client.List(ctx, &configMaps,
		client.InNamespace(task.ScanNamespace),
		client.MatchingLabels{"compliance-operator.alauda.io/scan": task.ScanName}); err == nil {

		for _, cm := range configMaps.Items {
			task.Resources = append(task.Resources, ResourceRef{
				Kind:      "ConfigMap",
				Name:      cm.Name,
				Namespace: cm.Namespace,
			})
		}
	}

	// Find Pods (for orphaned cleanup)
	var pods corev1.PodList
	if err := rcm.client.List(ctx, &pods,
		client.InNamespace(task.ScanNamespace),
		client.MatchingLabels{"compliance-operator.alauda.io/scan": task.ScanName}); err == nil {

		for _, pod := range pods.Items {
			task.Resources = append(task.Resources, ResourceRef{
				Kind:      "Pod",
				Name:      pod.Name,
				Namespace: pod.Namespace,
			})
		}
	}
}

// processCleanupQueue processes the cleanup queue
func (rcm *ResourceCleanupManager) processCleanupQueue() {
	rcm.mu.Lock()
	defer rcm.mu.Unlock()

	now := time.Now()
	var remainingTasks []*CleanupTask

	for _, task := range rcm.cleanupQueue {
		if now.After(task.ScheduledTime) {
			// Time to execute cleanup
			rcm.executeCleanupTask(task)
		} else {
			// Keep in queue
			remainingTasks = append(remainingTasks, task)
		}
	}

	rcm.cleanupQueue = remainingTasks
}

// executeCleanupTask executes a cleanup task
func (rcm *ResourceCleanupManager) executeCleanupTask(task *CleanupTask) {
	ctx := context.Background()

	rcm.log.Info("Executing cleanup task",
		"scanName", task.ScanName,
		"taskType", task.TaskType,
		"resourceCount", len(task.Resources),
		"attempt", task.Retries+1)

	successCount := 0
	for _, resource := range task.Resources {
		if err := rcm.deleteResource(ctx, resource); err != nil {
			rcm.log.Error(err, "Failed to delete resource",
				"kind", resource.Kind,
				"name", resource.Name,
				"namespace", resource.Namespace)
		} else {
			successCount++
		}
	}

	if successCount == len(task.Resources) {
		// All resources cleaned up successfully
		delete(rcm.pendingCleanups, task.ScanName)
		rcm.log.Info("Cleanup task completed successfully",
			"scanName", task.ScanName,
			"resourcesDeleted", successCount)
	} else if task.Retries < task.MaxRetries {
		// Retry failed cleanup
		task.Retries++
		task.ScheduledTime = time.Now().Add(time.Duration(task.Retries) * time.Minute)
		rcm.cleanupQueue = append(rcm.cleanupQueue, task)
		rcm.log.Info("Cleanup task rescheduled for retry",
			"scanName", task.ScanName,
			"attempt", task.Retries,
			"nextAttempt", task.ScheduledTime)
	} else {
		// Max retries exceeded
		delete(rcm.pendingCleanups, task.ScanName)
		rcm.log.Error(nil, "Cleanup task failed after max retries",
			"scanName", task.ScanName,
			"maxRetries", task.MaxRetries)
	}
}

// deleteResource deletes a specific resource
func (rcm *ResourceCleanupManager) deleteResource(ctx context.Context, resource ResourceRef) error {
	switch resource.Kind {
	case "Job":
		job := &batchv1.Job{}
		job.Name = resource.Name
		job.Namespace = resource.Namespace
		return rcm.client.Delete(ctx, job)

	case "ConfigMap":
		cm := &corev1.ConfigMap{}
		cm.Name = resource.Name
		cm.Namespace = resource.Namespace
		return rcm.client.Delete(ctx, cm)

	case "Pod":
		pod := &corev1.Pod{}
		pod.Name = resource.Name
		pod.Namespace = resource.Namespace
		return rcm.client.Delete(ctx, pod)

	default:
		return fmt.Errorf("unsupported resource kind: %s", resource.Kind)
	}
}

// cleanupOrphanedResources cleans up orphaned resources
func (rcm *ResourceCleanupManager) cleanupOrphanedResources() {
	ctx := context.Background()

	// Find orphaned Jobs (older than 2 hours without corresponding scan)
	var jobs batchv1.JobList
	if err := rcm.client.List(ctx, &jobs,
		client.MatchingLabels{"compliance-operator.alauda.io/scan": ""}); err == nil {

		for _, job := range jobs.Items {
			if job.CreationTimestamp.Time.Before(time.Now().Add(-2 * time.Hour)) {
				// Check if scan still exists
				scanName := job.Labels["compliance-operator.alauda.io/scan"]
				if scanName != "" {
					var scan complianceapi.Scan
					err := rcm.client.Get(ctx, types.NamespacedName{
						Name:      scanName,
						Namespace: job.Namespace,
					}, &scan)

					if errors.IsNotFound(err) {
						// Scan doesn't exist, job is orphaned
						rcm.log.Info("Found orphaned job", "jobName", job.Name, "scanName", scanName)
						rcm.ScheduleCleanup(scanName, job.Namespace, CleanupTypeOrphaned, 0)
					}
				}
			}
		}
	}
}

// CleanupScanResources immediately cleans up resources for a completed scan
func (rcm *ResourceCleanupManager) CleanupScanResources(ctx context.Context, scanName, scanNamespace string, immediate bool) error {
	if immediate {
		// Immediate cleanup
		task := &CleanupTask{
			ScanName:      scanName,
			ScanNamespace: scanNamespace,
			ScheduledTime: time.Now(),
			TaskType:      CleanupTypeImmediate,
			Resources:     []ResourceRef{},
			Retries:       0,
			MaxRetries:    3,
		}

		rcm.discoverResources(task)
		rcm.executeCleanupTask(task)
		return nil
	}

	// Scheduled cleanup
	delay := time.Duration(rcm.config.JobTTLSecondsAfterFinished) * time.Second
	rcm.ScheduleCleanup(scanName, scanNamespace, CleanupTypeScheduled, delay)
	return nil
}

// GetCleanupStats returns cleanup statistics
func (rcm *ResourceCleanupManager) GetCleanupStats() map[string]interface{} {
	rcm.mu.RLock()
	defer rcm.mu.RUnlock()

	return map[string]interface{}{
		"pendingCleanups": len(rcm.pendingCleanups),
		"queueLength":     len(rcm.cleanupQueue),
	}
}

// handleDeletion handles scan deletion and cleanup
func (r *ScanReconciler) handleDeletion(ctx context.Context, scan *complianceapi.Scan) (ctrl.Result, error) {
	// Clean up associated jobs and check results
	if err := r.cleanupJobs(ctx, scan); err != nil {
		return ctrl.Result{}, err
	}

	if err := r.cleanupCheckResults(ctx, scan); err != nil {
		return ctrl.Result{}, err
	}

	// Remove finalizer with retry logic
	for i := 0; i < 3; i++ {
		// Get the latest version of the scan object
		var latestScan complianceapi.Scan
		if err := r.Get(ctx, types.NamespacedName{Name: scan.Name, Namespace: scan.Namespace}, &latestScan); err != nil {
			if errors.IsNotFound(err) {
				// Object already deleted, nothing to do
				return ctrl.Result{}, nil
			}
			r.Log.Error(err, "Failed to get latest Scan object for deletion")
			return ctrl.Result{}, err
		}

		// Remove finalizer from the latest version
		controllerutil.RemoveFinalizer(&latestScan, "compliance-operator.alauda.io/scan")

		// Try to update
		if err := r.Update(ctx, &latestScan); err != nil {
			if errors.IsConflict(err) {
				r.Log.Info("Conflict removing finalizer, retrying", "scan", scan.Name, "attempt", i+1)
				continue // Retry
			}
			r.Log.Error(err, "Failed to remove finalizer")
			return ctrl.Result{}, err
		}

		// Success
		r.Log.Info("Successfully removed finalizer", "scan", scan.Name)
		return ctrl.Result{}, nil
	}

	// All retries failed
	r.Log.Error(nil, "Failed to remove finalizer after retries", "scan", scan.Name)
	return ctrl.Result{RequeueAfter: 5 * time.Second}, nil
}

// cleanupJobs cleans up jobs and associated resources
func (r *ScanReconciler) cleanupJobs(ctx context.Context, scan *complianceapi.Scan) error {
	// With TTLSecondsAfterFinished, Jobs and their Pods will be automatically cleaned up
	// We only need to clean up ConfigMaps and any remaining Jobs without TTL

	var jobs batchv1.JobList
	if err := r.List(ctx, &jobs, client.InNamespace(scan.Namespace), client.MatchingLabels{"compliance-operator.alauda.io/scan": scan.Name}); err != nil {
		return err
	}

	for _, job := range jobs.Items {
		// Clean up the associated ConfigMap
		configMapName := fmt.Sprintf("%s-result", job.Name)
		var configMap corev1.ConfigMap
		if err := r.Get(ctx, types.NamespacedName{Name: configMapName, Namespace: job.Namespace}, &configMap); err == nil {
			if err := r.Delete(ctx, &configMap); err != nil {
				r.Log.Error(err, "Failed to delete result ConfigMap", "configMap", configMapName)
			}
		}

		// Delete the job (TTL will handle Pod cleanup automatically)
		if err := r.Delete(ctx, &job); err != nil {
			return err
		}
	}

	// Clean up any remaining orphaned pods (fallback for edge cases)
	if err := r.cleanupOrphanedPods(ctx, scan); err != nil {
		r.Log.Error(err, "Failed to clean up orphaned pods", "scan", scan.Name)
		// Don't return error here, just log it as pods cleanup is not critical
	}

	return nil
}

// cleanupOrphanedPods cleans up any orphaned pods
func (r *ScanReconciler) cleanupOrphanedPods(ctx context.Context, scan *complianceapi.Scan) error {
	var pods corev1.PodList
	if err := r.List(ctx, &pods, client.InNamespace(scan.Namespace), client.MatchingLabels{"compliance-operator.alauda.io/scan": scan.Name}); err != nil {
		return err
	}

	r.Log.Info("Cleaning up orphaned pods", "scan", scan.Name, "podCount", len(pods.Items))

	for _, pod := range pods.Items {
		// Delete the pod
		if err := r.Delete(ctx, &pod); err != nil {
			r.Log.Error(err, "Failed to delete orphaned pod", "pod", pod.Name, "scan", scan.Name)
			// Continue with other pods even if one fails
		} else {
			r.Log.Info("Deleted orphaned pod", "pod", pod.Name, "scan", scan.Name)
		}
	}

	return nil
}

// cleanupCheckResults cleans up CheckResult objects
func (r *ScanReconciler) cleanupCheckResults(ctx context.Context, scan *complianceapi.Scan) error {
	var results complianceapi.CheckResultList
	if err := r.List(ctx, &results, client.InNamespace(scan.Namespace), client.MatchingLabels{"compliance-operator.alauda.io/scan": scan.Name}); err != nil {
		return err
	}

	for _, result := range results.Items {
		if err := r.Delete(ctx, &result); err != nil {
			return err
		}
	}

	return nil
}
