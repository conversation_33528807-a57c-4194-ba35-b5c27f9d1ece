package scan

import (
	"context"
	"fmt"
	"time"

	batchv1 "k8s.io/api/batch/v1"
	corev1 "k8s.io/api/core/v1"
	"k8s.io/apimachinery/pkg/api/errors"
	"k8s.io/apimachinery/pkg/types"
	ctrl "sigs.k8s.io/controller-runtime"
	"sigs.k8s.io/controller-runtime/pkg/client"
	"sigs.k8s.io/controller-runtime/pkg/controller/controllerutil"

	complianceapi "github.com/alauda/compliance-operator/pkg/apis/compliance/v1alpha1"
)

// handleDeletion handles scan deletion and cleanup
func (r *ScanReconciler) handleDeletion(ctx context.Context, scan *complianceapi.Scan) (ctrl.Result, error) {
	// Clean up associated jobs and check results
	if err := r.cleanupJobs(ctx, scan); err != nil {
		return ctrl.Result{}, err
	}

	if err := r.cleanupCheckResults(ctx, scan); err != nil {
		return ctrl.Result{}, err
	}

	// Remove finalizer with retry logic
	for i := 0; i < 3; i++ {
		// Get the latest version of the scan object
		var latestScan complianceapi.Scan
		if err := r.Get(ctx, types.NamespacedName{Name: scan.Name, Namespace: scan.Namespace}, &latestScan); err != nil {
			if errors.IsNotFound(err) {
				// Object already deleted, nothing to do
				return ctrl.Result{}, nil
			}
			r.Log.Error(err, "Failed to get latest Scan object for deletion")
			return ctrl.Result{}, err
		}

		// Remove finalizer from the latest version
		controllerutil.RemoveFinalizer(&latestScan, "compliance-operator.alauda.io/scan")

		// Try to update
		if err := r.Update(ctx, &latestScan); err != nil {
			if errors.IsConflict(err) {
				r.Log.Info("Conflict removing finalizer, retrying", "scan", scan.Name, "attempt", i+1)
				continue // Retry
			}
			r.Log.Error(err, "Failed to remove finalizer")
			return ctrl.Result{}, err
		}

		// Success
		r.Log.Info("Successfully removed finalizer", "scan", scan.Name)
		return ctrl.Result{}, nil
	}

	// All retries failed
	r.Log.Error(nil, "Failed to remove finalizer after retries", "scan", scan.Name)
	return ctrl.Result{RequeueAfter: 5 * time.Second}, nil
}

// cleanupJobs cleans up jobs and associated resources
func (r *ScanReconciler) cleanupJobs(ctx context.Context, scan *complianceapi.Scan) error {
	// With TTLSecondsAfterFinished, Jobs and their Pods will be automatically cleaned up
	// We only need to clean up ConfigMaps and any remaining Jobs without TTL

	var jobs batchv1.JobList
	if err := r.List(ctx, &jobs, client.InNamespace(scan.Namespace), client.MatchingLabels{"compliance-operator.alauda.io/scan": scan.Name}); err != nil {
		return err
	}

	for _, job := range jobs.Items {
		// Clean up the associated ConfigMap
		configMapName := fmt.Sprintf("%s-result", job.Name)
		var configMap corev1.ConfigMap
		if err := r.Get(ctx, types.NamespacedName{Name: configMapName, Namespace: job.Namespace}, &configMap); err == nil {
			if err := r.Delete(ctx, &configMap); err != nil {
				r.Log.Error(err, "Failed to delete result ConfigMap", "configMap", configMapName)
			}
		}

		// Delete the job (TTL will handle Pod cleanup automatically)
		if err := r.Delete(ctx, &job); err != nil {
			return err
		}
	}

	// Clean up any remaining orphaned pods (fallback for edge cases)
	if err := r.cleanupOrphanedPods(ctx, scan); err != nil {
		r.Log.Error(err, "Failed to clean up orphaned pods", "scan", scan.Name)
		// Don't return error here, just log it as pods cleanup is not critical
	}

	return nil
}

// cleanupOrphanedPods cleans up any orphaned pods
func (r *ScanReconciler) cleanupOrphanedPods(ctx context.Context, scan *complianceapi.Scan) error {
	var pods corev1.PodList
	if err := r.List(ctx, &pods, client.InNamespace(scan.Namespace), client.MatchingLabels{"compliance-operator.alauda.io/scan": scan.Name}); err != nil {
		return err
	}

	r.Log.Info("Cleaning up orphaned pods", "scan", scan.Name, "podCount", len(pods.Items))

	for _, pod := range pods.Items {
		// Delete the pod
		if err := r.Delete(ctx, &pod); err != nil {
			r.Log.Error(err, "Failed to delete orphaned pod", "pod", pod.Name, "scan", scan.Name)
			// Continue with other pods even if one fails
		} else {
			r.Log.Info("Deleted orphaned pod", "pod", pod.Name, "scan", scan.Name)
		}
	}

	return nil
}

// cleanupCheckResults cleans up CheckResult objects
func (r *ScanReconciler) cleanupCheckResults(ctx context.Context, scan *complianceapi.Scan) error {
	var results complianceapi.CheckResultList
	if err := r.List(ctx, &results, client.InNamespace(scan.Namespace), client.MatchingLabels{"compliance-operator.alauda.io/scan": scan.Name}); err != nil {
		return err
	}

	for _, result := range results.Items {
		if err := r.Delete(ctx, &result); err != nil {
			return err
		}
	}

	return nil
}
