package reportservice

import (
	"context"
	"fmt"
	"os"

	appsv1 "k8s.io/api/apps/v1"
	corev1 "k8s.io/api/core/v1"
	"k8s.io/apimachinery/pkg/api/errors"
	"k8s.io/apimachinery/pkg/api/resource"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/util/intstr"
	"k8s.io/utils/pointer"
	"sigs.k8s.io/controller-runtime/pkg/client"
	"sigs.k8s.io/controller-runtime/pkg/log"
)

const (
	ReportServiceName      = "openscap-report-service"
	ReportServiceNamespace = "compliance-system"
	ReportServicePort      = 8080
)

// ReportServiceManager manages the OpenSCAP report service
type ReportServiceManager struct {
	client.Client
	ImageRegistry string
}

// NewReportServiceManager creates a new report service manager
func NewReportServiceManager(client client.Client, imageRegistry string) *ReportServiceManager {
	return &ReportServiceManager{
		Client:        client,
		ImageRegistry: imageRegistry,
	}
}

// getImageRegistry returns the image registry from environment variable or default
func (r *ReportServiceManager) getImageRegistry() string {
	if registry := os.Getenv("IMAGE_REGISTRY"); registry != "" {
		return registry
	}
	// Use proxy registry for K8s cluster access
	return "registry.alauda.cn:60070/test/compliance"
}

// getReportServiceImage returns the report service image path
func (r *ReportServiceManager) getReportServiceImage() string {
	if image := os.Getenv("REPORT_SERVICE_IMAGE"); image != "" {
		return image
	}
	// Fallback to constructing from registry + default tag
	return r.getImageRegistry() + "/openscap-report-service:latest"
}

// EnsureReportService ensures the report service is running
func (r *ReportServiceManager) EnsureReportService(ctx context.Context) error {
	logger := log.FromContext(ctx).WithValues("component", "report-service")

	// Create namespace if not exists
	if err := r.ensureNamespace(ctx); err != nil {
		return fmt.Errorf("failed to ensure namespace: %v", err)
	}

	// Create deployment if not exists
	if err := r.ensureDeployment(ctx); err != nil {
		return fmt.Errorf("failed to ensure deployment: %v", err)
	}

	// Create service if not exists
	if err := r.ensureService(ctx); err != nil {
		return fmt.Errorf("failed to ensure service: %v", err)
	}

	logger.Info("OpenSCAP report service ensured successfully")
	return nil
}

func (r *ReportServiceManager) ensureNamespace(ctx context.Context) error {
	ns := &corev1.Namespace{
		ObjectMeta: metav1.ObjectMeta{
			Name: ReportServiceNamespace,
		},
	}

	err := r.Get(ctx, client.ObjectKey{Name: ReportServiceNamespace}, &corev1.Namespace{})
	if errors.IsNotFound(err) {
		return r.Create(ctx, ns)
	}
	return err
}

func (r *ReportServiceManager) ensureDeployment(ctx context.Context) error {
	// HTTP报告服务，提供上传下载API
	deployment := &appsv1.Deployment{
		ObjectMeta: metav1.ObjectMeta{
			Name:      ReportServiceName,
			Namespace: ReportServiceNamespace,
			Labels: map[string]string{
				"app":       ReportServiceName,
				"component": "report-service",
			},
		},
		Spec: appsv1.DeploymentSpec{
			Replicas: pointer.Int32Ptr(1),
			Selector: &metav1.LabelSelector{
				MatchLabels: map[string]string{
					"app": ReportServiceName,
				},
			},
			Template: corev1.PodTemplateSpec{
				ObjectMeta: metav1.ObjectMeta{
					Labels: map[string]string{
						"app":       ReportServiceName,
						"component": "report-service",
					},
				},
				Spec: corev1.PodSpec{
					ServiceAccountName: "compliance-operator",
					Containers: []corev1.Container{
						{
							Name:  "report-service",
							Image: r.getReportServiceImage(),
							Ports: []corev1.ContainerPort{
								{
									ContainerPort: 8080,
									Name:          "http",
								},
							},
							Env: []corev1.EnvVar{
								{Name: "PORT", Value: "8080"},
								{Name: "REPORTS_DIR", Value: "/reports"},
							},
							VolumeMounts: []corev1.VolumeMount{
								{
									Name:      "reports-storage",
									MountPath: "/reports",
								},
							},
							Resources: corev1.ResourceRequirements{
								Requests: corev1.ResourceList{
									corev1.ResourceMemory: resource.MustParse("64Mi"),
									corev1.ResourceCPU:    resource.MustParse("50m"),
								},
								Limits: corev1.ResourceList{
									corev1.ResourceMemory: resource.MustParse("256Mi"),
									corev1.ResourceCPU:    resource.MustParse("200m"),
								},
							},
							LivenessProbe: &corev1.Probe{
								ProbeHandler: corev1.ProbeHandler{
									HTTPGet: &corev1.HTTPGetAction{
										Path: "/health",
										Port: intstr.FromInt(8080),
									},
								},
								InitialDelaySeconds: 10,
								PeriodSeconds:       30,
							},
							ReadinessProbe: &corev1.Probe{
								ProbeHandler: corev1.ProbeHandler{
									HTTPGet: &corev1.HTTPGetAction{
										Path: "/health",
										Port: intstr.FromInt(8080),
									},
								},
								InitialDelaySeconds: 5,
								PeriodSeconds:       10,
							},
						},
					},
					Volumes: []corev1.Volume{
						{
							Name: "reports-storage",
							VolumeSource: corev1.VolumeSource{
								EmptyDir: &corev1.EmptyDirVolumeSource{},
							},
						},
					},
				},
			},
		},
	}

	err := r.Get(ctx, client.ObjectKey{Name: deployment.Name, Namespace: deployment.Namespace}, &appsv1.Deployment{})
	if errors.IsNotFound(err) {
		return r.Create(ctx, deployment)
	}
	return err
}

func (r *ReportServiceManager) ensureService(ctx context.Context) error {
	// HTTP报告服务的Service
	service := &corev1.Service{
		ObjectMeta: metav1.ObjectMeta{
			Name:      ReportServiceName,
			Namespace: ReportServiceNamespace,
			Labels: map[string]string{
				"app":       ReportServiceName,
				"component": "report-service",
			},
		},
		Spec: corev1.ServiceSpec{
			Type: corev1.ServiceTypeClusterIP,
			Ports: []corev1.ServicePort{
				{
					Port:       ReportServicePort,
					TargetPort: intstr.FromInt(8080),
					Protocol:   corev1.ProtocolTCP,
					Name:       "http",
				},
			},
			Selector: map[string]string{
				"app": ReportServiceName,
			},
		},
	}

	err := r.Get(ctx, client.ObjectKey{Name: service.Name, Namespace: service.Namespace}, &corev1.Service{})
	if errors.IsNotFound(err) {
		return r.Create(ctx, service)
	}
	return err
}
