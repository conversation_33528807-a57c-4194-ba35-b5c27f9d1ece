package main

import (
	"context"
	"flag"
	"fmt"
	"log"
	"net/http"
	"os"
	"path/filepath"

	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/runtime/schema"
	"k8s.io/client-go/dynamic"
	"k8s.io/client-go/kubernetes"
	"k8s.io/client-go/tools/clientcmd"
	"k8s.io/client-go/util/homedir"
)

func main() {
	var (
		kubeconfig = flag.String("kubeconfig", filepath.Join(homedir.HomeDir(), ".kube", "config"), "path to kubeconfig file")
		namespace  = flag.String("namespace", "compliance-system", "namespace where scan reports are stored")
		scan       = flag.String("scan", "", "scan name to view report for")
		export     = flag.String("export", "", "export report to file (optional)")
		serve      = flag.Bool("serve", false, "serve report via HTTP server")
		port       = flag.String("port", "8080", "port for HTTP server")
	)
	flag.Parse()

	if *scan == "" {
		fmt.Println("Usage: report-viewer -scan <scan-name> [-export <file>] [-serve] [-port <port>]")
		os.Exit(1)
	}

	// Build Kubernetes client
	config, err := clientcmd.BuildConfigFromFlags("", *kubeconfig)
	if err != nil {
		log.Fatalf("Failed to build config: %v", err)
	}

	clientset, err := kubernetes.NewForConfig(config)
	if err != nil {
		log.Fatalf("Failed to create clientset: %v", err)
	}

	// Create dynamic client for custom resources
	dynamicClient, err := dynamic.NewForConfig(config)
	if err != nil {
		log.Fatalf("Failed to create dynamic client: %v", err)
	}

	// Get the latest scanID from the Scan resource
	scanID, err := getLatestScanID(dynamicClient, *namespace, *scan)
	if err != nil {
		log.Fatalf("Failed to get latest scanID for scan %s: %v", *scan, err)
	}

	fmt.Printf("Found latest scanID: %s\n", scanID)

	// Try the new naming convention first: report-{scanID}
	configMapName := fmt.Sprintf("report-%s", scanID)
	configMap, err := clientset.CoreV1().ConfigMaps(*namespace).Get(context.TODO(), configMapName, metav1.GetOptions{})
	if err != nil {
		// Fallback to old naming convention: {scan-name}-report
		fmt.Printf("New format ConfigMap not found, trying legacy format...\n")
		configMapName = fmt.Sprintf("%s-report", *scan)
		configMap, err = clientset.CoreV1().ConfigMaps(*namespace).Get(context.TODO(), configMapName, metav1.GetOptions{})
		if err != nil {
			log.Fatalf("Failed to get report ConfigMap %s/%s: %v", *namespace, configMapName, err)
		}
	}

	htmlContent, exists := configMap.Data["report.html"]
	if !exists {
		log.Fatalf("Report HTML not found in ConfigMap %s/%s", *namespace, configMapName)
	}

	// Export to file if requested
	if *export != "" {
		if err := os.WriteFile(*export, []byte(htmlContent), 0644); err != nil {
			log.Fatalf("Failed to export report to %s: %v", *export, err)
		}
		fmt.Printf("Report exported to %s\n", *export)
		return
	}

	// Serve via HTTP if requested
	if *serve {
		http.HandleFunc("/", func(w http.ResponseWriter, r *http.Request) {
			w.Header().Set("Content-Type", "text/html; charset=utf-8")
			w.Write([]byte(htmlContent))
		})

		fmt.Printf("Serving report at http://localhost:%s\n", *port)
		fmt.Printf("Press Ctrl+C to stop the server\n")
		log.Fatal(http.ListenAndServe(":"+*port, nil))
	}

	// Default: print to stdout
	fmt.Println(htmlContent)
}

// getLatestScanID retrieves the latest scanID from the Scan resource
func getLatestScanID(dynamicClient dynamic.Interface, namespace, scanName string) (string, error) {
	// Define the Scan resource GVR
	scanGVR := schema.GroupVersionResource{
		Group:    "compliance-operator.alauda.io",
		Version:  "v1alpha1",
		Resource: "scans",
	}

	// Get the Scan resource
	scanResource, err := dynamicClient.Resource(scanGVR).Namespace(namespace).Get(context.TODO(), scanName, metav1.GetOptions{})
	if err != nil {
		return "", fmt.Errorf("failed to get scan resource: %v", err)
	}

	// Extract scanID from annotations
	annotations, found, err := unstructuredNestedStringMap(scanResource.Object, "metadata", "annotations")
	if err != nil || !found {
		return "", fmt.Errorf("failed to get annotations from scan resource")
	}

	scanID, exists := annotations["compliance-operator.alauda.io/current-scan-id"]
	if !exists || scanID == "" {
		return "", fmt.Errorf("no current-scan-id found in scan annotations")
	}

	return scanID, nil
}

// unstructuredNestedStringMap is a helper function to extract nested string maps from unstructured objects
func unstructuredNestedStringMap(obj map[string]interface{}, fields ...string) (map[string]string, bool, error) {
	val, found, err := unstructuredNestedFieldNoCopy(obj, fields...)
	if !found || err != nil {
		return nil, found, err
	}
	m, ok := val.(map[string]interface{})
	if !ok {
		return nil, false, fmt.Errorf("%v accessor error: %v is of the type %T, expected map[string]interface{}", fields, val, val)
	}
	strMap := make(map[string]string, len(m))
	for k, v := range m {
		if str, ok := v.(string); ok {
			strMap[k] = str
		}
	}
	return strMap, true, nil
}

// unstructuredNestedFieldNoCopy is a helper function to extract nested fields from unstructured objects
func unstructuredNestedFieldNoCopy(obj map[string]interface{}, fields ...string) (interface{}, bool, error) {
	var val interface{} = obj

	for i, field := range fields {
		if val == nil {
			return nil, false, nil
		}
		if m, ok := val.(map[string]interface{}); ok {
			val, ok = m[field]
			if !ok {
				return nil, false, nil
			}
		} else {
			return nil, false, fmt.Errorf("%v accessor error: %v is of the type %T, expected map[string]interface{}", fields[:i+1], val, val)
		}
	}
	return val, true, nil
}
