#!/bin/bash

# Compliance Operator Job Monitoring Script
# This script helps monitor the JobManager fix effectiveness

echo "=== Compliance Operator Job Monitoring ==="
echo "Timestamp: $(date)"
echo

# Function to show job status summary
show_job_summary() {
    echo "📊 Job Status Summary:"
    echo "----------------------"
    kubectl get jobs -n compliance-system --no-headers 2>/dev/null | \
    awk '{print $3}' | sort | uniq -c | \
    while read count status; do
        echo "  $status: $count jobs"
    done
    echo
}

# Function to show recent jobs
show_recent_jobs() {
    echo "🕒 Recent Jobs (last 10):"
    echo "-------------------------"
    kubectl get jobs -n compliance-system --sort-by=.metadata.creationTimestamp | tail -10
    echo
}

# Function to show scan status
show_scan_status() {
    echo "🔍 Scan Status:"
    echo "---------------"
    kubectl get scans -n compliance-system -o custom-columns="NAME:.metadata.name,PHASE:.status.phase,RESULT:.status.result,AGE:.metadata.creationTimestamp" 2>/dev/null
    echo
}

# Function to show active/running jobs
show_active_jobs() {
    echo "⚡ Active/Running Jobs:"
    echo "----------------------"
    local active_jobs=$(kubectl get jobs -n compliance-system --field-selector status.successful!=1,status.failed!=1 --no-headers 2>/dev/null | wc -l)
    if [ "$active_jobs" -eq 0 ]; then
        echo "  ✅ No active jobs (all completed)"
    else
        kubectl get jobs -n compliance-system --field-selector status.successful!=1,status.failed!=1
    fi
    echo
}

# Function to check for stuck jobs (jobs older than 10 minutes that are not complete)
check_stuck_jobs() {
    echo "🚨 Potential Stuck Jobs (>10min, not complete):"
    echo "-----------------------------------------------"
    local stuck_found=false
    
    kubectl get jobs -n compliance-system -o json 2>/dev/null | \
    jq -r '.items[] | select(.status.succeeded != 1 and .status.failed != 1) | select((now - (.metadata.creationTimestamp | fromdateiso8601)) > 600) | "\(.metadata.name) - Age: \((now - (.metadata.creationTimestamp | fromdateiso8601)) / 60 | floor)min"' 2>/dev/null | \
    while read line; do
        if [ ! -z "$line" ]; then
            echo "  ⚠️  $line"
            stuck_found=true
        fi
    done
    
    if [ "$stuck_found" = false ]; then
        echo "  ✅ No stuck jobs detected"
    fi
    echo
}

# Function to show ConfigMap cleanup status
show_configmap_status() {
    echo "📁 ConfigMap Status:"
    echo "-------------------"
    local total_cms=$(kubectl get configmaps -n compliance-system --no-headers 2>/dev/null | grep -v "kube-root-ca.crt" | wc -l)
    local report_cms=$(kubectl get configmaps -n compliance-system --no-headers 2>/dev/null | grep "report-" | wc -l)
    local result_cms=$(kubectl get configmaps -n compliance-system --no-headers 2>/dev/null | grep -v "report-\|kube-root-ca.crt" | wc -l)
    
    echo "  Total ConfigMaps: $total_cms"
    echo "  Report ConfigMaps: $report_cms"
    echo "  Result ConfigMaps: $result_cms"
    echo
}

# Main monitoring function
main() {
    show_job_summary
    show_scan_status
    show_active_jobs
    check_stuck_jobs
    show_configmap_status
    show_recent_jobs
}

# Check if watch mode is requested
if [ "$1" = "--watch" ] || [ "$1" = "-w" ]; then
    echo "👀 Watching mode enabled (Ctrl+C to stop)"
    echo "=========================================="
    while true; do
        clear
        main
        echo "🔄 Refreshing in 30 seconds... (Ctrl+C to stop)"
        sleep 30
    done
else
    main
    echo "💡 Tip: Use '$0 --watch' for continuous monitoring"
fi
