---
apiVersion: apiextensions.k8s.io/v1
kind: CustomResourceDefinition
metadata:
  annotations:
    controller-gen.kubebuilder.io/version: v0.14.0
  name: compliancesuites.compliance-operator.alauda.io
spec:
  group: compliance-operator.alauda.io
  names:
    kind: ComplianceSuite
    listKind: ComplianceSuiteList
    plural: compliancesuites
    singular: compliancesuite
  scope: Namespaced
  versions:
  - name: v1alpha1
    schema:
      openAPIV3Schema:
        description: ComplianceSuite represents a collection of compliance scans
        properties:
          apiVersion:
            description: |-
              APIVersion defines the versioned schema of this representation of an object.
              Servers should convert recognized schemas to the latest internal value, and
              may reject unrecognized values.
              More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#resources
            type: string
          kind:
            description: |-
              Kind is a string value representing the REST resource this object represents.
              Servers may infer this from the endpoint the client submits requests to.
              Cannot be updated.
              In CamelCase.
              More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds
            type: string
          metadata:
            type: object
          spec:
            description: ComplianceSuiteSpec defines the desired state of ComplianceSuite
            properties:
              autoApplyRemediations:
                description: AutoApplyRemediations indicates whether to automatically
                  apply remediations
                type: boolean
              scanSettingsBinding:
                description: ScanSettingsBinding allows to bind scan settings to all
                  scans in the suite
                type: string
              scans:
                description: Scans defines the scans to be executed as part of the
                  suite
                items:
                  description: ComplianceScanSpec defines a scan within a suite
                  properties:
                    name:
                      description: Name of the scan
                      type: string
                    nodeSelector:
                      additionalProperties:
                        type: string
                      description: NodeSelector selects nodes to scan (for node scans)
                      type: object
                    profile:
                      description: Profile to use for the scan
                      type: string
                    scanType:
                      description: ScanType defines the type of scan (node, platform,
                        or both)
                      enum:
                      - node
                      - platform
                      - both
                      type: string
                    schedule:
                      description: Schedule for individual scan (overrides suite schedule)
                      type: string
                  required:
                  - name
                  - profile
                  - scanType
                  type: object
                type: array
              schedule:
                description: Schedule defines when to run the compliance suite (cron
                  format)
                type: string
            required:
            - scans
            type: object
          status:
            description: ComplianceSuiteStatus defines the observed state of ComplianceSuite
            properties:
              endTime:
                description: EndTime indicates when the suite completed
                format: date-time
                type: string
              message:
                description: Message provides additional information about the current
                  status
                type: string
              phase:
                description: Phase indicates the current phase of the compliance suite
                enum:
                - LAUNCHING
                - RUNNING
                - DONE
                - ERROR
                type: string
              result:
                description: Result indicates the overall result of the compliance
                  suite
                enum:
                - COMPLIANT
                - NON-COMPLIANT
                - INCONSISTENT
                - ERROR
                type: string
              scanStatuses:
                description: ScanStatuses contains the status of individual scans
                items:
                  description: ComplianceScanStatus represents the status of a scan
                    within a suite
                  properties:
                    endTime:
                      description: EndTime of the scan
                      format: date-time
                      type: string
                    message:
                      description: Message provides additional information
                      type: string
                    name:
                      description: Name of the scan
                      type: string
                    phase:
                      description: Phase of the scan
                      type: string
                    result:
                      description: Result of the scan
                      type: string
                    resultsStorage:
                      description: ResultsStorage indicates where the scan results
                        are stored
                      properties:
                        name:
                          description: Name of the storage object (e.g., ConfigMap
                            name)
                          type: string
                        namespace:
                          description: Namespace of the storage object
                          type: string
                        type:
                          description: Type of storage (configmap, pvc, etc.)
                          type: string
                      required:
                      - name
                      - namespace
                      type: object
                    startTime:
                      description: StartTime of the scan
                      format: date-time
                      type: string
                  required:
                  - name
                  - phase
                  - result
                  type: object
                type: array
              startTime:
                description: StartTime indicates when the suite started
                format: date-time
                type: string
              summary:
                description: Summary provides aggregated results across all scans
                properties:
                  error:
                    description: Number of checks with errors
                    type: integer
                  failed:
                    description: Number of failed checks
                    type: integer
                  inconsistent:
                    description: Number of inconsistent checks
                    type: integer
                  manual:
                    description: Number of checks requiring manual review
                    type: integer
                  notApplicable:
                    description: Number of not applicable checks
                    type: integer
                  passed:
                    description: Number of passed checks
                    type: integer
                  total:
                    description: Total number of checks
                    type: integer
                required:
                - error
                - failed
                - inconsistent
                - manual
                - notApplicable
                - passed
                - total
                type: object
            type: object
        type: object
    served: true
    storage: true
    subresources:
      status: {}
