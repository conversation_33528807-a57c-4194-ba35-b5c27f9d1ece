---
apiVersion: apiextensions.k8s.io/v1
kind: CustomResourceDefinition
metadata:
  annotations:
    controller-gen.kubebuilder.io/version: v0.14.0
  name: scans.compliance-operator.alauda.io
spec:
  group: compliance-operator.alauda.io
  names:
    kind: Scan
    listKind: ScanList
    plural: scans
    singular: scan
  scope: Namespaced
  versions:
  - name: v1alpha1
    schema:
      openAPIV3Schema:
        description: Scan represents a compliance scan
        properties:
          apiVersion:
            description: |-
              APIVersion defines the versioned schema of this representation of an object.
              Servers should convert recognized schemas to the latest internal value, and
              may reject unrecognized values.
              More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#resources
            type: string
          kind:
            description: |-
              Kind is a string value representing the REST resource this object represents.
              Servers may infer this from the endpoint the client submits requests to.
              Cannot be updated.
              In CamelCase.
              More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds
            type: string
          metadata:
            type: object
          spec:
            description: ScanSpec defines the desired state of Scan
            properties:
              maxHistoricalResults:
                description: MaxHistoricalResults defines how many historical scan
                  results to keep
                format: int32
                type: integer
              nodeSelector:
                additionalProperties:
                  type: string
                description: NodeSelector is used to select nodes for node scans
                type: object
              profile:
                description: Profile is the name of the compliance profile to use
                  for this scan
                type: string
              scanType:
                description: ScanType indicates the type of scan to perform (platform,
                  node, or all)
                type: string
              schedule:
                description: Schedule defines when to run the scan using cron syntax
                type: string
            required:
            - profile
            type: object
          status:
            description: ScanStatus defines the observed state of Scan
            properties:
              endTime:
                description: EndTime is when the scan completed
                format: date-time
                type: string
              historicalResults:
                description: HistoricalResults contains references to previous scan
                  results
                items:
                  description: HistoricalResultRef references a historical scan result
                  properties:
                    checkResultName:
                      description: CheckResultName is the name of the CheckResult
                        resource
                      type: string
                    reportName:
                      description: ReportName is the name of the report ConfigMap
                      type: string
                    scanID:
                      description: ScanID is the unique identifier for this scan execution
                      type: string
                    stats:
                      description: Stats provides a summary of the scan results
                      properties:
                        error:
                          description: Error is the number of rules that encountered
                            errors
                          type: integer
                        fail:
                          description: Fail is the number of rules that failed
                          type: integer
                        inconsistent:
                          description: Inconsistent is the number of rules with inconsistent
                            results
                          type: integer
                        manual:
                          description: Manual is the number of rules requiring manual
                            verification
                          type: integer
                        notApplicable:
                          description: NotApplicable is the number of rules that were
                            not applicable
                          type: integer
                        pass:
                          description: Pass is the number of rules that passed
                          type: integer
                        total:
                          description: Total is the total number of rules checked
                          type: integer
                      required:
                      - error
                      - fail
                      - inconsistent
                      - manual
                      - notApplicable
                      - pass
                      - total
                      type: object
                    timestamp:
                      description: Timestamp is when the scan was executed
                      format: date-time
                      type: string
                  required:
                  - checkResultName
                  - scanID
                  - timestamp
                  type: object
                type: array
              latestResult:
                description: LatestResult references the most recent scan result
                properties:
                  checkResultName:
                    description: CheckResultName is the name of the CheckResult resource
                    type: string
                  reportName:
                    description: ReportName is the name of the report ConfigMap
                    type: string
                  scanID:
                    description: ScanID is the unique identifier for this scan execution
                    type: string
                  stats:
                    description: Stats provides a summary of the scan results
                    properties:
                      error:
                        description: Error is the number of rules that encountered
                          errors
                        type: integer
                      fail:
                        description: Fail is the number of rules that failed
                        type: integer
                      inconsistent:
                        description: Inconsistent is the number of rules with inconsistent
                          results
                        type: integer
                      manual:
                        description: Manual is the number of rules requiring manual
                          verification
                        type: integer
                      notApplicable:
                        description: NotApplicable is the number of rules that were
                          not applicable
                        type: integer
                      pass:
                        description: Pass is the number of rules that passed
                        type: integer
                      total:
                        description: Total is the total number of rules checked
                        type: integer
                    required:
                    - error
                    - fail
                    - inconsistent
                    - manual
                    - notApplicable
                    - pass
                    - total
                    type: object
                  timestamp:
                    description: Timestamp is when the scan was executed
                    format: date-time
                    type: string
                required:
                - checkResultName
                - scanID
                - timestamp
                type: object
              message:
                description: Message provides additional information about the scan
                  status
                type: string
              phase:
                description: 'Phase is the current phase of the scan: Pending, Running,
                  Done, Error'
                type: string
              result:
                description: 'Result is the overall result of the scan: Compliant,
                  NonCompliant, Error'
                type: string
              startTime:
                description: StartTime is when the scan was started
                format: date-time
                type: string
            type: object
        type: object
    served: true
    storage: true
    subresources:
      status: {}
