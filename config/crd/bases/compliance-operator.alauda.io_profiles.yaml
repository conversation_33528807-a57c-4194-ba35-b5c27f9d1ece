---
apiVersion: apiextensions.k8s.io/v1
kind: CustomResourceDefinition
metadata:
  annotations:
    controller-gen.kubebuilder.io/version: v0.14.0
  name: profiles.compliance-operator.alauda.io
spec:
  group: compliance-operator.alauda.io
  names:
    kind: Profile
    listKind: ProfileList
    plural: profiles
    singular: profile
  scope: Namespaced
  versions:
  - name: v1alpha1
    schema:
      openAPIV3Schema:
        description: Profile represents a compliance profile
        properties:
          apiVersion:
            description: |-
              APIVersion defines the versioned schema of this representation of an object.
              Servers should convert recognized schemas to the latest internal value, and
              may reject unrecognized values.
              More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#resources
            type: string
          kind:
            description: |-
              Kind is a string value representing the REST resource this object represents.
              Servers may infer this from the endpoint the client submits requests to.
              Cannot be updated.
              In CamelCase.
              More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds
            type: string
          metadata:
            type: object
          spec:
            description: ProfileSpec defines the desired state of Profile
            properties:
              dataStream:
                description: |-
                  OpenSCAP specific fields for datastream-based profiles
                  DataStream indicates this profile uses OpenSCAP datastream scanning
                properties:
                  contentFile:
                    description: ContentFile is the name of the SCAP data stream file
                      (e.g., ssg-ubuntu2204-ds.xml)
                    type: string
                  contentImage:
                    description: ContentImage is the container image that contains
                      the SCAP content
                    type: string
                  profileId:
                    description: |-
                      ProfileID is the XCCDF profile identifier within the datastream
                      (e.g., xccdf_org.ssgproject.content_profile_stig)
                    type: string
                  scanType:
                    description: ScanType indicates the type of scan this datastream
                      supports
                    enum:
                    - platform
                    - node
                    - both
                    type: string
                required:
                - contentFile
                - contentImage
                - profileId
                type: object
              description:
                description: Description provides detailed information about the profile
                type: string
              extends:
                description: Extends indicates if this profile extends another profile
                type: string
              id:
                description: ID is the XCCDF profile identifier (e.g., xccdf_org.ssgproject.content_profile_stig)
                type: string
              rules:
                description: Rules is the list of rules included in this profile
                items:
                  description: RuleReference references a compliance rule
                  properties:
                    name:
                      type: string
                  required:
                  - name
                  type: object
                type: array
              selections:
                description: Selections allows enabling/disabling specific rules
                items:
                  description: Selection allows enabling/disabling specific rules
                  properties:
                    id:
                      type: string
                    selected:
                      type: boolean
                  required:
                  - id
                  - selected
                  type: object
                type: array
              title:
                description: Title is the human-readable title of the profile
                type: string
              version:
                description: Version is the profile version (e.g., V2R2)
                type: string
            required:
            - title
            type: object
        type: object
    served: true
    storage: true
