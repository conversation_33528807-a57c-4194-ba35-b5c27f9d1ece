---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: manager-role
rules:
- apiGroups:
  - ""
  resources:
  - configmaps
  verbs:
  - create
  - delete
  - get
  - list
  - patch
  - update
  - watch
- apiGroups:
  - ""
  resources:
  - pods
  verbs:
  - create
  - delete
  - get
  - list
  - patch
  - update
  - watch
- apiGroups:
  - ""
  resources:
  - pods/log
  verbs:
  - get
  - list
- apiGroups:
  - batch
  resources:
  - jobs
  verbs:
  - create
  - delete
  - get
  - list
  - patch
  - update
  - watch
- apiGroups:
  - compliance-operator.alauda.io
  resources:
  - checkresults
  verbs:
  - create
  - delete
  - get
  - list
  - patch
  - update
  - watch
- apiGroups:
  - compliance-operator.alauda.io
  resources:
  - compliancesuites
  verbs:
  - create
  - delete
  - get
  - list
  - patch
  - update
  - watch
- apiGroups:
  - compliance-operator.alauda.io
  resources:
  - compliancesuites/status
  verbs:
  - get
  - patch
  - update
- apiGroups:
  - compliance-operator.alauda.io
  resources:
  - profilebundles
  verbs:
  - create
  - delete
  - get
  - list
  - patch
  - update
  - watch
- apiGroups:
  - compliance-operator.alauda.io
  resources:
  - profilebundles/status
  verbs:
  - get
  - patch
  - update
- apiGroups:
  - compliance-operator.alauda.io
  resources:
  - profiles
  verbs:
  - create
  - delete
  - get
  - list
  - patch
  - update
  - watch
- apiGroups:
  - compliance-operator.alauda.io
  resources:
  - rules
  verbs:
  - create
  - delete
  - get
  - list
  - patch
  - update
  - watch
- apiGroups:
  - compliance-operator.alauda.io
  resources:
  - scans
  verbs:
  - create
  - delete
  - get
  - list
  - patch
  - update
  - watch
- apiGroups:
  - compliance-operator.alauda.io
  resources:
  - scans/status
  verbs:
  - get
  - patch
  - update
