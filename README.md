# Compliance Operator

基于 Kubernetes 的现代化合规性检查工具，支持 STIG 标准、自定义规则配置和智能资源管理。

## 🚀 项目概述

Compliance Operator 是一个类似于 OpenShift compliance-operator 的 Kubernetes 合规性检查工具，具有以下特点：

- **🎯 简化配置**：使用 YAML 配置替代复杂的 OpenSCAP XML 数据流文件
- **🔄 兼容性**：同时支持 OpenSCAP XML 数据流文件扫描
- **⚡ 高效扫描**：支持平台级和节点级合规性检查，智能资源管理
- **📅 自动化**：支持定时扫描、自动资源清理和报告生成
- **🔧 可扩展**：支持自定义规则和检查脚本
- **🛡️ STIG 支持**：完整实现 DISA STIG Kubernetes v2r2 合规标准
- **🎨 现代界面**：美观的HTML报告，支持中英文和响应式设计

## 📋 核心功能

### 🔍 智能扫描
- **并发执行**：多规则并行检查，提升扫描效率
- **资源优化**：CheckResult数据结构优化，减少存储占用30-40%
- **自动清理**：扫描完成后自动清理临时资源，防止集群资源泄露
- **状态管理**：完善的扫描生命周期管理和错误处理

### 📊 报告系统
- **现代化界面**：渐变色设计、卡片式布局、响应式设计
- **中英文支持**：界面本地化，提升用户体验
- **交互功能**：Description字段支持展开/收起，优化长文本显示
- **动态数据**：报告生成时动态获取Rule信息，确保数据一致性

### 🔧 资源管理
- **精简存储**：CheckResult只存储核心检查结果，Rule信息按需获取
- **智能清理**：只清理已完成的Job资源，保留Error状态便于调试
- **历史管理**：支持历史结果保留配置，自动清理过期数据

## 🏗️ 架构设计

### 核心组件

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│ Compliance      │    │ ProfileBundle   │    │ Scan            │
│ Controller      │    │ Controller      │    │ Controller      │
│                 │    │                 │    │                 │
│ 主控制器        │    │ OpenSCAP解析    │    │ 扫描任务管理    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         └───────────────────────┼───────────────────────┘
                                 │
         ┌───────────────────────▼───────────────────────┐
         │            Scanner Images                     │
         │  ┌─────────────────────────────────────────┐  │
         │  │ unified-scanner.sh (统一扫描器)        │  │
         │  │ • Platform检查 • Node检查 • 结果收集   │  │
         │  └─────────────────────────────────────────┘  │
         └───────────────────────────────────────────────┘
```

### CRD 资源

| 资源类型 | 用途 | 主要字段 |
|---------|------|----------|
| `ProfileBundle` | 管理 OpenSCAP 数据流内容 | contentFile, contentImage |
| `Profile` | 定义合规性配置文件 | title, description, rules |
| `Rule` | 定义具体的检查规则 | title, description, checkType, checkScript |
| `Scan` | 定义扫描任务 | profile, scanType, nodeSelector, schedule |
| `CheckResult` | 存储检查结果 | ruleResults, scanName, timestamp |
| `ComplianceSuite` | 管理多个扫描的集合 | scans, schedule, autoApplyRemediations |

## 🚀 快速开始

### 前置条件

- Kubernetes 1.20+
- Helm 3.0+
- Docker/Podman (用于构建镜像)

### 安装部署

1. **克隆项目**
```bash
git clone https://github.com/alauda/compliance-operator.git
cd compliance-operator
```

2. **构建镜像**
```bash
make docker-build-all
make docker-push-all
```

3. **安装 Helm Chart**
```bash
helm install compliance-operator charts/compliance-operator \
  --namespace compliance-system \
  --create-namespace
```

4. **验证安装**
```bash
kubectl get pods -n compliance-system
kubectl get crd | grep compliance-operator.alauda.io
```

### 基本使用

1. **创建自定义 Profile 和 Rules**
```bash
kubectl apply -f examples/profile.yaml
kubectl apply -f stig-k8s-v2r2-rules/
```

2. **执行合规性扫描**
```bash
kubectl apply -f examples/scan.yaml
```

3. **查看扫描结果**
```bash
# 查看扫描状态
kubectl get scan -n compliance-system

# 查看检查结果
kubectl get checkresult -n compliance-system

# 查看详细结果
kubectl describe checkresult <result-name> -n compliance-system
```

4. **查看扫描报告**
```bash
# 使用报告查看器
go run cmd/report-viewer/main.go -scan <scan-name> -serve -port 8080

# 或导出HTML报告
go run cmd/report-viewer/main.go -scan <scan-name> -export report.html


kubectl get cm $(kubectl get scan <scan-name> -n compliance-system -o jsonpath='{.status.latestResult.reportName}') -n compliance-system -o jsonpath='{.data.report\.html}' > scan-report.html
```

## 📚 资源结构详解

### Rule（规则）

Rule 定义具体的检查规则，支持平台和节点两种检查类型：

```yaml
apiVersion: compliance-operator.alauda.io/v1alpha1
kind: Rule
metadata:
  name: stig-k8s-api-server-anonymous-auth-disabled
  namespace: compliance-system
spec:
  # 规则标题
  title: "CNTR-K8-000360 - API server anonymous authentication disabled"
  
  # 详细描述
  description: "The Kubernetes API Server must have anonymous authentication disabled to prevent unauthorized access."
  
  # 严重性级别: critical, high, medium, low
  severity: "high"
  
  # 检查类型: platform（控制平面）或 node（工作节点）
  checkType: "platform"
  
  # 检查脚本
  checkScript: |
    #!/bin/bash
    if ps aux | grep kube-apiserver | grep -q "anonymous-auth=false"; then
      echo "PASS: Anonymous authentication is disabled"
      exit 0
    else
      echo "FAIL: Anonymous authentication is not disabled"
      exit 1
    fi
  
  # 修复说明
  instructions: "Add --anonymous-auth=false to the API server configuration"
  
  # STIG 信息（可选）
  stig:
    id: "V-242376"
    cat: "CAT-I"
```

### Profile（配置文件）

Profile 组织相关的规则，支持规则选择和配置：

```yaml
apiVersion: compliance-operator.alauda.io/v1alpha1
kind: Profile
metadata:
  name: stig-k8s-v2r2-platform
  namespace: compliance-system
spec:
  # 配置文件标题
  title: "DISA STIG Kubernetes v2r2 - Platform Profile"
  
  # 详细描述
  description: "Defense Information Systems Agency Security Technical Implementation Guide for Kubernetes v2r2 - Platform Controls"
  
  # 包含的规则列表
  rules:
    - name: stig-k8s-api-server-anonymous-auth-disabled
    - name: stig-k8s-api-server-insecure-port-disabled
    - name: stig-k8s-api-server-audit-log-enabled
    # ... 更多规则
  
  # 规则选择配置（可选）
  selections:
    - id: stig-k8s-api-server-anonymous-auth-disabled
      selected: true
    - id: stig-k8s-api-server-insecure-port-disabled
      selected: true
```

### Scan（扫描任务）

Scan 定义扫描任务，支持多种扫描类型和调度选项：

```yaml
apiVersion: compliance-operator.alauda.io/v1alpha1
kind: Scan
metadata:
  name: security-scan
  namespace: compliance-system
spec:
  # 使用的配置文件（必填）
  profile: stig-k8s-v2r2-platform
  
  # 扫描类型: platform（平台）、node（节点）或 both（两者）
  scanType: both
  
  # 节点选择器（node或both类型时使用）
  nodeSelector:
    kubernetes.io/os: linux
    node-role.kubernetes.io/worker: ""
  
  # 定时调度（可选，使用cron语法）
  schedule: "0 2 * * *"  # 每天凌晨2点执行
  
  # 保留的历史结果数量（可选，默认5个）
  maxHistoricalResults: 10
```

### CheckResult（检查结果）

CheckResult 存储扫描的检查结果，结构已优化：

```yaml
apiVersion: compliance-operator.alauda.io/v1alpha1
kind: CheckResult
metadata:
  name: security-scan-20241225-090347-abc123
  namespace: compliance-system
spec:
  # 扫描名称
  scanName: security-scan
  
  # 使用的配置文件
  profileName: stig-k8s-v2r2-platform
  
  # 扫描时间戳
  timestamp: "2024-12-25T09:03:47Z"
  
  # 规则检查结果列表
  ruleResults:
    - ruleId: stig-k8s-api-server-anonymous-auth-disabled
      ruleName: stig-k8s-api-server-anonymous-auth-disabled
      severity: high
      checkType: platform
      status: PASS  # PASS, FAIL, ERROR, MANUAL, INCONSISTENT, NOT-APPLICABLE
      message: "Anonymous authentication is properly disabled"
      
    - ruleId: stig-k8s-kubelet-readonly-port-disabled
      ruleName: stig-k8s-kubelet-readonly-port-disabled
      severity: medium
      checkType: node
      status: FAIL
      message: "Failed on some nodes"
      # 节点级检查的详细结果
      nodeResults:
        - nodeName: worker-node-1
          status: PASS
          message: "Readonly port is disabled"
          evidence: "kubelet process shows --read-only-port=0"
        - nodeName: worker-node-2
          status: FAIL
          message: "Readonly port is not disabled"
          evidence: "kubelet process missing --read-only-port=0"
```

### ComplianceSuite（合规套件）

ComplianceSuite 管理多个相关的扫描任务：

```yaml
apiVersion: compliance-operator.alauda.io/v1alpha1
kind: ComplianceSuite
metadata:
  name: security-baseline-suite
  namespace: compliance-system
spec:
  # 套件级别的调度（可选）
  schedule: "0 2 * * *"
  
  # 是否自动应用修复措施
  autoApplyRemediations: false
  
  # 扫描配置列表
  scans:
    # 平台合规扫描
    - name: platform-security
      profile: stig-k8s-v2r2-platform
      scanType: platform
      
    # 节点合规扫描
    - name: node-security
      profile: stig-k8s-v2r2-node
      scanType: node
      nodeSelector:
        kubernetes.io/os: linux
```

## 🎯 使用场景

### 场景1：基础安全扫描

**适用于**：日常安全检查、快速合规验证

```bash
# 1. 创建基础扫描
kubectl apply -f - <<EOF
apiVersion: compliance-operator.alauda.io/v1alpha1
kind: Scan
metadata:
  name: basic-security-scan
  namespace: compliance-system
spec:
  profile: stig-k8s-v2r2-platform
  scanType: platform
EOF

# 2. 监控扫描进度
kubectl get scan basic-security-scan -n compliance-system -w

# 3. 查看结果
kubectl get checkresult -l compliance-operator.alauda.io/scan=basic-security-scan -n compliance-system
```

### 场景2：定时合规审计

**适用于**：定期合规检查、合规报告生成

```bash
# 1. 创建定时扫描
kubectl apply -f - <<EOF
apiVersion: compliance-operator.alauda.io/v1alpha1
kind: Scan
metadata:
  name: weekly-audit-scan
  namespace: compliance-system
spec:
  profile: stig-k8s-v2r2-platform
  scanType: both
  schedule: "0 2 * * 1"  # 每周一凌晨2点
  maxHistoricalResults: 10
  nodeSelector:
    kubernetes.io/os: linux
EOF

# 2. 查看调度状态
kubectl describe scan weekly-audit-scan -n compliance-system
```

### 场景3：节点特定扫描

**适用于**：特定节点检查、故障排查

```bash
# 1. 扫描特定节点
kubectl apply -f - <<EOF
apiVersion: compliance-operator.alauda.io/v1alpha1
kind: Scan
metadata:
  name: node-specific-scan
  namespace: compliance-system
spec:
  profile: stig-k8s-v2r2-node
  scanType: node
  nodeSelector:
    kubernetes.io/hostname: "worker-node-1"
EOF

# 2. 查看节点扫描结果
kubectl get checkresult -l compliance-operator.alauda.io/scan=node-specific-scan -n compliance-system -o yaml
```

### 场景4：手动触发扫描

**适用于**：按需检查、问题验证

```bash
# 方法1：使用强制扫描注解（推荐）
kubectl annotate scan <scan-name> compliance-operator.alauda.io/force-scan=true -n compliance-system

# 方法2：使用便捷脚本
./scripts/trigger-scan.sh <scan-name> compliance-system

# 方法3：重置扫描状态
kubectl patch scan <scan-name> -n compliance-system --type merge --subresource=status \
  -p '{"status":{"phase":"Pending","message":"Manual trigger"}}'
```

### 场景5：综合合规套件

**适用于**：全面合规检查、企业级部署

```bash
# 1. 部署综合合规套件
kubectl apply -f examples/compliancesuite.yaml

# 2. 查看套件状态
kubectl get compliancesuite -n compliance-system

# 3. 查看所有扫描结果
kubectl get scan,checkresult -n compliance-system
```

## 🛡️ STIG Kubernetes v2r2 合规检查

本项目完整实现了 DISA STIG Kubernetes v2r2 合规标准，包含 35 个核心规则。

### 快速部署 STIG 检查

```bash
# 1. 部署 STIG 规则
kubectl apply -f stig-k8s-v2r2-rules/

# 2. 部署 STIG Profile
kubectl apply -f examples/stig-k8s-v2r2-profiles-complete-fixed.yaml

# 3. 执行 STIG 扫描
kubectl apply -f examples/stig-k8s-v2r2-scans-complete-fixed.yaml

# 4. 查看扫描结果
kubectl get scan,checkresult -n compliance-system
```

### STIG 规则分类

**Platform 类型规则（25个）**：
- API Server 安全配置（10个规则）
- Controller Manager 配置（3个规则）
- Scheduler 配置（2个规则）
- etcd 配置（3个规则）
- Pod 安全控制（2个规则）
- 其他安全控制（5个规则）

**Node 类型规则（10个）**：
- Kubelet 配置（6个规则）
- 文件权限检查（4个规则）

详细的 STIG 实现说明请参考：[examples/STIG-K8S-V2R2-README.md](examples/STIG-K8S-V2R2-README.md)

## 📊 报告和监控

### OpenSCAP 报告服务

Compliance Operator 内置了 OpenSCAP 报告服务，自动收集和管理 OpenSCAP 扫描生成的 HTML 报告。

#### 服务架构

- **自动部署**：compliance-operator 启动时自动创建报告服务
- **报告收集**：OpenSCAP scanner 扫描完成后自动上传 HTML 报告
- **按扫描分组**：报告按 scanId 分目录存储
- **临时存储**：使用 EmptyDir，Pod 重启后报告会丢失

#### 查看可用扫描

```bash
# 方法1：通过 curl 调用 API
curl http://openscap-report-service.compliance-system.svc.cluster.local:8080/list

# 方法2：通过 kubectl port-forward 访问
kubectl port-forward -n compliance-system svc/openscap-report-service 8080:8080 &
curl http://localhost:8080/list
```

#### 下载扫描报告

**方法1：通过 API 下载 ZIP 文件**

```bash
# 直接下载指定扫描的所有报告（zip格式）
curl -O http://openscap-report-service.compliance-system.svc.cluster.local:8080/download/{scanId}

# 使用 port-forward 下载
kubectl port-forward -n compliance-system svc/openscap-report-service 8080:8080 &
curl -O http://localhost:8080/download/scan-123
```

**方法2：通过 kubectl 直接访问文件**

```bash
# 查看报告服务 Pod
kubectl get pods -n compliance-system -l app=openscap-report-service

# 列出所有扫描目录
kubectl exec -n compliance-system deployment/openscap-report-service -- ls -la /reports/

# 查看特定扫描的报告文件
kubectl exec -n compliance-system deployment/openscap-report-service -- ls -la /reports/{scanId}/

# 下载单个报告文件
kubectl cp compliance-system/openscap-report-service-xxx:/reports/{scanId}/report.html ./report.html

# 下载整个扫描目录
kubectl cp compliance-system/openscap-report-service-xxx:/reports/{scanId}/ ./local-reports/
```

**方法3：创建和下载 ZIP 文件**

```bash
# 在 Pod 内创建 ZIP 文件
kubectl exec -n compliance-system deployment/openscap-report-service -- \
  sh -c "cd /reports/{scanId} && tar czf ../{scanId}.tar.gz *.html"

# 下载 ZIP 文件
kubectl cp compliance-system/openscap-report-service-xxx:/reports/{scanId}.tar.gz ./{scanId}.tar.gz

# 或者使用 zip 命令（如果 Pod 内有 zip 工具）
kubectl exec -n compliance-system deployment/openscap-report-service -- \
  sh -c "cd /reports/{scanId} && zip -r ../{scanId}.zip *.html"
```

#### 实用脚本示例

**批量下载所有扫描报告**

```bash
#!/bin/bash
# 获取所有可用扫描
SCANS=$(curl -s http://openscap-report-service.compliance-system.svc.cluster.local:8080/list | jq -r 'keys[]')

# 为每个扫描下载报告
for scan in $SCANS; do
  echo "Downloading reports for scan: $scan"
  curl -O http://openscap-report-service.compliance-system.svc.cluster.local:8080/download/$scan
done
```

**查看报告统计信息**

```bash
#!/bin/bash
# 通过 kubectl 查看报告统计
echo "=== OpenSCAP 报告统计 ==="
kubectl exec -n compliance-system deployment/openscap-report-service -- \
  sh -c 'for dir in /reports/*/; do
    if [ -d "$dir" ]; then
      scan=$(basename "$dir")
      count=$(ls -1 "$dir"/*.html 2>/dev/null | wc -l)
      size=$(du -sh "$dir" 2>/dev/null | cut -f1)
      echo "扫描: $scan, 报告数量: $count, 总大小: $size"
    fi
  done'
```

#### API 端点说明

| 端点 | 方法 | 描述 | 示例 |
|------|------|------|------|
| `/health` | GET | 健康检查 | `curl .../health` |
| `/list` | GET | 列出所有扫描 | `curl .../list` |
| `/upload` | POST | 上传报告（Scanner 使用） | 自动调用 |
| `/download/{scanId}` | GET | 下载扫描的 ZIP 文件 | `curl -O .../download/scan-123` |

#### 注意事项

1. **临时存储**：报告存储在 EmptyDir 中，Pod 重启后会丢失
2. **网络访问**：API 调用需要在集群内部或通过 port-forward
3. **文件大小**：大型报告建议使用 kubectl cp 方式下载
4. **权限要求**：kubectl 操作需要适当的 RBAC 权限

### 查看扫描报告

```bash
# 1. 使用Web界面查看报告
go run cmd/report-viewer/main.go -scan <scan-name> -serve -port 8080

# 2. 导出HTML报告
go run cmd/report-viewer/main.go -scan <scan-name> -export report.html

# 3. 查看扫描统计
kubectl get scan <scan-name> -n compliance-system -o jsonpath='{.status}'
```

### 监控扫描状态

```bash
# 1. 实时监控扫描进度
kubectl get scan -n compliance-system -w

# 2. 查看扫描日志
kubectl logs -l compliance-operator.alauda.io/scan=<scan-name> -n compliance-system -f

# 3. 查看失败的检查
kubectl get checkresult -n compliance-system \
  -o custom-columns="NAME:.metadata.name,SCAN:.spec.scanName,FAILED:.spec.ruleResults[?(@.status=='FAIL')].ruleId"
```

## 🔧 开发和定制

### 本地开发

```bash
# 1. 启动控制器（连接远程集群）
make run

# 2. 构建和推送镜像
make docker-build-all
make docker-push-all

# 3. 运行测试
make test
```

### 自定义规则

1. **创建规则文件**
```yaml
apiVersion: compliance-operator.alauda.io/v1alpha1
kind: Rule
metadata:
  name: custom-security-check
  namespace: compliance-system
spec:
  title: "Custom Security Check"
  description: "Custom security compliance check"
  severity: "medium"
  checkType: "platform"
  checkScript: |
    #!/bin/bash
    # 你的检查逻辑
    echo "Custom check passed"
    exit 0
```

2. **添加到Profile**
```yaml
apiVersion: compliance-operator.alauda.io/v1alpha1
kind: Profile
metadata:
  name: custom-profile
spec:
  title: "Custom Security Profile"
  rules:
    - name: custom-security-check
```

3. **执行扫描**
```yaml
apiVersion: compliance-operator.alauda.io/v1alpha1
kind: Scan
metadata:
  name: custom-scan
spec:
  profile: custom-profile
  scanType: platform
```

## 🚨 故障排查

### 常见问题

1. **扫描卡在Running状态**
```bash
# 检查Job状态
kubectl get jobs -l compliance-operator.alauda.io/scan=<scan-name> -n compliance-system

# 查看Pod日志
kubectl logs -l compliance-operator.alauda.io/scan=<scan-name> -n compliance-system
```

2. **ConfigMap创建失败**
```bash
# 检查scanner镜像版本
kubectl get jobs -l compliance-operator.alauda.io/scan=<scan-name> -n compliance-system -o yaml | grep image

# 确保使用最新的unified-scanner镜像
```

3. **权限问题**
```bash
# 检查ServiceAccount权限
kubectl describe serviceaccount compliance-scanner -n compliance-system

# 检查ClusterRole绑定
kubectl describe clusterrolebinding compliance-operator-manager
```

### 调试模式

```bash
# 1. 查看控制器日志
kubectl logs -l app=compliance-operator -n compliance-system -f

# 2. 启用详细日志
kubectl patch deployment compliance-operator-controller-manager -n compliance-system \
  --type='json' -p='[{"op": "add", "path": "/spec/template/spec/containers/0/args/-", "value": "--zap-log-level=debug"}]'
```

## 📈 性能优化

### 资源优化

- **CheckResult存储优化**：移除冗余字段，减少存储占用30-40%
- **智能资源清理**：自动清理临时资源，防止集群资源泄露
- **并发扫描**：支持多规则并行检查，提升扫描效率

### 最佳实践

1. **合理设置历史结果保留数量**
```yaml
spec:
  maxHistoricalResults: 5  # 根据需求调整
```

2. **使用节点选择器优化扫描范围**
```yaml
spec:
  nodeSelector:
    node-role.kubernetes.io/worker: ""  # 只扫描worker节点
```

3. **定时扫描避开高峰期**
```yaml
spec:
  schedule: "0 2 * * *"  # 凌晨2点执行
```

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 📄 许可证

本项目基于 Apache License 2.0 许可证开源。详细信息请参阅 [LICENSE](LICENSE) 文件。

## 🆘 支持

- 📖 [文档](docs/)
- 🐛 [问题反馈](https://github.com/alauda/compliance-operator/issues)
- 💬 [讨论区](https://github.com/alauda/compliance-operator/discussions)

---

**Compliance Operator** - 让 Kubernetes 合规检查变得简单高效！ 🚀 