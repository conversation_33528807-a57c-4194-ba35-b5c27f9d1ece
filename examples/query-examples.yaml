# CheckResult 查询示例
# 这些示例展示了如何使用标签选择器来查询和管理 CheckResult

# 1. 查询特定扫描的所有结果
apiVersion: v1
kind: ConfigMap
metadata:
  name: query-examples
  namespace: compliance-system
data:
  # 按扫描名称查询
  query-by-scan.sh: |
    #!/bin/bash
    # 查询特定扫描的所有结果
    kubectl get checkresult -n compliance-system -l compliance-operator.alauda.io/scan=node-specific-scan
    
  # 按状态查询
  query-by-status.sh: |
    #!/bin/bash
    # 查询所有失败的检查
    kubectl get checkresult -n compliance-system -o json | \
      jq -r '.items[] | select(.spec.status=="FAIL") | .metadata.name'
    
  # 按 Profile 查询
  query-by-profile.sh: |
    #!/bin/bash
    # 查询特定 Profile 的所有结果
    kubectl get checkresult -n compliance-system -l compliance-operator.alauda.io/profile=pod-security-standards
    
  # 按节点查询
  query-by-node.sh: |
    #!/bin/bash
    # 查询特定节点的所有检查结果
    kubectl get checkresult -n compliance-system -l compliance-operator.alauda.io/node=worker-node-1
    
  # 按节点角色查询
  query-by-node-role.sh: |
    #!/bin/bash
    # 查询所有 master 节点的检查结果
    kubectl get checkresult -n compliance-system -l compliance-operator.alauda.io/node-role=master
    
  # 复合查询
  complex-query.sh: |
    #!/bin/bash
    # 查询特定扫描中失败的平台检查
    kubectl get checkresult -n compliance-system \
      -l compliance-operator.alauda.io/scan=security-scan,compliance-operator.alauda.io/scan-type=platform \
      -o json | jq -r '.items[] | select(.spec.status=="FAIL") | .metadata.name'
    
  # 统计查询
  stats-query.sh: |
    #!/bin/bash
    # 统计各种状态的检查结果数量
    echo "=== Compliance Check Statistics ==="
    echo "Total CheckResults: $(kubectl get checkresult -n compliance-system --no-headers | wc -l)"
    echo "PASS: $(kubectl get checkresult -n compliance-system -o json | jq '[.items[] | select(.spec.status=="PASS")] | length')"
    echo "FAIL: $(kubectl get checkresult -n compliance-system -o json | jq '[.items[] | select(.spec.status=="FAIL")] | length')"
    echo "ERROR: $(kubectl get checkresult -n compliance-system -o json | jq '[.items[] | select(.spec.status=="ERROR")] | length')"
    echo "MANUAL: $(kubectl get checkresult -n compliance-system -o json | jq '[.items[] | select(.spec.status=="MANUAL")] | length')"

---
# 示例：查询结果的自定义资源
apiVersion: v1
kind: ConfigMap
metadata:
  name: advanced-queries
  namespace: compliance-system
data:
  # 生成合规报告
  compliance-report.sh: |
    #!/bin/bash
    echo "=== Compliance Report ==="
    echo "Generated: $(date)"
    echo ""
    
    # 按扫描分组统计
    for scan in $(kubectl get checkresult -n compliance-system -o json | jq -r '[.items[].metadata.labels."compliance-operator.alauda.io/scan"] | unique | .[]'); do
      echo "Scan: $scan"
      echo "  PASS: $(kubectl get checkresult -n compliance-system -l compliance-operator.alauda.io/scan=$scan -o json | jq '[.items[] | select(.spec.status=="PASS")] | length')"
      echo "  FAIL: $(kubectl get checkresult -n compliance-system -l compliance-operator.alauda.io/scan=$scan -o json | jq '[.items[] | select(.spec.status=="FAIL")] | length')"
      echo "  ERROR: $(kubectl get checkresult -n compliance-system -l compliance-operator.alauda.io/scan=$scan -o json | jq '[.items[] | select(.spec.status=="ERROR")] | length')"
      echo ""
    done
  
  # 查找问题检查
  find-issues.sh: |
    #!/bin/bash
    echo "=== Failed Checks ==="
    kubectl get checkresult -n compliance-system -o json | \
      jq -r '.items[] | select(.spec.status=="FAIL" or .spec.status=="ERROR") | 
             "\(.metadata.labels."compliance-operator.alauda.io/scan")/\(.metadata.labels."compliance-operator.alauda.io/rule"): \(.spec.status) - \(.spec.message)"'
    
  # 节点合规性概览
  node-compliance.sh: |
    #!/bin/bash
    echo "=== Node Compliance Overview ==="
    for node in $(kubectl get nodes --no-headers -o custom-columns=NAME:.metadata.name); do
      total=$(kubectl get checkresult -n compliance-system -l compliance-operator.alauda.io/node=$node --no-headers 2>/dev/null | wc -l)
      if [ $total -gt 0 ]; then
        pass=$(kubectl get checkresult -n compliance-system -l compliance-operator.alauda.io/node=$node -o json 2>/dev/null | jq '[.items[] | select(.spec.status=="PASS")] | length')
        fail=$(kubectl get checkresult -n compliance-system -l compliance-operator.alauda.io/node=$node -o json 2>/dev/null | jq '[.items[] | select(.spec.status=="FAIL")] | length')
        echo "Node $node: $pass/$total passed, $fail failed"
      fi
    done 