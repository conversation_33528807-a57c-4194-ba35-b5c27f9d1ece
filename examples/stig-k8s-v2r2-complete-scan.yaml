---
# STIG Kubernetes V2R2 Platform Scan
# Scans all platform-level security configurations
apiVersion: compliance-operator.alauda.io/v1alpha1
kind: Scan
metadata:
  name: stig-k8s-v2r2-platform-scan
  namespace: compliance-system
  labels:
    compliance-operator.alauda.io/scan-type: platform
    compliance-operator.alauda.io/standard: stig
    compliance-operator.alauda.io/version: v2r2
spec:
  profile: stig-k8s-v2r2-platform
  scanType: "platform"
  # schedule: "0 2 * * 0"  # Weekly scan at 2 AM on Sunday

---
# STIG Kubernetes V2R2 Node Scan  
# Scans all node-level security configurations
apiVersion: compliance-operator.alauda.io/v1alpha1
kind: Scan
metadata:
  name: stig-k8s-v2r2-node-scan
  namespace: compliance-system
  labels:
    compliance-operator.alauda.io/scan-type: node
    compliance-operator.alauda.io/standard: stig
    compliance-operator.alauda.io/version: v2r2
spec:
  profile: stig-k8s-v2r2-node
  scanType: "node"
  # schedule: "0 3 * * 0"  # Weekly scan at 3 AM on Sunday
  # nodeSelector:
  #   kubernetes.io/os: linux

---
# STIG Kubernetes V2R2 Complete Scan (Alternative: Single scan for both types)
# This is an alternative approach using a single scan that covers both platform and node checks
# apiVersion: compliance-operator.alauda.io/v1alpha1
# kind: Scan
# metadata:
#   name: stig-k8s-v2r2-complete-scan
#   namespace: compliance-system
#   labels:
#     compliance-operator.alauda.io/scan-type: complete
#     compliance-operator.alauda.io/standard: stig
#     compliance-operator.alauda.io/version: v2r2
# spec:
#   # Note: This would require a combined profile or ComplianceSuite
#   # For now, use the individual scans above
#   profile: stig-k8s-v2r2-platform  # Primary profile
#   scanType: "platform"
#   schedule: "0 1 * * 1"  # Weekly scan at 1 AM on Monday 