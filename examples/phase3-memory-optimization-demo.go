package main

import (
	"fmt"
	"runtime"
	"time"

	"github.com/alauda/compliance-operator/pkg/controller/scan"
	"github.com/go-logr/logr"
	batchv1 "k8s.io/api/batch/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
)

func main() {
	fmt.Println("=== Phase 3: Memory and Concurrency Optimization Demo ===")
	fmt.Println()

	// Create configuration with memory optimization enabled
	config := &scan.ScanControllerConfig{
		MaxMemoryUsage:            512, // 512MB
		GCThreshold:               256, // 256MB
		ConcurrencyControlEnabled: true,
		MaxConcurrentScans:        5,
		MemoryOptimizationEnabled: true,
		BatchOperationEnabled:     true,
		BatchSize:                 50,
	}

	fmt.Println("1. Configuration:")
	fmt.Printf("   - Memory Optimization: %v\n", config.MemoryOptimizationEnabled)
	fmt.Printf("   - Concurrency Control: %v\n", config.ConcurrencyControlEnabled)
	fmt.Printf("   - Max Memory Usage: %d MB\n", config.MaxMemoryUsage)
	fmt.Printf("   - GC Threshold: %d MB\n", config.GCThreshold)
	fmt.Printf("   - Max Concurrent Scans: %d\n", config.MaxConcurrentScans)
	fmt.Println()

	// Demo 1: Object Pools
	fmt.Println("2. Object Pools Demo:")
	demoObjectPools()
	fmt.Println()

	// Demo 2: Memory Manager
	fmt.Println("3. Memory Manager Demo:")
	demoMemoryManager(config)
	fmt.Println()

	// Demo 3: Performance Comparison
	fmt.Println("4. Performance Comparison:")
	demoPerformanceComparison()
	fmt.Println()

	fmt.Println("=== Demo Complete ===")
}

func demoObjectPools() {
	pools := scan.NewObjectPools()

	fmt.Println("   Creating and reusing objects...")

	// Demonstrate Job pool
	start := time.Now()
	for i := 0; i < 1000; i++ {
		job := pools.GetJob()
		job.Name = fmt.Sprintf("test-job-%d", i)
		job.Namespace = "test-namespace"
		pools.PutJob(job)
	}
	poolTime := time.Since(start)

	// Compare with direct allocation
	start = time.Now()
	for i := 0; i < 1000; i++ {
		// Direct allocation - create new Job each time
		job := &batchv1.Job{
			TypeMeta: metav1.TypeMeta{
				APIVersion: "batch/v1",
				Kind:       "Job",
			},
		}
		job.Name = fmt.Sprintf("test-job-%d", i)
		job.Namespace = "test-namespace"
		// No pooling - object gets GC'd
	}
	directTime := time.Since(start)

	fmt.Printf("   - Pool allocation (1000 jobs): %v\n", poolTime)
	fmt.Printf("   - Direct allocation (1000 jobs): %v\n", directTime)

	if poolTime < directTime {
		improvement := float64(directTime-poolTime) / float64(directTime) * 100
		fmt.Printf("   - Pool is %.1f%% faster\n", improvement)
	}

	// Show memory stats
	stats := pools.GetMemoryStats()
	fmt.Printf("   - Pool stats: Job=%d, ConfigMap=%d, CheckResult=%d\n",
		stats.JobPoolSize, stats.ConfigMapPoolSize, stats.CheckResultPoolSize)
}

func demoMemoryManager(config *scan.ScanControllerConfig) {
	log := logr.Discard()
	manager := scan.NewMemoryManager(config, log)

	fmt.Println("   Testing concurrency control...")

	// Test concurrency limits
	var activeScans []string
	for i := 0; i < 10; i++ {
		scanName := fmt.Sprintf("demo-scan-%d", i)
		if manager.CanStartScan() {
			manager.StartScan(scanName)
			activeScans = append(activeScans, scanName)
			fmt.Printf("   - Started scan: %s\n", scanName)
		} else {
			fmt.Printf("   - Scan %s denied (concurrency limit reached)\n", scanName)
		}
	}

	// Show current stats
	stats := manager.GetRuntimeStats()
	fmt.Printf("   - Active scans: %d/%d\n", stats.ActiveScanCount, stats.MaxConcurrency)
	fmt.Printf("   - Current memory: %d MB\n", stats.CurrentMemoryMB)
	fmt.Printf("   - Peak memory: %d MB\n", stats.PeakMemoryMB)
	fmt.Printf("   - Throttle active: %v\n", stats.ThrottleActive)

	// Clean up
	for _, scanName := range activeScans {
		manager.EndScan(scanName)
	}

	finalStats := manager.GetRuntimeStats()
	fmt.Printf("   - Final active scans: %d\n", finalStats.ActiveScanCount)
}

func demoPerformanceComparison() {
	fmt.Println("   Memory usage before optimization:")
	var m1 runtime.MemStats
	runtime.ReadMemStats(&m1)
	fmt.Printf("   - Heap: %d KB\n", m1.HeapAlloc/1024)
	fmt.Printf("   - System: %d KB\n", m1.Sys/1024)

	// Simulate memory-intensive operations without pools
	fmt.Println("   - Simulating operations without pools...")
	start := time.Now()
	for i := 0; i < 10000; i++ {
		// Direct allocation (would normally cause more GC pressure)
		data := make(map[string]string)
		data["key"] = fmt.Sprintf("value-%d", i)
		slice := make([]string, 0, 10)
		slice = append(slice, fmt.Sprintf("item-%d", i))
		// Use the variables to avoid compiler warnings
		_ = data
		_ = slice
		// Objects go out of scope and need GC
	}
	directTime := time.Since(start)

	var m2 runtime.MemStats
	runtime.ReadMemStats(&m2)
	fmt.Printf("   - Time without pools: %v\n", directTime)
	fmt.Printf("   - Heap after: %d KB\n", m2.HeapAlloc/1024)

	// Force GC and measure
	runtime.GC()
	var m3 runtime.MemStats
	runtime.ReadMemStats(&m3)
	fmt.Printf("   - Heap after GC: %d KB\n", m3.HeapAlloc/1024)

	// Now with pools
	fmt.Println("   - Simulating operations with pools...")
	pools := scan.NewObjectPools()
	start = time.Now()
	for i := 0; i < 10000; i++ {
		// Pool allocation
		mapPtr := pools.GetMap()
		(*mapPtr)["key"] = fmt.Sprintf("value-%d", i)
		pools.PutMap(mapPtr)

		slicePtr := pools.GetStringSlice()
		*slicePtr = append(*slicePtr, fmt.Sprintf("item-%d", i))
		pools.PutStringSlice(slicePtr)
	}
	poolTime := time.Since(start)

	var m4 runtime.MemStats
	runtime.ReadMemStats(&m4)
	fmt.Printf("   - Time with pools: %v\n", poolTime)
	fmt.Printf("   - Heap after pools: %d KB\n", m4.HeapAlloc/1024)

	if directTime > poolTime {
		improvement := float64(directTime-poolTime) / float64(directTime) * 100
		fmt.Printf("   - Pools are %.1f%% faster\n", improvement)
	}

	memoryReduction := float64(m2.HeapAlloc-m4.HeapAlloc) / float64(m2.HeapAlloc) * 100
	if memoryReduction > 0 {
		fmt.Printf("   - Memory usage reduced by %.1f%%\n", memoryReduction)
	}
}
