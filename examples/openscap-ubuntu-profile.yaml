apiVersion: compliance-operator.alauda.io/v1alpha1
kind: Profile
metadata:
  name: ubuntu-stig-openscap
  namespace: compliance-system
spec:
  title: "Ubuntu 22.04 STIG Profile (OpenSCAP)"
  description: "OpenSCAP-based Ubuntu 22.04 STIG compliance profile using datastream scanning"
  version: "V2R2"
  dataStream:
    contentFile: "ssg-ubuntu2204-ds.xml"
    contentImage: "registry.alauda.cn:60070/ait/compliance-content:v0.0.0-beta.1.gcc58491d"
    profileId: "xccdf_org.ssgproject.content_profile_stig"
    scanType: "node"
---
apiVersion: compliance-operator.alauda.io/v1alpha1
kind: Scan
metadata:
  name: ubuntu-openscap-scan
  namespace: compliance-system
spec:
  profile: ubuntu-stig-openscap
  scanType: node