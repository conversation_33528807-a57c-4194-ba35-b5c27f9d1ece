apiVersion: compliance-operator.alauda.io/v1alpha1
kind: Profile
metadata:
  name: ubuntu-stig-openscap
  namespace: compliance-system
spec:
  title: "Ubuntu 22.04 STIG Profile (OpenSCAP)"
  description: "OpenSCAP-based Ubuntu 22.04 STIG compliance profile using datastream scanning"
  version: "V2R2"
  dataStream:
    contentFile: "ssg-ubuntu2204-ds.xml"
    contentImage: "registry.alauda.cn:60070/test/compliance/os-content:latest"
    profileId: "xccdf_org.ssgproject.content_profile_stig"
    scanType: "node"
---
# apiVersion: compliance-operator.alauda.io/v1alpha1
# kind: Profile  
# metadata:
#   name: slmicro-stig-openscap
#   namespace: compliance-system
# spec:
#   title: "SUSE Linux Micro 5 STIG Profile (OpenSCAP)"
#   description: "OpenSCAP-based SUSE Linux Micro 5 STIG compliance profile using datastream scanning"
#   version: "V2R2"
#   dataStream:
#     contentFile: "ssg-slmicro5-ds.xml"
#     contentImage: "registry.alauda.cn:60070/test/compliance/os-content:latest"
#     profileId: "xccdf_org.ssgproject.content_profile_stig"
#     scanType: "node" 