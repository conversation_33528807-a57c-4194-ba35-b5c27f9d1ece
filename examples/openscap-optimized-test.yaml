apiVersion: batch/v1
kind: Job
metadata:
  name: openscap-optimized-test
  namespace: compliance-system
  labels:
    compliance-operator.alauda.io/scan: "optimized-test"
    compliance-operator.alauda.io/scan-type: "openscap"
spec:
  template:
    metadata:
      labels:
        compliance-operator.alauda.io/scan: "optimized-test"
    spec:
      restartPolicy: Never
      hostNetwork: false
      hostPID: false
      securityContext:
        runAsUser: 0
        runAsGroup: 0
        fsGroup: 0
      # InitContainer to extract content from content image
      initContainers:
      - name: content-extractor
        image: registry.alauda.cn:60070/test/compliance/os-content:latest
        command: ["/bin/sh", "-c"]
        args:
        - |
          echo "Extracting content files..."
          cp -v /content/*.xml /shared-content/
          ls -la /shared-content/
          echo "Content extraction completed"
        volumeMounts:
        - name: shared-content
          mountPath: /shared-content
        securityContext:
          runAsUser: 0
          runAsGroup: 0
          allowPrivilegeEscalation: false
          readOnlyRootFilesystem: false
          capabilities:
            drop:
            - ALL
      # Main OpenSCAP scanner container with optimizations
      containers:
      - name: openscap-scanner
        image: registry.alauda.cn:60070/test/compliance-acp/openscap-scanner:debian12-success
        env:
        - name: PROFILE
          value: "xccdf_org.ssgproject.content_profile_stig"
        - name: CONTENT
          value: "ssg-ubuntu2204-ds.xml"
        - name: NODE_NAME
          value: "**************"
        - name: SCAN_ID
          value: "optimized-test-001"
        - name: HOSTROOT
          value: "/host"
        - name: REPORT_DIR
          value: "/reports"
        - name: CONTENT_IMAGE
          value: "registry.alauda.cn:60070/test/compliance/os-content:latest"
        volumeMounts:
        - name: shared-content
          mountPath: /shared-content
          readOnly: true
        - name: host-root
          mountPath: /host
          readOnly: true
        - name: reports
          mountPath: /reports
        securityContext:
          runAsUser: 0
          runAsGroup: 0
          privileged: true
          allowPrivilegeEscalation: true
          readOnlyRootFilesystem: false
          capabilities:
            add:
            - SYS_ADMIN
            - DAC_OVERRIDE
            - FOWNER
            - SETUID
            - SETGID
      # Volumes
      volumes:
      - name: shared-content
        emptyDir: {}
      - name: host-root
        hostPath:
          path: /
          type: Directory
      - name: reports
        emptyDir: {}
      # Node selection (optional)
      nodeSelector:
        kubernetes.io/hostname: "**************" 