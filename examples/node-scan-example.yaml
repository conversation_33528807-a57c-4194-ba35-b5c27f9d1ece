---
apiVersion: compliance-operator.alauda.io/v1alpha1
kind: Scan
metadata:
  name: node-security-scan
  namespace: compliance-system
spec:
  # 指定要使用的合规配置文件
  profile: stig-k8s-v2r2-node-profile
  
  # 扫描类型设置为节点扫描
  scanType: node
  
  # 节点选择器，用于选择要扫描的特定节点
  nodeSelector:
    # 选择特定IP的节点（示例）
    kubernetes.io/hostname: "**************"
    
    # 也可以使用其他标签选择器
    # node-role.kubernetes.io/worker: ""
    # compliance-scan: enabled 