---
apiVersion: compliance-operator.alauda.io/v1alpha1
kind: Profile
metadata:
  name: platform-security-profile
  namespace: compliance-system
spec:
  title: "Kubernetes Platform Security Profile"
  description: "Platform-level security compliance profile focusing on API server, controller manager, and etcd configurations"
  rules:
    # API Server 安全配置
    - name: stig-k8s-api-server-anonymous-auth-disabled
    - name: stig-k8s-api-server-authorization-mode
    - name: stig-k8s-api-server-audit-log-enabled
    - name: stig-k8s-api-server-tls-min-version
    - name: stig-k8s-api-server-secure-port-set
    - name: stig-k8s-api-server-insecure-port-disabled
    
    # Controller Manager 安全配置
    - name: stig-k8s-controller-manager-secure-binding
    - name: stig-k8s-controller-manager-profiling-disabled
    - name: stig-k8s-controller-manager-use-service-account-credentials
    
    # ETCD 安全配置
    - name: stig-k8s-etcd-client-cert-auth
    - name: stig-k8s-etcd-auto-tls-disabled
    - name: stig-k8s-etcd-cert-file
    
    # 准入控制和策略
    - name: stig-k8s-validating-admission-webhook
    - name: stig-k8s-pod-security-standards-enabled
    - name: stig-k8s-network-policies-configured
  
  selections:
    # API Server
    - id: stig-k8s-api-server-anonymous-auth-disabled
      selected: true
    - id: stig-k8s-api-server-authorization-mode
      selected: true
    - id: stig-k8s-api-server-audit-log-enabled
      selected: true
    - id: stig-k8s-api-server-tls-min-version
      selected: true
    - id: stig-k8s-api-server-secure-port-set
      selected: true
    - id: stig-k8s-api-server-insecure-port-disabled
      selected: true
    
    # Controller Manager
    - id: stig-k8s-controller-manager-secure-binding
      selected: true
    - id: stig-k8s-controller-manager-profiling-disabled
      selected: true
    - id: stig-k8s-controller-manager-use-service-account-credentials
      selected: true
    
    # ETCD
    - id: stig-k8s-etcd-client-cert-auth
      selected: true
    - id: stig-k8s-etcd-auto-tls-disabled
      selected: true
    - id: stig-k8s-etcd-cert-file
      selected: true
    
    # 准入控制和策略
    - id: stig-k8s-validating-admission-webhook
      selected: true
    - id: stig-k8s-pod-security-standards-enabled
      selected: true
    - id: stig-k8s-network-policies-configured
      selected: true 