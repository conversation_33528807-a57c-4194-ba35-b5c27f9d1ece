apiVersion: v1
kind: Namespace
metadata:
  name: compliance-system
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: openscap-report-service
  namespace: compliance-system
  labels:
    app: openscap-report-service
    component: report-service
spec:
  replicas: 1
  selector:
    matchLabels:
      app: openscap-report-service
  template:
    metadata:
      labels:
        app: openscap-report-service
        component: report-service
    spec:
      containers:
      - name: openscap-report-service
        image: build-harbor.alauda.cn/test/compliance/openscap-report-service:latest
        imagePullPolicy: Always
        ports:
        - containerPort: 8080
          name: http
        env:
        - name: REPORTS_DIR
          value: "/reports"
        - name: PORT
          value: "8080"
        - name: TZ
          value: "Asia/Shanghai"
        volumeMounts:
        - name: reports-storage
          mountPath: /reports
        resources:
          requests:
            memory: "128Mi"
            cpu: "100m"
          limits:
            memory: "512Mi"
            cpu: "500m"
        livenessProbe:
          httpGet:
            path: /health
            port: 8080
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
        readinessProbe:
          httpGet:
            path: /health
            port: 8080
          initialDelaySeconds: 5
          periodSeconds: 5
          timeoutSeconds: 3
          failureThreshold: 3
        securityContext:
          runAsNonRoot: true
          runAsUser: 65534
          readOnlyRootFilesystem: false
          allowPrivilegeEscalation: false
          capabilities:
            drop:
            - ALL
      volumes:
      - name: reports-storage
        emptyDir: {}
      securityContext:
        fsGroup: 65534
      restartPolicy: Always
---
apiVersion: v1
kind: Service
metadata:
  name: openscap-report-service
  namespace: compliance-system
  labels:
    app: openscap-report-service
    component: report-service
spec:
  type: ClusterIP
  ports:
  - port: 8080
    targetPort: 8080
    protocol: TCP
    name: http
  selector:
    app: openscap-report-service
---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: openscap-reports-pvc
  namespace: compliance-system
  labels:
    app: openscap-report-service
    component: report-service
spec:
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: 10Gi
  # storageClassName: standard  # Uncomment and adjust if needed
---
# Optional: Port forward service for local access
# kubectl port-forward -n compliance-system svc/openscap-report-service 8080:8080
# Then access at: http://localhost:8080 