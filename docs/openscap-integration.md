# OpenSCAP Integration Guide

## Overview

The Compliance Operator now supports OpenSCAP datastream-based scanning in addition to the existing rule-based scanning. This integration allows you to leverage existing SCAP content (like STIG datastreams) for comprehensive compliance scanning.

## Features

### 🔍 **Datastream Scanning**
- Support for SCAP datastream files (`.xml` format)
- Automatic content extraction from container images
- Multiple profile support within a single datastream

### 📊 **Comprehensive Reporting**
- HTML reports with detailed findings
- XML results with structured data
- Automatic statistics calculation
- Smart storage handling for large reports

### 🗄️ **Intelligent Storage**
- **Small reports** (< 1MB): Stored in ConfigMaps
- **Large reports** (> 1MB): Stored in PersistentVolumeClaims with metadata in ConfigMaps
- Compressed XML results to optimize storage

### 🔄 **Seamless Integration**
- Works alongside existing rule-based scanning
- Same scan controller and CRD structure
- Automatic scanner type detection

## Architecture

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   Profile CRD   │    │   Scan CRD       │    │ CheckResult CRD │
│                 │    │                  │    │                 │
│ DataStream:     │───▶│ profile: ubuntu- │───▶│ RuleResults[]   │
│ - contentFile   │    │   stig-openscap  │    │ Scanner: openscap│
│ - contentImage  │    │ scanType: node    │    │ Statistics      │
│ - profileId     │    │                  │    │                 │
└─────────────────┘    └──────────────────┘    └─────────────────┘
         │                       │                       │
         ▼                       ▼                       ▼
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│ Content Image   │    │ OpenSCAP Jobs    │    │ Report Storage  │
│                 │    │                  │    │                 │
│ /content/       │    │ InitContainer:   │    │ ConfigMap or    │
│ ├─ssg-ubuntu*.xml   │ │ - Extract content│    │ PVC based on    │
│ └─ssg-slmicro*.xml  │ │ Main Container:  │    │ report size     │
└─────────────────┘    │ - Run OpenSCAP   │    │                 │
                       │ - Generate reports│    │                 │
                       └──────────────────┘    └─────────────────┘
```

## Quick Start

### 1. Create OpenSCAP Profile

```yaml
apiVersion: compliance-operator.alauda.io/v1alpha1
kind: Profile
metadata:
  name: ubuntu-stig-openscap
  namespace: compliance-system
spec:
  title: "Ubuntu 22.04 STIG Profile (OpenSCAP)"
  description: "OpenSCAP-based Ubuntu 22.04 STIG compliance profile"
  version: "V2R2"
  dataStream:
    contentFile: "ssg-ubuntu2204-ds.xml"
    contentImage: "registry.alauda.cn:60070/test/compliance/os-content:latest"
    profileId: "xccdf_org.ssgproject.content_profile_stig"
    scanType: "node"
```

### 2. Create Scan

```yaml
apiVersion: compliance-operator.alauda.io/v1alpha1
kind: Scan
metadata:
  name: ubuntu-openscap-scan
  namespace: compliance-system
spec:
  profile: ubuntu-stig-openscap
  scanType: node
  nodeSelector:
    kubernetes.io/os: linux
```

### 3. Apply and Monitor

```bash
# Apply the profile and scan
kubectl apply -f examples/openscap-ubuntu-profile.yaml
kubectl apply -f examples/openscap-scan-example.yaml

# Monitor scan progress
kubectl get scans -n compliance-system
kubectl get jobs -n compliance-system

# Check results
kubectl get checkresults -n compliance-system
kubectl get configmaps -l compliance-operator.alauda.io/scanner=openscap -n compliance-system
```

## Profile Configuration

### DataStream Specification

```yaml
spec:
  dataStream:
    contentFile: "ssg-ubuntu2204-ds.xml"     # SCAP datastream file name
    contentImage: "registry.example.com/content:latest"  # Container image with content
    profileId: "xccdf_org.ssgproject.content_profile_stig"  # XCCDF profile ID
    scanType: "node"  # node, platform, or both
```

### Available Content

| OS | Content File | Profile ID | Description |
|----|--------------|------------|-------------|
| Ubuntu 22.04 | `ssg-ubuntu2204-ds.xml` | `xccdf_org.ssgproject.content_profile_stig` | Ubuntu STIG profile |
| SUSE Linux Micro 5 | `ssg-slmicro5-ds.xml` | `xccdf_org.ssgproject.content_profile_stig` | SUSE Micro STIG profile |

## Report Access

### Small Reports (ConfigMap Storage)

```bash
# Get the ConfigMap name
kubectl get configmaps -l compliance-operator.alauda.io/scanner=openscap -n compliance-system

# Extract HTML report
kubectl get configmap <configmap-name> -o jsonpath='{.data.html_report_base64}' | base64 -d > report.html

# Extract scan results JSON
kubectl get configmap <configmap-name> -o jsonpath='{.data.scan_result}' | jq .
```

### Large Reports (PVC Storage)

```bash
# Check report metadata
kubectl get configmap <configmap-name> -o jsonpath='{.data.scan_result}' | jq .htmlReport

# Access via temporary pod
kubectl run report-access --image=alpine --rm -it -- sh
# Inside pod: mount PVC and access files
```

## Scanner Job Configuration

The OpenSCAP scanner runs as Kubernetes Jobs with the following characteristics:

### Platform Scans
- **Privileges**: Minimal (no privileged mode)
- **Mounts**: `/host/proc`, `/host/etc/kubernetes`
- **Purpose**: Kubernetes API and configuration scanning

### Node Scans
- **Privileges**: Privileged mode with host access
- **Mounts**: Full host filesystem at `/host`
- **Purpose**: Operating system and file system scanning

### InitContainer Pattern
All OpenSCAP jobs use an InitContainer to extract SCAP content:

```yaml
initContainers:
- name: content-extractor
  image: registry.alauda.cn:60070/test/compliance/os-content:latest
  command: ["/bin/sh", "-c"]
  args:
  - |
    echo 'Extracting content files...'
    cp -v /content/*.xml /shared-content/
    ls -la /shared-content/
    echo 'Content extraction completed'
  volumeMounts:
  - name: shared-content
    mountPath: /shared-content
```

## Troubleshooting

### Common Issues

#### 1. Profile Not Found
```
ERROR: No profiles found or error reading datastream
```
**Solution**: Verify the `profileId` matches available profiles in the datastream.

#### 2. Content Extraction Failed
```
ERROR: Content file not found: ssg-ubuntu2204-ds.xml
```
**Solution**: Check that the `contentImage` contains the specified `contentFile`.

#### 3. Large Report Storage
```
WARNING: HTML report too large for ConfigMap (2500000 bytes)
```
**Solution**: Reports > 1MB are automatically stored in PVC. Ensure PVC storage is available.

#### 4. Scanner Pod Errors
```
Pod status: Error, Exit code: 1
```
**Solution**: Check pod logs for OpenSCAP execution errors:
```bash
kubectl logs <pod-name> -c openscap-scanner -n compliance-system
```

### Debugging Commands

```bash
# Check scan status
kubectl get scan <scan-name> -o yaml -n compliance-system

# List OpenSCAP jobs
kubectl get jobs -l compliance-operator.alauda.io/scanner=openscap -n compliance-system

# Check job details
kubectl describe job <job-name> -n compliance-system

# View scanner logs
kubectl logs <pod-name> -c openscap-scanner -n compliance-system

# Check ConfigMap results
kubectl get configmaps -l compliance-operator.alauda.io/scanner=openscap -n compliance-system
```

## Performance Considerations

### Resource Requirements

| Component | CPU | Memory | Storage |
|-----------|-----|--------|---------|
| Platform Scanner | 100m | 256Mi | - |
| Node Scanner | 200m | 512Mi | - |
| Reports PVC | - | - | 1Gi |

### Scan Duration

| Scan Type | Typical Duration | Rules Checked |
|-----------|------------------|---------------|
| Platform | 2-5 minutes | 50-100 |
| Node | 5-15 minutes | 200-300 |

### Storage Usage

| Report Type | Size Range | Storage Method |
|-------------|------------|----------------|
| Small HTML | < 1MB | ConfigMap |
| Large HTML | 1-5MB | PVC + Metadata ConfigMap |
| XML Results | 1-10MB | Compressed in ConfigMap |

## Security Considerations

### Privileges
- **Platform scans**: Run with minimal privileges
- **Node scans**: Require privileged access for host filesystem scanning
- **Content extraction**: Runs as non-privileged InitContainer

### Network Access
- Scanner pods do not require external network access
- Content is pre-loaded in container images
- Results are stored within the cluster

### Data Sensitivity
- Reports may contain sensitive system information
- Use RBAC to control access to ConfigMaps and PVCs
- Consider encryption at rest for sensitive environments

## Migration from Rule-Based Scanning

### Comparison

| Feature | Rule-Based | OpenSCAP DataStream |
|---------|------------|-------------------|
| Content Source | Individual Rule CRDs | SCAP Datastream |
| Scanner | Unified Scanner | OpenSCAP Scanner |
| Report Format | Custom HTML | OpenSCAP HTML |
| Standards Support | Custom | SCAP/XCCDF Standard |
| Rule Coverage | Manual definition | Comprehensive (200+ rules) |

### Migration Steps

1. **Identify equivalent profiles**: Map existing rule-based profiles to SCAP datastreams
2. **Create datastream profiles**: Define new Profile CRDs with `dataStream` specification
3. **Test scanning**: Run parallel scans to compare results
4. **Update automations**: Modify CI/CD pipelines to use new profile names
5. **Deprecate old profiles**: Gradually phase out rule-based profiles

## Best Practices

### 1. Profile Management
- Use descriptive profile names that include the OS and standard
- Include version information in profile descriptions
- Group related profiles by namespace or labels

### 2. Scan Scheduling
- Schedule scans during maintenance windows for node scans
- Use different schedules for platform vs node scans
- Monitor resource usage during scans

### 3. Result Management
- Regularly clean up old scan results
- Archive important compliance reports
- Set up monitoring for scan failures

### 4. Content Updates
- Regularly update content images with latest SCAP datastreams
- Test new content versions before production deployment
- Maintain version history of content images

## Integration Examples

### CI/CD Pipeline

```bash
#!/bin/bash
# compliance-scan.sh

# Apply OpenSCAP profile and scan
kubectl apply -f profiles/ubuntu-stig-openscap.yaml
kubectl apply -f scans/ubuntu-compliance-scan.yaml

# Wait for completion
kubectl wait --for=condition=Complete scan/ubuntu-compliance-scan --timeout=30m

# Extract results
SCAN_RESULT=$(kubectl get scan ubuntu-compliance-scan -o jsonpath='{.status.result}')
if [ "$SCAN_RESULT" != "Compliant" ]; then
    echo "Compliance scan failed: $SCAN_RESULT"
    exit 1
fi

echo "Compliance scan passed successfully"
```

### Monitoring Integration

```yaml
# Prometheus AlertManager rule
groups:
- name: compliance
  rules:
  - alert: ComplianceScanFailed
    expr: kube_job_status_failed{job_name=~".*openscap.*"} > 0
    for: 5m
    labels:
      severity: warning
    annotations:
      summary: "OpenSCAP compliance scan failed"
      description: "Job {{ $labels.job_name }} has failed"
```

## Conclusion

The OpenSCAP integration provides a powerful, standards-based approach to compliance scanning that complements the existing rule-based system. By leveraging industry-standard SCAP content, organizations can achieve comprehensive compliance coverage with minimal configuration.

For questions or issues, please refer to the troubleshooting section or check the project documentation. 