# Scan Controller Troubleshooting Guide

## Common Issues and Solutions

### 1. "Scan not found" Error After Controller Restart

#### Problem Description
After controller restart, you may see repeated errors like:
```
ERROR controllers.Scan Failed to update Scan status {"error": "scans.compliance-operator.alauda.io \"scan-name\" not found"}
```

#### Root Cause Analysis
This is a **Race Condition** issue that occurs when:

1. **Resource Deletion**: A Scan object gets deleted (manually by user, cascade deletion from parent resource, etc.)
2. **Queue Persistence**: controller-runtime's work queue still contains reconcile requests for the deleted object
3. **Status Update Failure**: When the controller processes these queued requests, the object no longer exists, causing status update operations to fail

#### Timeline of Events
```
Time 1: Scan object exists, controller queues reconcile requests
Time 2: Scan object gets deleted (by user/system)
Time 3: Controller processes queued requests → Object not found error
```

#### Solution Applied
The fix involves adding proper error handling in status update methods to gracefully handle object deletion scenarios:

**Before Fix:**
```go
if err := r.Status().Update(ctx, &latestScan); err != nil {
    if errors.IsConflict(err) {
        // Only handled conflict errors
        continue
    }
    // All other errors (including NotFound) caused failures
    return ctrl.Result{}, err
}
```

**After Fix:**
```go
if err := r.Status().Update(ctx, &latestScan); err != nil {
    if errors.IsNotFound(err) {
        // Gracefully handle object deletion
        r.Log.Info("Scan object was deleted during status update", "scan", scan.Name)
        return ctrl.Result{}, nil
    }
    if errors.IsConflict(err) {
        continue // Retry on conflicts
    }
    return ctrl.Result{}, err
}
```

#### Fixed Methods
The following methods have been updated to handle object deletion gracefully:
1. `updateScanStatus()`
2. `updateScanStatusWithStartTime()`
3. `updateScanStatusWithEndTime()`

### 2. Prevention Best Practices

#### For Operators
1. **Graceful Deletion**: Use `kubectl delete --wait=true` to ensure proper cleanup
2. **Monitor Finalizers**: Check that finalizers are properly removed during deletion
3. **Avoid Force Deletion**: Don't use `--force` unless absolutely necessary

#### For Developers
1. **Proper Error Handling**: Always check for `errors.IsNotFound()` in status update operations
2. **Finalizer Management**: Ensure finalizers are added/removed correctly
3. **Graceful Shutdown**: Implement proper cleanup in deletion handlers

### 3. Monitoring and Diagnostics

#### Check Object Status
```bash
# Check if scan object exists
kubectl get scans -n compliance-system

# Check scan object details
kubectl describe scan <scan-name> -n compliance-system

# Check finalizers
kubectl get scan <scan-name> -n compliance-system -o yaml | grep -A5 finalizers
```

#### Check Controller Logs
```bash
# Check controller logs for errors
kubectl logs -n compliance-system deployment/compliance-operator -f

# Filter for specific scan errors
kubectl logs -n compliance-system deployment/compliance-operator | grep "scan-name"
```

#### Check Work Queue Status
```bash
# Check if there are pending reconcile requests
kubectl get events -n compliance-system --field-selector involvedObject.name=<scan-name>
```

### 4. Related Issues and Solutions

#### Issue: Jobs Not Cleaned Up
**Symptom**: Scan is deleted but associated Jobs remain
**Solution**: Check TTLSecondsAfterFinished setting and Job cleanup logic

#### Issue: CheckResults Orphaned
**Symptom**: CheckResults exist without parent Scan
**Solution**: Verify owner reference setting and cascade deletion

#### Issue: Finalizer Stuck
**Symptom**: Scan object stuck in Terminating state
**Solution**: Check finalizer removal logic and manually remove if necessary

### 5. Emergency Recovery

If the controller is stuck in an error loop:

```bash
# 1. Scale down the controller
kubectl scale deployment compliance-operator --replicas=0 -n compliance-system

# 2. Clean up problematic resources
kubectl delete scan <problematic-scan> --force --grace-period=0 -n compliance-system

# 3. Scale up the controller
kubectl scale deployment compliance-operator --replicas=1 -n compliance-system
```

### 6. Code References

The fix has been applied to the following files:
- `pkg/controller/scan/controller.go`: Lines around updateScanStatus methods
- Added proper `errors.IsNotFound()` handling in all status update operations

### 7. Testing the Fix

To verify the fix works:

1. Create a scan object
2. Let it start processing (controller logs show "Initializing scan")
3. Delete the scan object while it's being processed
4. Check controller logs - should see graceful handling messages instead of errors

Expected log messages after fix:
```
INFO controllers.Scan Scan object was deleted during status update {"scan": "scan-name"}
```

Instead of error messages:
```
ERROR controllers.Scan Failed to update Scan status {"error": "scans.compliance-operator.alauda.io \"scan-name\" not found"}
``` 