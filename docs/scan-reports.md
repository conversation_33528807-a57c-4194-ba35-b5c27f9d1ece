# Compliance Scan Reports

## Overview

The Compliance Operator automatically generates HTML reports for completed scans. These reports provide a comprehensive view of compliance check results in a format similar to the [STIG Viewer](https://stigviewer.com/stigs/kubernetes).

## Features

- **Comprehensive Overview**: Scan metadata, timing, and statistics
- **Detailed Findings**: Each check result with ID, severity, status, and description
- **Visual Design**: Professional styling inspired by STIG Viewer
- **Easy Access**: Reports stored as ConfigMaps in the cluster
- **Export Options**: View in browser or export to HTML files

## Report Generation

Reports are automatically generated when a scan completes successfully. The report generation process:

1. **Triggered**: Automatically after scan completion
2. **Data Collection**: Gathers scan, profile, and check result information
3. **HTML Generation**: Creates a styled HTML report using Go templates
4. **Storage**: Saves the report in a ConfigMap named `<scan-name>-report`
5. **Reference**: Updates the scan status with the ConfigMap name

## Report Contents

### Header Section
- Scan name and profile title
- Visual gradient header similar to STIG Viewer

### Overview Section
- **Scan Information**: Name, profile, scan type, status
- **Timing**: Start time, completion time, report generation time
- **Profile Details**: Title, description, total rules
- **Statistics**: Color-coded counts for pass/fail/error/manual results

### Findings Section
- **All Check Results**: Sorted by severity (High → Medium → Low) then by ID
- **Finding Details**: 
  - Rule ID (e.g., V-242379)
  - Severity badge (High/Medium/Low)
  - Status badge (PASS/FAIL/ERROR/MANUAL)
  - Node name (for node-level checks)
  - Timestamp
  - Full description
  - Check message/evidence

## Accessing Reports

### Method 1: Direct ConfigMap Access

```bash
# Get the report ConfigMap name
kubectl get scan <scan-name> -o jsonpath='{.status.reportConfigMap}'

# Export the HTML report
kubectl get configmap <report-configmap-name> -o jsonpath='{.data.report\.html}' > report.html

# Open in browser
open report.html  # macOS
xdg-open report.html  # Linux
```

### Method 2: Report Viewer Tool

The compliance operator includes a convenient report viewer tool:

```bash
# Serve report via HTTP (recommended)
go run cmd/report-viewer/main.go -scan <scan-name> -serve -port 8080

# Export to file
go run cmd/report-viewer/main.go -scan <scan-name> -export report.html

# Print to stdout
go run cmd/report-viewer/main.go -scan <scan-name>
```

### Method 3: Kubernetes Dashboard

If you have Kubernetes Dashboard installed, you can:

1. Navigate to ConfigMaps in the compliance-system namespace
2. Find the `<scan-name>-report` ConfigMap
3. View the `report.html` data key
4. Copy the content to an HTML file

## Report Examples

### Successful Scan Report
- Green statistics showing passed checks
- Detailed findings with PASS status
- Professional layout with clear navigation

### Failed Compliance Scan
- Red statistics highlighting failures
- Failed checks with detailed error messages
- Recommendations for remediation

### Mixed Results
- Color-coded statistics showing the distribution
- Clear separation between passed and failed checks
- Manual review items highlighted

## Configuration

### Report Storage
- **Location**: ConfigMaps in the same namespace as the scan
- **Naming**: `<scan-name>-report`
- **Ownership**: ConfigMaps are owned by the scan (automatic cleanup)
- **Labels**: Tagged with `compliance-operator.alauda.io/report: true`

### Report Content
- **Template**: Customizable Go template in `pkg/controller/scan/report.go`
- **Styling**: Embedded CSS for offline viewing
- **Data**: Includes all scan metadata and check results

## Troubleshooting

### Report Not Generated
1. Check scan status: `kubectl get scan <scan-name> -o yaml`
2. Look for errors in controller logs
3. Verify scan completed successfully
4. Check if ConfigMap exists: `kubectl get configmap <scan-name>-report`

### Report ConfigMap Missing
1. Check if scan has owner reference
2. Verify RBAC permissions for ConfigMap creation
3. Look for resource quotas or limits

### Styling Issues
1. Ensure the full HTML content is preserved
2. Check for truncation in ConfigMap data
3. Verify browser compatibility (modern browsers recommended)

## Customization

### Template Modification
To customize the report template:

1. Edit `pkg/controller/scan/report.go`
2. Modify the HTML template in `renderReportTemplate()`
3. Update CSS styles as needed
4. Rebuild and redeploy the operator

### Additional Data
To include additional data in reports:

1. Extend the `ReportData` struct
2. Update the data collection logic in `generateReportHTML()`
3. Modify the template to display new fields

## Security Considerations

- Reports contain detailed compliance information
- ConfigMaps are stored in the cluster (consider encryption at rest)
- Access control via Kubernetes RBAC
- Reports include evidence and check details (may contain sensitive paths)

## Integration

### CI/CD Pipelines
```bash
# Run scan and wait for completion
kubectl apply -f scan.yaml
kubectl wait --for=condition=Complete scan/<scan-name> --timeout=30m

# Export report for archival
kubectl get configmap <scan-name>-report -o jsonpath='{.data.report\.html}' > compliance-report-$(date +%Y%m%d).html
```

### Monitoring and Alerting
- Monitor scan completion status
- Alert on failed compliance checks
- Archive reports for audit trails
- Integrate with compliance dashboards

## Best Practices

1. **Regular Scans**: Schedule periodic compliance scans
2. **Report Archival**: Export and archive reports for compliance records
3. **Access Control**: Limit access to compliance reports
4. **Review Process**: Establish processes for reviewing failed checks
5. **Remediation**: Track and remediate compliance failures
6. **Documentation**: Maintain documentation of compliance exceptions