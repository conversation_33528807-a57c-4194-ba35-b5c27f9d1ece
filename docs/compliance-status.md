# Compliance Check Result Status

## Overview

The Compliance Operator uses standardized status values for check results, following the OpenShift Compliance Operator conventions.

## Status Values

### PASS
- **Meaning**: The compliance check passed successfully
- **Usage**: The system or configuration meets the compliance requirement
- **Example**: A required security setting is properly configured

### FAIL
- **Meaning**: The compliance check failed (non-compliant)
- **Usage**: The system or configuration does not meet the compliance requirement
- **Example**: A security setting is missing or misconfigured
- **Note**: This indicates a compliance violation, not a system error

### MANUAL
- **Meaning**: Manual review is required
- **Usage**: The check cannot be automated and requires human verification
- **Example**: Physical security controls or documentation reviews

### ERROR
- **Meaning**: An error occurred during the check execution
- **Usage**: System errors that prevented the check from running properly
- **Example**: 
  - Job execution failed
  - Script execution error
  - Unable to access required resources
- **Note**: This indicates a system problem, not a compliance failure

### INCONSISTENT
- **Meaning**: Check results are inconsistent across multiple runs or nodes
- **Usage**: When the same check produces different results in similar environments
- **Example**: Configuration drift between cluster nodes

### NOT-APPLICABLE
- **Meaning**: The check is not applicable to the current environment
- **Usage**: When a check doesn't apply to the current system configuration
- **Example**: 
  - Windows-specific checks on Linux nodes
  - Feature-specific checks when the feature is not enabled

## Status Usage Guidelines

1. **Use FAIL for compliance violations**: When a security requirement is not met
2. **Use ERROR for system issues**: When the check process itself fails
3. **Use MANUAL for human-required checks**: When automation is not possible
4. **Use NOT-APPLICABLE for irrelevant checks**: When checks don't apply to the environment

## Comparison with OpenShift Compliance Operator

Our status values align with the OpenShift Compliance Operator standards:
- ✅ PASS - Same meaning
- ✅ FAIL - Same meaning  
- ✅ MANUAL - Same meaning
- ✅ ERROR - Same meaning
- ✅ INCONSISTENT - Same meaning
- ✅ NOT-APPLICABLE - Same meaning

This ensures compatibility and familiar user experience for those migrating from or using both systems. 