# ComplianceSuite 使用指南

## 概述

ComplianceSuite 是 compliance-operator 的核心功能，它允许您定义、管理和调度多个合规扫描作为一个统一的套件。这提供了更高层次的抽象，使得大规模合规管理变得更加简单和高效。

## 核心特性

### 1. 多扫描管理
- 在单个 ComplianceSuite 中定义多个扫描
- 支持平台扫描、节点扫描和混合扫描
- 统一的状态监控和结果聚合

### 2. 定时调度
- 支持 cron 格式的调度表达式
- Suite 级别和单个扫描级别的调度
- 自动化的合规检查周期

### 3. 结果聚合
- 自动聚合所有扫描的结果
- 提供统一的合规状态视图
- 支持详细的统计信息

### 4. 灵活配置
- 节点选择器支持
- 扫描类型配置
- Profile 绑定

## 基本用法

### 创建简单的 ComplianceSuite

```yaml
apiVersion: compliance-operator.alauda.io/v1alpha1
kind: ComplianceSuite
metadata:
  name: basic-security-suite
  namespace: compliance-operator
spec:
  scans:
    - name: platform-check
      profile: security-baseline
      scanType: platform
    - name: node-check
      profile: security-baseline
      scanType: node
```

### 定时扫描配置

```yaml
apiVersion: compliance-operator.alauda.io/v1alpha1
kind: ComplianceSuite
metadata:
  name: daily-compliance-check
  namespace: compliance-operator
spec:
  # 每天凌晨2点执行
  schedule: "0 2 * * *"
  scans:
    - name: daily-platform
      profile: daily-security
      scanType: platform
    - name: daily-nodes
      profile: daily-security
      scanType: node
```

### 高级配置示例

```yaml
apiVersion: compliance-operator.alauda.io/v1alpha1
kind: ComplianceSuite
metadata:
  name: enterprise-compliance-suite
  namespace: compliance-operator
spec:
  schedule: "0 3 * * 1"  # 每周一凌晨3点
  autoApplyRemediations: false
  scans:
    # 主节点安全扫描
    - name: master-security
      profile: master-security-profile
      scanType: node
      nodeSelector:
        node-role.kubernetes.io/master: ""
    
    # 工作节点安全扫描
    - name: worker-security
      profile: worker-security-profile
      scanType: node
      nodeSelector:
        node-role.kubernetes.io/worker: ""
    
    # 平台级安全扫描
    - name: platform-security
      profile: platform-security-profile
      scanType: platform
    
    # 特殊调度的扫描
    - name: weekly-deep-scan
      profile: comprehensive-security
      scanType: both
      schedule: "0 6 * * 0"  # 每周日早上6点
```

## 状态监控

### 查看 Suite 状态

```bash
# 查看所有 ComplianceSuite
kubectl get compliancesuites -n compliance-operator

# 查看详细状态
kubectl describe compliancesuite enterprise-compliance-suite -n compliance-operator

# 查看状态 JSON
kubectl get compliancesuite enterprise-compliance-suite -n compliance-operator -o json | jq '.status'
```

### 状态字段说明

- **Phase**: Suite 的当前阶段
  - `LAUNCHING`: 初始化中
  - `RUNNING`: 扫描进行中
  - `DONE`: 扫描完成
  - `ERROR`: 出现错误

- **Result**: 总体合规结果
  - `COMPLIANT`: 完全合规
  - `NON-COMPLIANT`: 存在合规问题
  - `ERROR`: 扫描过程中出现错误
  - `MANUAL`: 需要手动审查

- **Summary**: 结果统计
  - `total`: 总检查项数
  - `passed`: 通过的检查项
  - `failed`: 失败的检查项
  - `error`: 错误的检查项
  - `manual`: 需要手动审查的检查项

## 查询和分析

### 查看相关的扫描

```bash
# 查看 Suite 创建的扫描
kubectl get scans -l compliance-operator.alauda.io/suite=enterprise-compliance-suite -n compliance-operator

# 查看扫描详情
kubectl describe scan enterprise-compliance-suite-master-security -n compliance-operator
```

### 查看检查结果

```bash
# 查看 Suite 的所有检查结果
kubectl get checkresults -l compliance-operator.alauda.io/suite=enterprise-compliance-suite -n compliance-operator

# 查看失败的检查结果
kubectl get checkresults -l compliance-operator.alauda.io/suite=enterprise-compliance-suite -n compliance-operator --field-selector spec.status=FAIL

# 统计结果
kubectl get checkresults -l compliance-operator.alauda.io/suite=enterprise-compliance-suite -n compliance-operator -o json | \
  jq '.items | group_by(.spec.status) | map({status: .[0].spec.status, count: length})'
```

## 最佳实践

### 1. 命名约定
- Suite 名称应该反映其用途和范围
- 扫描名称应该简洁明了
- 使用一致的命名模式

### 2. 调度策略
- 避免在业务高峰期运行扫描
- 考虑扫描的资源消耗
- 为不同类型的扫描设置不同的调度

### 3. 节点选择
- 合理使用节点选择器
- 考虑节点的角色和功能
- 避免对关键节点造成过大负载

### 4. 监控和维护
- 定期检查 Suite 的执行状态
- 监控资源使用情况
- 及时处理失败的扫描

## 故障排除

### 常见问题

1. **Suite 一直处于 LAUNCHING 状态**
   - 检查 Profile 是否存在
   - 验证节点选择器是否正确
   - 查看控制器日志

2. **扫描失败**
   - 检查节点资源是否充足
   - 验证 RBAC 权限
   - 查看扫描 Pod 的日志

3. **调度不生效**
   - 验证 cron 表达式格式
   - 检查控制器是否正常运行
   - 查看控制器日志

### 调试命令

```bash
# 查看控制器日志
kubectl logs -n compliance-operator deployment/compliance-operator -f

# 查看 Suite 相关的 Events
kubectl get events -n compliance-operator --field-selector involvedObject.name=enterprise-compliance-suite

# 查看扫描 Pod 日志
kubectl logs -n compliance-operator -l compliance-operator.alauda.io/suite=enterprise-compliance-suite
```

## 集成示例

### 与监控系统集成

```yaml
# 添加监控标签
apiVersion: compliance-operator.alauda.io/v1alpha1
kind: ComplianceSuite
metadata:
  name: monitored-compliance-suite
  namespace: compliance-operator
  labels:
    monitoring: "enabled"
    team: "security"
spec:
  scans:
    - name: monitored-scan
      profile: security-baseline
      scanType: platform
```

### CI/CD 集成

```bash
#!/bin/bash
# 在 CI/CD 流水线中检查合规状态

SUITE_NAME="ci-compliance-suite"
NAMESPACE="compliance-operator"

# 创建一次性合规检查
kubectl apply -f - <<EOF
apiVersion: compliance-operator.alauda.io/v1alpha1
kind: ComplianceSuite
metadata:
  name: ${SUITE_NAME}
  namespace: ${NAMESPACE}
spec:
  scans:
    - name: ci-platform-check
      profile: ci-security-baseline
      scanType: platform
EOF

# 等待扫描完成
kubectl wait --for=condition=Ready compliancesuite/${SUITE_NAME} -n ${NAMESPACE} --timeout=600s

# 检查结果
RESULT=$(kubectl get compliancesuite ${SUITE_NAME} -n ${NAMESPACE} -o jsonpath='{.status.result}')

if [ "$RESULT" != "COMPLIANT" ]; then
  echo "Compliance check failed: $RESULT"
  exit 1
fi

echo "Compliance check passed"
```

这个架构为您提供了一个完整的、生产就绪的 ComplianceSuite 实现，支持定时调度、结果聚合、状态管理等核心功能。 