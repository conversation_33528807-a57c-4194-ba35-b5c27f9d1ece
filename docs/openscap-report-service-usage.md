# OpenSCAP报告服务使用指南

## 🎯 概述

OpenSCAP报告服务是一个**稳定的、持久化的Web服务**，专门用于收集、存储和展示OpenSCAP扫描报告。它与compliance-operator一同部署，提供了一个集中化的报告管理平台。

## 🏗️ 架构特点

### 核心功能
- **报告收集**: 自动接收各节点OpenSCAP scanner上传的HTML报告  
- **按扫描分组**: 使用scanID对报告进行智能分组管理
- **持久化存储**: 使用PVC确保报告数据不丢失
- **美观Web界面**: 提供现代化的报告浏览和查看体验
- **健康检查**: 内置健康检查确保服务稳定性

### 技术架构
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│  OpenSCAP       │    │  OpenSCAP       │    │  OpenSCAP       │
│  Scanner Pod    │    │  Scanner Pod    │    │  Scanner Pod    │
│  (Node 1)       │    │  (Node 2)       │    │  (Node 3)       │
└─────────┬───────┘    └─────────┬───────┘    └─────────┬───────┘
          │                      │                      │
          │ HTTP POST /upload    │                      │
          └──────────────────────┼──────────────────────┘
                                 │
                    ┌─────────────▼─────────────┐
                    │   OpenSCAP Report Service │
                    │   (Deployment + Service)  │
                    │                           │
                    │ - 接收HTML报告上传        │
                    │ - 按scanID组织存储        │
                    │ - 提供Web查看界面         │
                    │ - 持久化存储(PVC)         │
                    └─────────────┬─────────────┘
                                 │
                    ┌─────────────▼─────────────┐
                    │   用户Web访问界面         │
                    │   http://localhost:8080   │
                    └───────────────────────────┘
```

## 🚀 部署方式

### 方式1: 使用Helm Chart (推荐)

```bash
# 启用报告服务
helm upgrade --install compliance-operator ./charts/compliance-operator \
  --set reportService.enabled=true \
  --set reportService.storageSize=20Gi \
  --namespace compliance-system \
  --create-namespace
```

### 方式2: 直接部署YAML

```bash
# 部署报告服务
kubectl apply -f examples/openscap-report-service-example.yaml

# 检查部署状态
kubectl get pods -n compliance-system -l app=openscap-report-service
```

## 🔧 构建和推送镜像

```bash
# 构建镜像
./scripts/build-openscap-report-service.sh

# 或指定特定标签
./scripts/build-openscap-report-service.sh -t v1.0.0
```

## 📊 使用方法

### 1. 访问Web界面

```bash
# 端口转发到本地
kubectl port-forward -n compliance-system svc/openscap-report-service 8080:8080

# 在浏览器中访问
open http://localhost:8080
```

### 2. Web界面功能

#### 首页Dashboard
- **扫描统计**: 显示总扫描数、报告数、节点数
- **扫描列表**: 按最新上传时间排序的扫描卡片
- **扫描信息**: 每个扫描显示Profile、节点数量、最新上传时间

#### 扫描详情页面
- **节点报告**: 显示该扫描下所有节点的报告
- **报告元数据**: 上传时间、Scanner类型、Profile信息
- **直接查看**: 点击可直接在浏览器中查看HTML报告

### 3. API接口

#### 上传报告 (Scanner自动调用)
```bash
curl -X POST http://openscap-report-service.compliance-system.svc.cluster.local:8080/upload \
  -F "scanId=scan-12345" \
  -F "scanName=ubuntu-stig-scan" \
  -F "nodeName=worker-node-1" \
  -F "profile=xccdf_org.ssgproject.content_profile_stig" \
  -F "scanner=openscap" \
  -F "report=@/path/to/report.html"
```

#### 健康检查
```bash
curl http://openscap-report-service.compliance-system.svc.cluster.local:8080/health
```

#### 获取扫描列表 (JSON API)
```bash
curl http://openscap-report-service.compliance-system.svc.cluster.local:8080/api/scans
```

## ⚙️ 配置选项

### Helm Chart配置

```yaml
# values.yaml
reportService:
  enabled: true
  storageSize: "20Gi"
  storageClass: "fast-ssd"
  
  resources:
    requests:
      memory: "256Mi"
      cpu: "200m"
    limits:
      memory: "1Gi"
      cpu: "1000m"
  
  ingress:
    enabled: true
    hosts:
      - host: openscap-reports.example.com
        paths:
          - path: /
            pathType: Prefix
```

### 环境变量

| 变量名 | 默认值 | 说明 |
|--------|--------|------|
| `REPORTS_DIR` | `/reports` | 报告存储目录 |
| `PORT` | `8080` | 服务端口 |
| `TZ` | `Asia/Shanghai` | 时区设置 |

## 🔍 监控和故障排除

### 检查服务状态
```bash
# 检查Pod状态
kubectl get pods -n compliance-system -l app=openscap-report-service

# 查看日志
kubectl logs -n compliance-system -l app=openscap-report-service -f

# 检查存储
kubectl get pvc -n compliance-system openscap-reports-pvc
```

### 健康检查
```bash
# 直接健康检查
kubectl exec -n compliance-system deployment/openscap-report-service -- \
  wget -qO- http://localhost:8080/health
```

### 常见问题

#### 1. 报告上传失败
**症状**: Scanner日志显示上传失败
**解决**: 
- 检查服务是否正常运行
- 确认网络连接
- 查看报告服务日志

#### 2. 存储空间不足
**症状**: 上传失败，存储满了
**解决**:
```bash
# 扩展PVC大小
kubectl patch pvc openscap-reports-pvc -n compliance-system -p '{"spec":{"resources":{"requests":{"storage":"50Gi"}}}}'
```

#### 3. Web界面无法访问
**症状**: 端口转发后无法访问
**解决**:
- 检查Pod是否Ready
- 确认端口转发命令正确
- 检查防火墙设置

## 📈 性能优化

### 存储优化
- 使用SSD存储类提高I/O性能
- 根据扫描频率调整存储大小
- 定期清理旧报告

### 资源配置
```yaml
resources:
  requests:
    memory: "512Mi"    # 根据报告数量调整
    cpu: "500m"        # 根据并发需求调整
  limits:
    memory: "2Gi"
    cpu: "2000m"
```

## 🔐 安全考虑

### 访问控制
- 服务默认只在集群内访问
- 使用Ingress时配置适当的认证
- 考虑使用NetworkPolicy限制网络访问

### 数据保护
- 报告数据存储在PVC中，确保备份
- 敏感信息不会在日志中泄露
- 使用非特权用户运行服务

## 🎉 总结

OpenSCAP报告服务提供了一个**稳定、可靠、易用**的报告管理平台，完美解决了以下问题：

✅ **报告过大无法存储在ConfigMap**  
✅ **多节点报告分散管理困难**  
✅ **报告查看体验差**  
✅ **数据持久化需求**  
✅ **集中化管理需求**  

通过与compliance-operator的深度集成，实现了OpenSCAP扫描报告的**自动收集、智能分组、持久存储和美观展示**，大大提升了合规扫描的管理效率和用户体验。 