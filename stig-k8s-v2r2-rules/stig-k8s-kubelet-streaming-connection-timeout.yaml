apiVersion: compliance-operator.alauda.io/v1alpha1
kind: Rule
metadata:
  name: stig-k8s-kubelet-streaming-connection-timeout
  namespace: compliance-system
spec:
  id: "V-242435"
  title: "The Kubernetes kubelet must enable streaming connection timeout."
  description: |
    Streaming connections that are not properly configured can consume system resources and can be used to cause a denial of service attack. Streaming connections must be terminated after a period of inactivity.
    
    The kubelet's streaming connection idle timeout ensures that streaming connections (such as kubectl exec, kubectl logs, kubectl port-forward) are automatically closed after a period of inactivity. This prevents resource exhaustion and potential denial of service attacks.
    
    Setting streamingConnectionIdleTimeout to 0 disables the timeout, which is not recommended for production environments.
  checkText: |
    On the Control Plane and Worker Nodes, run the command:
    ps -ef | grep kubelet
    
    Check the config file(s) referenced by the --config argument (example: --config=/etc/kubernetes/kubelet/kubelet-config.yaml):
    
    Change to the directory identified by --config argument and run the command:
    grep -i streamingconnectionidletimeout *
    
    If the setting streamingConnectionIdleTimeout is not configured or is set to \"0\", this is a finding.
    
    If the setting streamingConnectionIdleTimeout is not configured, check if there is an entry for \"--streaming-connection-idle-timeout\" in the kubelet command.
    
    On the Control Plane and Worker Nodes, run the command:
    ps -ef | grep kubelet
    
    If \"--streaming-connection-idle-timeout\" is not configured or is set to \"0\", this is a finding.
  fixText: |
    On the Control Plane and Worker Nodes, run the command:
    ps -ef | grep kubelet
    
    Check the config file(s) referenced by the --config argument (example: --config=/etc/kubernetes/kubelet/kubelet-config.yaml):
    
    Edit the Kubernetes Kubelet config file:
    Set the value of streamingConnectionIdleTimeout to an appropriate timeout value (e.g., \"5m\" for 5 minutes).
    
    Reset Kubelet service using the following command:
    service kubelet restart
  severity: "medium"
  checkType: "node"
  checkScript: |
    #!/bin/bash
    # Check if streaming connection timeout is configured
    # STIG requirement: streaming connection timeout should be configured and not disabled
    
    TIMEOUT_CONFIGURED=false
    CONFIG_FOUND=false
    TIMEOUT_DISABLED=false
    
    echo "Checking kubelet streaming connection timeout configuration..."
    
    # Function to check kubelet config file for streaming timeout setting
    check_kubelet_streaming_config() {
      local config_file="$1"
      if [ -f "$config_file" ]; then
        CONFIG_FOUND=true
        echo "✓ Config file found: $config_file"
        
        if grep -q "streamingConnectionIdleTimeout:" "$config_file"; then
          local timeout_value=$(grep "streamingConnectionIdleTimeout:" "$config_file" | sed 's/.*streamingConnectionIdleTimeout: *\([^[:space:]]*\).*/\1/')
          echo "✓ Found streamingConnectionIdleTimeout: $timeout_value"
          
          # Check if timeout is disabled (0 or 0s)
          if [ "$timeout_value" = "0" ] || [ "$timeout_value" = "0s" ]; then
            echo "✗ Streaming connection timeout is disabled (set to $timeout_value)"
            TIMEOUT_DISABLED=true
            return 1
          else
            echo "✓ Streaming connection timeout is properly configured: $timeout_value"
            TIMEOUT_CONFIGURED=true
            return 0
          fi
        else
          echo "ℹ No explicit streamingConnectionIdleTimeout setting found in config file"
        fi
      fi
      return 0
    }
    
    # Function to check kubelet process for streaming timeout parameter
    check_kubelet_streaming_process() {
      if [ -d /host/proc ]; then
        for pid in $(ls /host/proc | grep '^[0-9]*$'); do
          if [ -f "/host/proc/$pid/cmdline" ] && grep -q kubelet "/host/proc/$pid/cmdline" 2>/dev/null; then
            echo "✓ Found kubelet process: PID $pid"
            
            # Check for streaming connection timeout parameter
            if tr '\0' ' ' < "/host/proc/$pid/cmdline" | grep -q -- "--streaming-connection-idle-timeout"; then
              local timeout_param=$(tr '\0' ' ' < "/host/proc/$pid/cmdline" | grep -o -- "--streaming-connection-idle-timeout=[^[:space:]]*" | cut -d= -f2)
              echo "✓ Found --streaming-connection-idle-timeout: $timeout_param"
              
              # Check if timeout is disabled
              if [ "$timeout_param" = "0" ] || [ "$timeout_param" = "0s" ]; then
                echo "✗ Streaming connection timeout is disabled via command line (set to $timeout_param)"
                TIMEOUT_DISABLED=true
                return 1
              else
                echo "✓ Streaming connection timeout is properly configured via command line: $timeout_param"
                TIMEOUT_CONFIGURED=true
                return 0
              fi
            else
              echo "ℹ No explicit --streaming-connection-idle-timeout parameter found (using default)"
            fi
          fi
        done
      fi
      return 0
    }
    
    # Check kubelet configuration files
    for config_path in "/host/var/lib/kubelet/config.yaml" "/host/etc/kubernetes/kubelet/kubelet-config.yaml" "/host/etc/kubernetes/kubelet.yaml"; do
      if ! check_kubelet_streaming_config "$config_path"; then
        echo "FAIL: Streaming connection timeout is disabled in configuration"
        exit 1
      fi
    done
    
    # Check kubelet process parameters
    if ! check_kubelet_streaming_process; then
      echo "FAIL: Streaming connection timeout is disabled via command line"
      exit 1
    fi
    
    # Final evaluation
    echo ""
    echo "=== EVALUATION ==="
    echo "Config found: $CONFIG_FOUND"
    echo "Timeout configured: $TIMEOUT_CONFIGURED"
    echo "Timeout disabled: $TIMEOUT_DISABLED"
    
    if [ "$TIMEOUT_DISABLED" = "true" ]; then
      echo "FAIL: Kubelet streaming connection timeout is disabled"
      exit 1
    fi
    
    if [ "$TIMEOUT_CONFIGURED" = "true" ]; then
      echo "PASS: Kubelet streaming connection timeout is properly configured"
      exit 0
    fi
    
    if [ "$CONFIG_FOUND" = "false" ]; then
      echo "FAIL: No kubelet configuration found to verify streaming connection timeout"
      exit 1
    fi
    
    # According to STIG requirements, timeout must be explicitly configured
    echo "FAIL: Streaming connection timeout is not explicitly configured"
    exit 1
