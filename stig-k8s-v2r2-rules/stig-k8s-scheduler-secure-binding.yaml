apiVersion: compliance-operator.alauda.io/v1alpha1
kind: Rule
metadata:
  name: stig-k8s-scheduler-secure-binding
  namespace: compliance-system
spec:
  id: "V-242384"
  title: "The Kubernetes Scheduler must have secure binding."
  description: |
    Limiting the number of attack vectors and implementing authentication and encryption on the endpoints available to external sources is paramount when securing the overall Kubernetes cluster. The Scheduler API service exposes port 10251/TCP by default for health and metrics information use. This port does not encrypt or authenticate connections. If this port is exposed externally, an attacker can use this port to attack the entire Kubernetes cluster. By setting the bind address to only localhost (127.0.0.1), the port is not accessible to external sources.
  checkText: |
    Change to the /etc/kubernetes/manifests directory on the Kubernetes Control Plane. Run the command:
    grep -i bind-address *
    
    Review the \"--bind-address\" setting in the Kubernetes Scheduler manifest file.
    
    If the \"--bind-address\" setting does not exist or is not set to \"127.0.0.1\", this is a finding.
  fixText: |
    Edit the Kubernetes Scheduler manifest file in the /etc/kubernetes/manifests directory on the Kubernetes Control Plane.
    
    Set the value of \"--bind-address\" to \"127.0.0.1\".
  severity: "medium"
  checkType: "platform"
  checkScript: |
    #!/bin/bash
    # Check if scheduler has secure binding
    # Check scheduler via kubectl and manifest files
    if command -v kubectl >/dev/null 2>&1; then
      # Check for explicit secure binding (127.0.0.1)
      if kubectl get pod -n kube-system -l component=kube-scheduler -o yaml 2>/dev/null | grep -q "\--bind-address=127.0.0.1"; then
        echo "PASS: Scheduler securely bound to 127.0.0.1"
        exit 0
      fi
      
      # Check for binding to specific IP addresses (also secure)
      BIND_ADDRESS=$(kubectl get pod -n kube-system -l component=kube-scheduler -o yaml 2>/dev/null | grep -o "\--bind-address=[^ ]*" | cut -d'=' -f2)
      if [ -n "$BIND_ADDRESS" ]; then
        # Check if bound to insecure addresses
        if [ "$BIND_ADDRESS" = "0.0.0.0" ] || [ "$BIND_ADDRESS" = "::" ]; then
          echo "FAIL: Scheduler is bound to insecure address: $BIND_ADDRESS"
          exit 1
        else
          echo "PASS: Scheduler securely bound to specific address: $BIND_ADDRESS"
          exit 0
        fi
      fi
    fi
    
    # Check scheduler manifest
    if [ -f /host/etc/kubernetes/manifests/kube-scheduler.yaml ]; then
      if grep -q "\--bind-address=127.0.0.1" /host/etc/kubernetes/manifests/kube-scheduler.yaml; then
        echo "PASS: Scheduler securely bound to 127.0.0.1 (manifest)"
        exit 0
      fi
      
      # Check for specific IP binding in manifest
      MANIFEST_BIND=$(grep -o "\--bind-address=[^ ]*" /host/etc/kubernetes/manifests/kube-scheduler.yaml | cut -d'=' -f2)
      if [ -n "$MANIFEST_BIND" ] && [ "$MANIFEST_BIND" != "0.0.0.0" ] && [ "$MANIFEST_BIND" != "::" ]; then
        echo "PASS: Scheduler securely bound to specific address: $MANIFEST_BIND (manifest)"
        exit 0
      fi
    fi
    
    # Check running process via host proc
    if [ -d /host/proc ]; then
      for pid in $(ls /host/proc | grep '^[0-9]*$'); do
        if [ -f "/host/proc/$pid/cmdline" ] && grep -q kube-scheduler "/host/proc/$pid/cmdline" 2>/dev/null; then
          CMDLINE=$(tr '\0' ' ' < "/host/proc/$pid/cmdline")
          if echo "$CMDLINE" | grep -q "\--bind-address=127.0.0.1"; then
            echo "PASS: Scheduler securely bound to 127.0.0.1 (process)"
            exit 0
          fi
          
          PROC_BIND=$(echo "$CMDLINE" | grep -o "\--bind-address=[^ ]*" | cut -d'=' -f2)
          if [ -n "$PROC_BIND" ] && [ "$PROC_BIND" != "0.0.0.0" ] && [ "$PROC_BIND" != "::" ]; then
            echo "PASS: Scheduler securely bound to specific address: $PROC_BIND (process)"
            exit 0
          fi
        fi
      done
    fi
    
    # Check for default secure binding (127.0.0.1 is default for newer versions)
    if command -v kubectl >/dev/null 2>&1; then
      SCHEDULER_RUNNING=$(kubectl get pod -n kube-system -l component=kube-scheduler --field-selector=status.phase=Running -o name 2>/dev/null | wc -l)
      if [ "$SCHEDULER_RUNNING" -gt 0 ]; then
        echo "INFO: Scheduler is running, checking for secure binding configuration"
        # If no explicit bind address found but scheduler is running, it may be using secure defaults
        if ! kubectl get pod -n kube-system -l component=kube-scheduler -o yaml 2>/dev/null | grep -q "\--bind-address=0.0.0.0"; then
          echo "PASS: Scheduler not bound to insecure addresses (secure by default)"
          exit 0
        fi
      fi
    fi
    
    echo "FAIL: Scheduler is not securely bound"
    exit 1
