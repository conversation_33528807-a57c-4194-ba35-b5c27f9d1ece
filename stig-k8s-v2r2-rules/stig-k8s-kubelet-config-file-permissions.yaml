apiVersion: compliance-operator.alauda.io/v1alpha1
kind: Rule
metadata:
  name: stig-k8s-kubelet-config-file-permissions
  namespace: compliance-system
spec:
  id: "V-242407"
  title: "The Kubernetes KubeletConfiguration files must have file permissions set to 644 or more restrictive."
  description: |
    The kubelet configuration file contains the runtime configuration of the kubelet service. If an attacker can gain access to this file, changes can be made to open vulnerabilities and bypass user authorizations inherit within Kubernetes with RBAC implemented.
  checkText: |
    On the Kubernetes Control Plane and Worker nodes, run the command:
    ps -ef | grep kubelet

    Check the config file (path identified by: --config):

    Change to the directory identified by --config (example /etc/sysconfig/) and run the command:
    ls -l kubelet

    Each KubeletConfiguration file must have permissions of "644" or more restrictive.

    If any KubeletConfiguration file is less restrictive than "644", this is a finding.
  fixText: |
    On the Kubernetes Control Plane and Worker nodes, run the command:
    ps -ef | grep kubelet

    Check the config file (path identified by: --config):

    Change to the directory identified by --config (example /etc/sysconfig/) and run the command:
    chmod 644 kubelet

    To verify the change took place, run the command:
    ls -l kubelet

    The kubelet file should now have the permissions of "644".
  severity: "medium"
  checkType: "node"
  checkScript: |
    #!/bin/bash
    # Check kubelet configuration file permissions
    # STIG requirement: kubelet config files should have 644 permissions or more restrictive
    
    echo "Checking kubelet configuration file permissions..."
    
    VIOLATIONS_FOUND=false
    CONFIG_FILES_CHECKED=0
    
    # Common kubelet config file locations
    KUBELET_CONFIG_PATHS=(
      "/host/var/lib/kubelet/config.yaml"
      "/host/etc/kubernetes/kubelet/kubelet-config.yaml"
      "/host/etc/kubernetes/kubelet.yaml"
      "/host/etc/sysconfig/kubelet"
      "/host/etc/default/kubelet"
    )
    
    # Check each potential kubelet config file
    for config_path in "${KUBELET_CONFIG_PATHS[@]}"; do
      if [ -f "$config_path" ]; then
        CONFIG_FILES_CHECKED=$((CONFIG_FILES_CHECKED + 1))
        filename=$(basename "$config_path")
        perms=$(stat -c "%a" "$config_path" 2>/dev/null)
        
        echo "✓ Found kubelet config file: $config_path"
        echo "  Permissions: $perms"
        
        # Check if permissions are more permissive than 644
        if [ "$perms" -gt 644 ]; then
          echo "  ✗ VIOLATION: Kubelet config file $filename has overly permissive permissions: $perms (should be 644 or more restrictive)"
          VIOLATIONS_FOUND=true
        else
          echo "  ✓ OK: Kubelet config file $filename has appropriate permissions: $perms"
        fi
      fi
    done
    
    # Try to find kubelet config from running process
    if [ -d "/host/proc" ]; then
      for pid in $(ls /host/proc | grep '^[0-9]*$'); do
        if [ -f "/host/proc/$pid/cmdline" ] && grep -q kubelet "/host/proc/$pid/cmdline" 2>/dev/null; then
          cmdline=$(tr '\0' ' ' < "/host/proc/$pid/cmdline")
          if echo "$cmdline" | grep -q -- "--config"; then
            config_file=$(echo "$cmdline" | sed 's/.*--config[= ]\([^ ]*\).*/\1/')
            if [ -f "/host$config_file" ]; then
              CONFIG_FILES_CHECKED=$((CONFIG_FILES_CHECKED + 1))
              perms=$(stat -c "%a" "/host$config_file" 2>/dev/null)
              echo "✓ Found kubelet config from process: /host$config_file"
              echo "  Permissions: $perms"
              
              if [ "$perms" -gt 644 ]; then
                echo "  ✗ VIOLATION: Kubelet config file has overly permissive permissions: $perms (should be 644 or more restrictive)"
                VIOLATIONS_FOUND=true
              else
                echo "  ✓ OK: Kubelet config file has appropriate permissions: $perms"
              fi
            fi
          fi
          break
        fi
      done
    fi
    
    if [ "$CONFIG_FILES_CHECKED" -eq 0 ]; then
      echo "WARNING: No kubelet configuration files found"
      exit 0
    fi
    
    if [ "$VIOLATIONS_FOUND" = true ]; then
      echo "FAIL: Found kubelet configuration files with overly permissive permissions"
      exit 1
    else
      echo "PASS: All kubelet configuration files have appropriate permissions (644 or more restrictive)"
      exit 0
    fi