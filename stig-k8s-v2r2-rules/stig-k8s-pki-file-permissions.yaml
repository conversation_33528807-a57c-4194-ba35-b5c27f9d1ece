apiVersion: compliance-operator.alauda.io/v1alpha1
kind: Rule
metadata:
  name: stig-k8s-pki-file-permissions
  namespace: compliance-system
spec:
  id: "V-242409"
  title: "The Kubernetes PKI keys must have file permissions set to 600 or more restrictive."
  description: |
    The Kubernetes PKI directory contains all certificate key files supporting secure network communications in the Kubernetes Control Plane. If these files can be modified, data traversing within the cluster communications could be compromised. Many of the security settings within the document are implemented through these files.
    
    The PKI certificate key files should be protected with the most restrictive file permissions possible. Setting the permissions to 600 ensures that only the owner (root) can read and write to these sensitive files.
  checkText: |
    Review the Kubernetes PKI key files by using the command:
    
    find /etc/kubernetes/pki/ -name '*.key' -exec stat -c permissions=%a %n {} \;
    
    If any key file permissions are not set to 600 or more restrictive, this is a finding.
  fixText: |
    Change the permissions of the PKI key files by executing the command:
    
    find /etc/kubernetes/pki/ -name '*.key' -exec chmod 600 {} \;
  severity: "medium"
  checkType: "node"
  checkScript: |
    #!/bin/bash
    # Check PKI file permissions
    PKI_DIR="/host/etc/kubernetes/pki"
    if [ -d "$PKI_DIR" ]; then
      # Find all .key files and check their permissions
      FAILED=0
      while IFS= read -r -d '' keyfile; do
        if [ -f "$keyfile" ]; then
          PERMS=$(stat -c "%a" "$keyfile" 2>/dev/null)
          if [ -n "$PERMS" ]; then
            # Check if permissions are more restrictive than 600 (owner read/write only)
            if [ "$PERMS" -gt 600 ]; then
              echo "FAIL: $keyfile has permissions $PERMS (should be 600 or more restrictive)"
              FAILED=1
            fi
          fi
        fi
      done < <(find "$PKI_DIR" -name "*.key" -print0 2>/dev/null)
      
      # Also check certificate files for proper permissions
      while IFS= read -r -d '' certfile; do
        if [ -f "$certfile" ]; then
          PERMS=$(stat -c "%a" "$certfile" 2>/dev/null)
          if [ -n "$PERMS" ]; then
            # Certificate files should be readable but not writable by others (644 or more restrictive)
            if [ "$PERMS" -gt 644 ]; then
              echo "FAIL: $certfile has permissions $PERMS (should be 644 or more restrictive)"
              FAILED=1
            fi
          fi
        fi
      done < <(find "$PKI_DIR" -name "*.crt" -o -name "*.pem" -print0 2>/dev/null)
      
      if [ "$FAILED" -eq 1 ]; then
        exit 1
      fi
    else
      echo "FAIL: PKI directory $PKI_DIR not found"
      exit 1
    fi
    echo "PASS: All PKI files have appropriate permissions"
    exit 0
