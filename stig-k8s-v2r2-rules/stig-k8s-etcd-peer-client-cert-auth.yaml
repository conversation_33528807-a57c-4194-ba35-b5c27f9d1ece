apiVersion: compliance-operator.alauda.io/v1alpha1
kind: Rule
metadata:
  name: stig-k8s-etcd-peer-client-cert-auth
  namespace: compliance-system
spec:
  id: "V-242426"
  title: "Kubernetes etcd must enable client authentication to secure service."
  description: |
    Kubernetes container and pod configuration are maintained by Kubelet. Kubelet agents register nodes with the API Server, mount volume storage, and perform health checks for containers and pods. Anyone who gains access to Kubelet agents can effectively control applications within the pods and containers. Using authenticity protection, the communication can be protected against man-in-the-middle attacks/session hijacking and the insertion of false information into sessions.

    The communication session is protected by utilizing transport encryption protocols, such as TLS. TLS provides the Kubernetes API Server with a means to be able to authenticate sessions and encrypt traffic.

    Etcd is a highly-available key value store used by Kubernetes deployments for persistent storage of all of its REST API objects. These objects are sensitive and should be accessible only by authenticated etcd peers in the etcd cluster. The parameter "--peer-client-cert-auth" must be set for etcd to check all incoming peer requests from the cluster for valid client certificates.
  checkText: |
    Change to the /etc/kubernetes/manifests directory on the Kubernetes Control Plane. Run the command:
    grep -i peer-client-cert-auth * 

    If the setting "--peer-client-cert-auth" is not configured in the Kubernetes etcd manifest file or set to "false", this is a finding.
  fixText: |
    Edit the Kubernetes etcd manifest file in the /etc/kubernetes/manifests directory on the Kubernetes Control Plane.

    Set the value of "--peer-client-cert-auth" to "true" for the etcd.
  checkType: "platform"
  severity: "medium"
  checkScript: |
    #!/bin/bash
    # Check if etcd peer client certificate authentication is enabled
    # STIG requirement: --peer-client-cert-auth should be set to true
    
    echo "Checking etcd peer client certificate authentication configuration..."
    
    peer_client_cert_auth_found=false
    
    # Method 1: Check via kubectl for etcd pods
    if command -v kubectl >/dev/null 2>&1; then
      if kubectl get pod -n kube-system -l component=etcd -o yaml 2>/dev/null | grep -q "peer-client-cert-auth.*true"; then
        echo "PASS: etcd peer-client-cert-auth is enabled (kubectl)"
        peer_client_cert_auth_found=true
      fi
    fi
    
    # Method 2: Check etcd manifest file
    if [ -f /host/etc/kubernetes/manifests/etcd.yaml ]; then
      if grep -q "peer-client-cert-auth.*true" /host/etc/kubernetes/manifests/etcd.yaml; then
        echo "PASS: etcd peer-client-cert-auth is enabled (manifest)"
        peer_client_cert_auth_found=true
      fi
    fi
    
    # Method 3: Check etcd process
    if pgrep etcd >/dev/null 2>&1; then
      if ps aux | grep etcd | grep -q "peer-client-cert-auth.*true"; then
        echo "PASS: etcd peer-client-cert-auth is enabled (process)"
        peer_client_cert_auth_found=true
      fi
    fi
    
    if [ "$peer_client_cert_auth_found" = true ]; then
      exit 0
    else
      echo "FAIL: etcd peer-client-cert-auth is not enabled or not configured"
      exit 1
    fi