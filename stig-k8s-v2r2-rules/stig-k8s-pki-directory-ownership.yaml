apiVersion: compliance-operator.alauda.io/v1alpha1
kind: Rule
metadata:
  name: stig-k8s-pki-directory-ownership
  namespace: compliance-system
spec:
  id: "V-242416"
  title: "The Kubernetes PKI directory and file(s) must be owned by root."
  description: |
    The Kubernetes PKI directory contains all the public/private key pairs and certificates used by Kubernetes components. If an attacker can gain access to these files, changes can be made to open vulnerabilities and bypass user authorizations inherent within Kubernetes with RBAC implemented.
  checkText: |
    On the Control Plane, run the command:
    stat -c %U:%G /etc/kubernetes/pki/*

    If any file is not owned by root:root, this is a finding.
  fixText: |
    On the Control Plane, run the command:
    chown -R root:root /etc/kubernetes/pki/
  severity: "high"
  checkType: "node"
  checkScript: |
    #!/bin/bash
    # Check PKI directory and files ownership
    # STIG requirement: PKI directory and all files should be owned by root:root
    
    echo "Checking PKI directory and files ownership..."
    
    PKI_PATHS=(
      "/host/etc/kubernetes/pki"
      "/host/var/lib/kubernetes/pki"
    )
    
    VIOLATIONS_FOUND=false
    FILES_CHECKED=0
    
    for pki_path in "${PKI_PATHS[@]}"; do
      if [ -d "$pki_path" ]; then
        echo "✓ Found PKI directory: $pki_path"
        
        # Check directory ownership
        dir_owner=$(stat -c "%U:%G" "$pki_path" 2>/dev/null)
        echo "  Directory owner: $dir_owner"
        
        if [ "$dir_owner" != "root:root" ]; then
          echo "  ✗ VIOLATION: PKI directory is not owned by root:root (current: $dir_owner)"
          VIOLATIONS_FOUND=true
        else
          echo "  ✓ OK: PKI directory is properly owned by root:root"
        fi
        
        # Check all files in PKI directory
        find "$pki_path" -type f | while read -r pki_file; do
          FILES_CHECKED=$((FILES_CHECKED + 1))
          filename=$(basename "$pki_file")
          owner=$(stat -c "%U:%G" "$pki_file" 2>/dev/null)
          
          echo "  ✓ Checking PKI file: $filename"
          echo "    Owner: $owner"
          
          if [ "$owner" != "root:root" ]; then
            echo "    ✗ VIOLATION: PKI file $filename is not owned by root:root (current: $owner)"
            VIOLATIONS_FOUND=true
          else
            echo "    ✓ OK: PKI file $filename is properly owned by root:root"
          fi
        done
        
        # Check subdirectories
        find "$pki_path" -type d | while read -r pki_subdir; do
          if [ "$pki_subdir" != "$pki_path" ]; then
            subdir_name=$(basename "$pki_subdir")
            subdir_owner=$(stat -c "%U:%G" "$pki_subdir" 2>/dev/null)
            
            echo "  ✓ Checking PKI subdirectory: $subdir_name"
            echo "    Owner: $subdir_owner"
            
            if [ "$subdir_owner" != "root:root" ]; then
              echo "    ✗ VIOLATION: PKI subdirectory $subdir_name is not owned by root:root (current: $subdir_owner)"
              VIOLATIONS_FOUND=true
            else
              echo "    ✓ OK: PKI subdirectory $subdir_name is properly owned by root:root"
            fi
          fi
        done
      fi
    done
    
    # Check for additional PKI files in common locations
    ADDITIONAL_PKI_FILES=(
      "/host/etc/ssl/certs/kubernetes"
      "/host/etc/kubernetes/ssl"
    )
    
    for additional_path in "${ADDITIONAL_PKI_FILES[@]}"; do
      if [ -d "$additional_path" ]; then
        echo "✓ Found additional PKI directory: $additional_path"
        
        find "$additional_path" -type f | while read -r pki_file; do
          FILES_CHECKED=$((FILES_CHECKED + 1))
          filename=$(basename "$pki_file")
          owner=$(stat -c "%U:%G" "$pki_file" 2>/dev/null)
          
          echo "  ✓ Checking additional PKI file: $filename"
          echo "    Owner: $owner"
          
          if [ "$owner" != "root:root" ]; then
            echo "    ✗ VIOLATION: Additional PKI file $filename is not owned by root:root (current: $owner)"
            VIOLATIONS_FOUND=true
          fi
        done
      fi
    done
    
    if [ "$FILES_CHECKED" -eq 0 ]; then
      echo "WARNING: No PKI files found"
      exit 0
    fi
    
    if [ "$VIOLATIONS_FOUND" = true ]; then
      echo "FAIL: Found PKI directory or files not owned by root:root"
      exit 1
    else
      echo "PASS: All PKI directories and files are properly owned by root:root"
      exit 0
    fi