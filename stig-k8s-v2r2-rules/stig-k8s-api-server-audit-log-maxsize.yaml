apiVersion: compliance-operator.alauda.io/v1alpha1
kind: Rule
metadata:
  name: stig-k8s-api-server-audit-log-maxsize
  namespace: compliance-system
spec:
  id: "V-242464"
  title: "Kubernetes API Server audit log maximum size must be set."
  description: |
    Kubernetes API Server validates and configures pods and services for the API object. The REST operation provides frontend functionality to the cluster share state. Audit logs capture individual user, administrator, and system accesses to the API Server. The audit log maximum size setting determines the maximum size of individual audit log files before they are rotated. The maximum size must be configured to prevent the loss of audit data.
  checkText: |
    Change to the /etc/kubernetes/manifests directory on the Kubernetes Control Plane. Run the command:
    grep -i audit-log-maxsize *

    If the setting audit-log-maxsize is not set or is set to less than 100, this is a finding.
  fixText: |
    Edit the Kubernetes API Server manifest file in the /etc/kubernetes/manifests directory on the Kubernetes Control Plane. Set the argument "--audit-log-maxsize" to "100" or more.
  severity: "medium"
  checkType: "platform"
  checkScript: |
    #!/bin/bash
    # Check API Server audit log maximum size configuration
    # STIG requirement: --audit-log-maxsize must be set to 100 or more
    
    echo "Checking API Server audit log maximum size configuration..."
    
    AUDIT_MAXSIZE_CONFIGURED=false
    AUDIT_MAXSIZE_VALUE=""
    
    # Method 1: Check via kubectl for API server pods
    if command -v kubectl >/dev/null 2>&1; then
      if kubectl get pod -n kube-system -l component=kube-apiserver -o yaml 2>/dev/null | grep -q "audit-log-maxsize"; then
        maxsize_value=$(kubectl get pod -n kube-system -l component=kube-apiserver -o yaml 2>/dev/null | grep "audit-log-maxsize" | head -1 | sed 's/.*--audit-log-maxsize[= ]\([^ ]*\).*/\1/')
        if [ -n "$maxsize_value" ]; then
          echo "✓ Found audit-log-maxsize in API server: $maxsize_value (kubectl)"
          AUDIT_MAXSIZE_CONFIGURED=true
          AUDIT_MAXSIZE_VALUE="$maxsize_value"
        fi
      fi
    fi
    
    # Method 2: Check API server manifest file
    if [ -f "/host/etc/kubernetes/manifests/kube-apiserver.yaml" ]; then
      echo "✓ Checking API server manifest: /host/etc/kubernetes/manifests/kube-apiserver.yaml"
      
      if grep -q "audit-log-maxsize" "/host/etc/kubernetes/manifests/kube-apiserver.yaml"; then
        maxsize_value=$(grep "audit-log-maxsize" "/host/etc/kubernetes/manifests/kube-apiserver.yaml" | head -1 | sed 's/.*--audit-log-maxsize[= ]\([^ ]*\).*/\1/')
        if [ -n "$maxsize_value" ]; then
          echo "  ✓ Found audit-log-maxsize in manifest: $maxsize_value"
          AUDIT_MAXSIZE_CONFIGURED=true
          AUDIT_MAXSIZE_VALUE="$maxsize_value"
        fi
      else
        echo "  ✗ No audit-log-maxsize found in manifest"
      fi
    fi
    
    # Method 3: Check running API server process
    if [ -d "/host/proc" ]; then
      for pid in $(ls /host/proc | grep '^[0-9]*$'); do
        if [ -f "/host/proc/$pid/cmdline" ] && grep -q kube-apiserver "/host/proc/$pid/cmdline" 2>/dev/null; then
          cmdline=$(tr '\0' ' ' < "/host/proc/$pid/cmdline")
          if echo "$cmdline" | grep -q "audit-log-maxsize"; then
            maxsize_value=$(echo "$cmdline" | sed 's/.*--audit-log-maxsize[= ]\([^ ]*\).*/\1/')
            echo "✓ Found audit-log-maxsize in API server process: $maxsize_value"
            AUDIT_MAXSIZE_CONFIGURED=true
            AUDIT_MAXSIZE_VALUE="$maxsize_value"
          fi
          break
        fi
      done
    fi
    
    if [ "$AUDIT_MAXSIZE_CONFIGURED" = false ]; then
      echo "FAIL: API Server audit-log-maxsize is not configured"
      exit 1
    fi
    
    # Check if the value is numeric and >= 100
    if ! [[ "$AUDIT_MAXSIZE_VALUE" =~ ^[0-9]+$ ]]; then
      echo "FAIL: audit-log-maxsize value is not numeric: $AUDIT_MAXSIZE_VALUE"
      exit 1
    fi
    
    if [ "$AUDIT_MAXSIZE_VALUE" -lt 100 ]; then
      echo "FAIL: audit-log-maxsize is set to $AUDIT_MAXSIZE_VALUE (must be 100 or more)"
      exit 1
    fi
    
    echo "PASS: API Server audit-log-maxsize is properly configured: $AUDIT_MAXSIZE_VALUE MB"
    exit 0