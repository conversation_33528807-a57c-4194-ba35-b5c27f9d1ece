apiVersion: compliance-operator.alauda.io/v1alpha1
kind: Rule
metadata:
  name: stig-k8s-secrets-not-environment-variables
  namespace: compliance-system
spec:
  id: "V-242415"
  title: "Secrets in Kubernetes must not be stored as environment variables."
  description: |
    Secrets are stored in the etcd datastore as plaintext. Additionally, anyone who is authorized to create a pod in a namespace can use that access to read any secret in that namespace; this includes indirect access such as the ability to create a deployment. Secrets should be mounted as data volumes instead of environment variables.
    
    When secrets are stored as environment variables, they are visible in the process list and can be accessed through various means including the Kubernetes API, container inspection tools, and process dumps. This increases the risk of credential exposure.
    
    Instead of using environment variables, secrets should be mounted as volumes or accessed through the Kubernetes API with proper authentication and authorization.
  checkText: |
    To check for secrets stored as environment variables, run the command:
    kubectl get all -o jsonpath='{range .items[*]}{\"\n\"}{.kind}{\"\t\"}{.metadata.name}{\"\t\"}{range .spec.containers[*]}{.env[*].valueFrom.secretKeyRef}{end}{end}' --all-namespaces
    
    Review the output for any entries that show secrets being used as environment variables through secretKeyRef.
    
    If any secrets are found to be stored as environment variables, this is a finding.
  fixText: |
    Remove the environment variables that are using secretKeyRef and instead mount the secrets as volumes or use other secure methods to access the secrets.
    
    Example of mounting a secret as a volume:
    volumes:
    - name: secret-volume
      secret:
        secretName: mysecret
    
    Then mount it in the container:
    volumeMounts:
    - name: secret-volume
      mountPath: \"/etc/secret-volume\"
      readOnly: true
  severity: "high"
  checkType: "platform"
  checkScript: |
    #!/bin/bash
    # Check if secrets are used as environment variables
    # STIG requirement: Secrets must not be stored as environment variables
    
    echo "Checking for secrets used as environment variables..."
    
    if ! command -v kubectl >/dev/null 2>&1; then
      echo "SKIP: kubectl not available for secrets check"
      exit 0
    fi
    
    # Flag to track if secrets as env vars were found
    SECRETS_AS_ENV_FOUND=false
    
    # Check all pods for secretKeyRef usage
    if kubectl get pods --all-namespaces -o yaml 2>/dev/null | grep -q "secretKeyRef"; then
      echo "FAIL: Found pods using secrets as environment variables"
      
      # Show which pods are using secrets as env vars
      kubectl get pods --all-namespaces -o yaml 2>/dev/null | grep -B10 -A5 "secretKeyRef" | grep -E "(namespace:|name:)" | head -20
      SECRETS_AS_ENV_FOUND=true
    fi
    
    # Check deployments for secretKeyRef usage
    if kubectl get deployments --all-namespaces -o yaml 2>/dev/null | grep -q "secretKeyRef"; then
      echo "FAIL: Found deployments using secrets as environment variables"
      SECRETS_AS_ENV_FOUND=true
    fi
    
    # Check statefulsets for secretKeyRef usage
    if kubectl get statefulsets --all-namespaces -o yaml 2>/dev/null | grep -q "secretKeyRef"; then
      echo "FAIL: Found statefulsets using secrets as environment variables"
      SECRETS_AS_ENV_FOUND=true
    fi
    
    # Check daemonsets for secretKeyRef usage
    if kubectl get daemonsets --all-namespaces -o yaml 2>/dev/null | grep -q "secretKeyRef"; then
      echo "FAIL: Found daemonsets using secrets as environment variables"
      SECRETS_AS_ENV_FOUND=true
    fi
    
    # If secrets as env vars were found, this is a finding
    if [ "$SECRETS_AS_ENV_FOUND" = "true" ]; then
      exit 1
    fi
    
    echo "PASS: No secrets found being used as environment variables"
    exit 0
