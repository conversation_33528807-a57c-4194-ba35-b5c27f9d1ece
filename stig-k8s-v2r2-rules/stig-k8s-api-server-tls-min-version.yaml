apiVersion: compliance-operator.alauda.io/v1alpha1
kind: Rule
metadata:
  name: stig-k8s-api-server-tls-min-version
  namespace: compliance-system
spec:
  id: "V-242378"
  title: "The Kubernetes API Server must use TLS 1.2, at a minimum, to protect the confidentiality of sensitive data during electronic dissemination."
  description: |
    The Kubernetes API Server will prohibit the use of SSL and unauthorized versions of TLS protocols to properly secure communication.

    The use of unsupported protocol exposes vulnerabilities to the Kubernetes by rogue traffic interceptions, man-in-the-middle attacks, and impersonation of users or services from the container platform runtime, registry, and keystore. To enable the minimum version of TLS to be used by the Kubernetes API Server, the setting "tls-min-version" must be set.
  checkText: |
    Change to the /etc/kubernetes/manifests directory on the Kubernetes Control Plane. Run the command:
    grep -i tls-min-version *

    If the setting "tls-min-version" is not configured in the Kubernetes API Server manifest file or it is set to "VersionTLS10" or "VersionTLS11", this is a finding.
  fixText: |
    Edit the Kubernetes API Server manifest file in the /etc/kubernetes/manifests directory on the Kubernetes Control Plane. Set the value of "--tls-min-version" to "VersionTLS12" or higher.
  severity: "medium"
  checkType: "platform"
  checkScript: |
    #!/bin/bash
    # Check if minimum TLS version is set to 1.2
    # Check API server via kubectl and manifest files
    if command -v kubectl >/dev/null 2>&1; then
      if kubectl get pod -n kube-system -l component=kube-apiserver -o yaml 2>/dev/null | grep -q "\--tls-min-version=VersionTLS12"; then
        exit 0
      fi
    fi
    # Check API server manifest
    if [ -f /host/etc/kubernetes/manifests/kube-apiserver.yaml ]; then
      if grep -q "\--tls-min-version=VersionTLS12" /host/etc/kubernetes/manifests/kube-apiserver.yaml; then
        exit 0
      fi
    fi
    # Check running process via host proc
    if [ -d /host/proc ]; then
      for pid in $(ls /host/proc | grep '^[0-9]*$'); do
        if [ -f "/host/proc/$pid/cmdline" ] && grep -q kube-apiserver "/host/proc/$pid/cmdline" 2>/dev/null; then
          if tr '\0' ' ' < "/host/proc/$pid/cmdline" | grep -q "\--tls-min-version=VersionTLS12"; then
            exit 0
          fi
        fi
      done
    fi
    # Additional check: ensure no weaker TLS versions are configured
    if command -v kubectl >/dev/null 2>&1; then
      if kubectl get pod -n kube-system -l component=kube-apiserver -o yaml 2>/dev/null | grep -q "\--tls-min-version=VersionTLS1[01]"; then
        echo "FAIL: Weaker TLS version detected (TLS 1.0 or 1.1)"
        exit 1
      fi
    fi
    echo "FAIL: TLS minimum version is not set to 1.2 (--tls-min-version=VersionTLS12 not found)"
    exit 1
