apiVersion: compliance-operator.alauda.io/v1alpha1
kind: Rule
metadata:
  name: stig-k8s-old-components-removed
  namespace: compliance-system
spec:
  id: "V-242433"
  title: "Kubernetes must remove old components after updated versions have been installed."
  description: |
    Previous versions of Kubernetes components that are not removed after updates may be exploited by adversaries. Some malicious software may execute procedures to install older versions of Kubernetes components to reintroduce vulnerabilities that have been mitigated in newer versions.
    
    When Kubernetes components are updated, previous versions should be completely removed from the system to prevent potential security issues and ensure that only the current, patched versions are available for execution.
  checkText: |
    Verify that old versions of Kubernetes components have been removed from the system.
    
    Check for multiple versions of Kubernetes binaries by running:
    find /usr/bin/ -name 'kube*' -type f
    find /usr/local/bin/ -name 'kube*' -type f
    
    If multiple versions of the same Kubernetes component are found (e.g., kubelet, kubectl, kubeadm), this is a finding.
  fixText: |
    Remove old versions of Kubernetes components:
    
    1. Identify all versions of Kubernetes components
    2. Remove outdated versions while keeping only the current version
    3. Verify that only the current version remains
    
    Example:
    rm /usr/bin/kubelet-old
    rm /usr/bin/kubectl-1.24.0
  severity: "medium"
  checkType: "node"
  checkScript: |
    #!/bin/bash
    # Check for multiple versions of Kubernetes binaries
    KUBE_BINS="/host/usr/bin/kubelet /host/usr/bin/kubectl /host/usr/bin/kubeadm"
    for bin in $KUBE_BINS; do
      if [ -f "$bin" ]; then
        # Check if there are backup or old versions
        OLD_VERSIONS=$(find "$(dirname "$bin")" -name "$(basename "$bin")*" -not -path "$bin" 2>/dev/null | wc -l)
        if [ "$OLD_VERSIONS" -gt 0 ]; then
          echo "FAIL: Found old versions of $(basename "$bin")"
          exit 1
        fi
      fi
    done
    exit 0
