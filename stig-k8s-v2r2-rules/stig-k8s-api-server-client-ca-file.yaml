apiVersion: compliance-operator.alauda.io/v1alpha1
kind: Rule
metadata:
  name: stig-k8s-api-server-client-ca-file
  namespace: compliance-system
spec:
  id: "V-242419"
  title: "Kubernetes API Server must have the SSL Certificate Authority set."
  description: |
    Kubernetes control plane and external communication are managed by API Server. The main implementation of the API Server is to manage hardware resources for pods and containers using horizontal or vertical scaling. Anyone who can access the API Server can effectively control the Kubernetes architecture. Using authenticity protection, the communication can be protected against man-in-the-middle attacks/session hijacking and the insertion of false information into sessions.

    The communication session is protected by utilizing transport encryption protocols such as TLS. TLS provides the Kubernetes API Server with a means to authenticate sessions and encrypt traffic. 

    To enable encrypted communication for API Server, the parameter client-ca-file must be set. This parameter gives the location of the SSL Certificate Authority file used to secure API Server communication.
  checkText: |
    Change to the /etc/kubernetes/manifests/ directory on the Kubernetes Control Plane. Run the command:
    grep -i client-ca-file *

    If the setting feature client-ca-file is not set in the Kubernetes API server manifest file or contains no value, this is a finding.
  fixText: |
    Edit the Kubernetes API Server manifest file in the /etc/kubernetes/manifests directory on the Kubernetes Control Plane. 

    Set the value of "--client-ca-file" to path containing Approved Organizational Certificate.
  checkType: "platform"
  severity: "medium"
  checkScript: |
    #!/bin/bash
    # Check if API Server client CA file is configured
    # STIG requirement: --client-ca-file must be set
    
    echo "Checking API Server client CA file configuration..."
    
    client_ca_file_found=false
    
    # Method 1: Check via kubectl for API server pods
    if command -v kubectl >/dev/null 2>&1; then
      if kubectl get pod -n kube-system -l component=kube-apiserver -o yaml 2>/dev/null | grep -q "client-ca-file"; then
        ca_path=$(kubectl get pod -n kube-system -l component=kube-apiserver -o yaml 2>/dev/null | grep "client-ca-file" | head -1 | awk '{print $2}')
        if [ -n "$ca_path" ]; then
          echo "PASS: API Server client-ca-file is configured: $ca_path (kubectl)"
          client_ca_file_found=true
        fi
      fi
    fi
    
    # Method 2: Check API server manifest file
    if [ -f /host/etc/kubernetes/manifests/kube-apiserver.yaml ]; then
      if grep -q "client-ca-file" /host/etc/kubernetes/manifests/kube-apiserver.yaml; then
        ca_path=$(grep "client-ca-file" /host/etc/kubernetes/manifests/kube-apiserver.yaml | head -1 | awk '{print $2}')
        if [ -n "$ca_path" ]; then
          echo "PASS: API Server client-ca-file is configured: $ca_path (manifest)"
          client_ca_file_found=true
        fi
      fi
    fi
    
    # Method 3: Check API server process
    if pgrep kube-apiserver >/dev/null 2>&1; then
      if ps aux | grep kube-apiserver | grep -q "client-ca-file"; then
        echo "PASS: API Server client-ca-file is configured (process)"
        client_ca_file_found=true
      fi
    fi
    
    if [ "$client_ca_file_found" = true ]; then
      exit 0
    else
      echo "FAIL: API Server client-ca-file is not configured"
      exit 1
    fi