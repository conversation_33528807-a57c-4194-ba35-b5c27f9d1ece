apiVersion: compliance-operator.alauda.io/v1alpha1
kind: Rule
metadata:
  name: stig-k8s-api-server-secure-port-set
  namespace: compliance-system
spec:
  id: "V-242389"
  title: "The Kubernetes API server must have the secure port set."
  description: |
    By default, the API server will listen on what is rightfully called the secure port, port 6443. Any requests to this port will perform authentication and authorization checks. If this port is disabled, anyone who gains access to the host on which the Control Plane is running has full control of the entire cluster over encrypted traffic.

    Open the secure port by setting the API server's "--secure-port" flag to a value other than "0".
  checkText: |
    Change to the /etc/kubernetes/manifests directory on the Kubernetes Control Plane. Run the command:
    grep -i secure-port *

    If the setting "--secure-port" is set to "0" or is not configured in the Kubernetes API manifest file, this is a finding.
  fixText: |
    Edit the Kubernetes API Server manifest file in the /etc/kubernetes/manifests directory on the Kubernetes Control Plane.

    Set the value of "--secure-port" to a value greater than "0".
  severity: "medium"
  checkType: "platform"
  checkScript: |
    #!/bin/bash
    # Check if secure port is properly configured
    
    echo "Checking API server secure port configuration..."
    
    SECURE_PORT_FOUND=false
    SECURE_PORT_DISABLED=false
    GREP_ERROR=false
    
    # Check API server via kubectl
    if command -v kubectl >/dev/null 2>&1; then
      GREP_OUTPUT=$(kubectl get pod -n kube-system -l component=kube-apiserver -o yaml 2>/dev/null | grep -q "\--secure-port=6443\|\--secure-port=443" 2>&1)
      if echo "$GREP_OUTPUT" | grep -q "warning"; then
        echo "WARNING: Grep command produced warnings: $GREP_OUTPUT"
        GREP_ERROR=true
      elif [ $? -eq 0 ]; then
        echo "✓ Found secure port configuration via kubectl"
        SECURE_PORT_FOUND=true
      fi
      
      # Check for explicitly disabled secure port
      if kubectl get pod -n kube-system -l component=kube-apiserver -o yaml 2>/dev/null | grep -q "\--secure-port=0"; then
        echo "FAIL: Secure port is explicitly disabled"
        SECURE_PORT_DISABLED=true
      fi
    fi
    
    # Check API server manifest
    if [ -f /host/etc/kubernetes/manifests/kube-apiserver.yaml ]; then
      if grep -q "\--secure-port=6443\|\--secure-port=443" /host/etc/kubernetes/manifests/kube-apiserver.yaml; then
        echo "✓ Found secure port configuration in manifest"
        SECURE_PORT_FOUND=true
      fi
      
      # Check for explicitly disabled secure port
      if grep -q "\--secure-port=0" /host/etc/kubernetes/manifests/kube-apiserver.yaml; then
        echo "FAIL: Secure port is explicitly disabled in manifest"
        SECURE_PORT_DISABLED=true
      fi
    fi
    
    # Check running process via host proc
    if [ -d /host/proc ]; then
      for pid in $(ls /host/proc | grep '^[0-9]*$'); do
        if [ -f "/host/proc/$pid/cmdline" ] && grep -q kube-apiserver "/host/proc/$pid/cmdline" 2>/dev/null; then
          if tr '\0' ' ' < "/host/proc/$pid/cmdline" | grep -q "\--secure-port=6443\|\--secure-port=443"; then
            echo "✓ Found secure port configuration in process"
            SECURE_PORT_FOUND=true
          fi
          
          # Check for explicitly disabled secure port
          if tr '\0' ' ' < "/host/proc/$pid/cmdline" | grep -q "\--secure-port=0"; then
            echo "FAIL: Secure port is explicitly disabled in process"
            SECURE_PORT_DISABLED=true
          fi
          break
        fi
      done
    fi
    
    # Return appropriate exit code based on findings
    if [ "$GREP_ERROR" = "true" ]; then
      echo "WARNING: Grep errors detected, manual verification required"
      exit 2
    elif [ "$SECURE_PORT_DISABLED" = "true" ]; then
      echo "FAIL: Secure port is explicitly disabled"
      exit 1
    elif [ "$SECURE_PORT_FOUND" = "true" ]; then
      echo "PASS: Secure port is properly configured"
      exit 0
    else
      echo "FAIL: Secure port is not properly configured (--secure-port=6443 or --secure-port=443 not found)"
      exit 1
    fi
