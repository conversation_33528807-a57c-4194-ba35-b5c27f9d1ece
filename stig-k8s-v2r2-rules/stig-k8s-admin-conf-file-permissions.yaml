apiVersion: compliance-operator.alauda.io/v1alpha1
kind: Rule
metadata:
  name: stig-k8s-admin-conf-file-permissions
  namespace: compliance-system
spec:
  id: "V-242411"
  title: "The Kubernetes admin.conf must have file permissions set to 644 or more restrictive."
  description: |
    The admin.conf is the administrator kubeconfig file defining administrator user, base64-encoded certificate, and cluster server location. This configuration file contains the credentials necessary to access the cluster as an administrator. If an attacker can gain access to this file, changes can be made to open vulnerabilities and bypass user authorizations inherent within Kubernetes with RBAC implemented.
  checkText: |
    On the Control Plane, run the command:
    stat -c %a /etc/kubernetes/admin.conf

    If the file has permissions more permissive than "644", this is a finding.
  fixText: |
    On the Control Plane, run the command:
    chmod 644 /etc/kubernetes/admin.conf
  severity: "medium"
  checkType: "node"
  checkScript: |
    #!/bin/bash
    # Check admin.conf file permissions
    # STIG requirement: admin.conf should have 644 permissions or more restrictive
    
    echo "Checking admin.conf file permissions..."
    
    ADMIN_CONF_PATHS=(
      "/host/etc/kubernetes/admin.conf"
      "/host/root/.kube/config"
    )
    
    VIOLATIONS_FOUND=false
    FILES_CHECKED=0
    
    for admin_conf_path in "${ADMIN_CONF_PATHS[@]}"; do
      if [ -f "$admin_conf_path" ]; then
        FILES_CHECKED=$((FILES_CHECKED + 1))
        filename=$(basename "$admin_conf_path")
        perms=$(stat -c "%a" "$admin_conf_path" 2>/dev/null)
        
        echo "✓ Found admin config file: $admin_conf_path"
        echo "  Permissions: $perms"
        
        # Check if permissions are more permissive than 644
        if [ "$perms" -gt 644 ]; then
          echo "  ✗ VIOLATION: Admin config file $filename has overly permissive permissions: $perms (should be 644 or more restrictive)"
          VIOLATIONS_FOUND=true
        else
          echo "  ✓ OK: Admin config file $filename has appropriate permissions: $perms"
        fi
      fi
    done
    
    # Check for additional kubeconfig files in common locations
    ADDITIONAL_PATHS=(
      "/host/home/<USER>/.kube/config"
      "/host/root/.kube/admin.conf"
    )
    
    for path_pattern in "${ADDITIONAL_PATHS[@]}"; do
      for config_file in $path_pattern; do
        if [ -f "$config_file" ]; then
          FILES_CHECKED=$((FILES_CHECKED + 1))
          filename=$(basename "$config_file")
          perms=$(stat -c "%a" "$config_file" 2>/dev/null)
          
          echo "✓ Found additional config file: $config_file"
          echo "  Permissions: $perms"
          
          # For admin configs, they should have restrictive permissions
          if echo "$config_file" | grep -q admin; then
            if [ "$perms" -gt 644 ]; then
              echo "  ✗ VIOLATION: Admin config file $filename has overly permissive permissions: $perms (should be 644 or more restrictive)"
              VIOLATIONS_FOUND=true
            else
              echo "  ✓ OK: Admin config file $filename has appropriate permissions: $perms"
            fi
          fi
        fi
      done
    done
    
    if [ "$FILES_CHECKED" -eq 0 ]; then
      echo "WARNING: No admin configuration files found"
      exit 0
    fi
    
    if [ "$VIOLATIONS_FOUND" = true ]; then
      echo "FAIL: Found admin configuration files with overly permissive permissions"
      exit 1
    else
      echo "PASS: All admin configuration files have appropriate permissions (644 or more restrictive)"
      exit 0
    fi