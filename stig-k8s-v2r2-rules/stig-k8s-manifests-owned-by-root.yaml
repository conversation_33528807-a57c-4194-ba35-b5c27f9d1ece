apiVersion: compliance-operator.alauda.io/v1alpha1
kind: Rule
metadata:
  name: stig-k8s-manifests-owned-by-root
  namespace: compliance-system
spec:
  id: "V-242405"
  title: "The Kubernetes manifests must be owned by root."
  description: |
    The manifest files contain the runtime configuration of the API server, proxy, scheduler, controller, and etcd. If an attacker can gain access to these files, changes can be made to open vulnerabilities and bypass user authorizations inherit within Kubernetes with RBAC implemented.
  checkText: |
    On the Control Plane, change to the /etc/kubernetes/manifest directory. Run the command:
    ls -l *

    Each manifest file must be owned by root:root.

    If any manifest file is not owned by root:root, this is a finding.
  fixText: |
    On the Control Plane, change to the /etc/kubernetes/manifest directory. Run the command:
    chown root:root *

    To verify the change took place, run the command:
    ls -l *

    All the manifest files should be owned by root:root.
  severity: "medium"
  checkType: "node"
  checkScript: |
    #!/bin/bash
    # Check manifest file ownership
    # STIG requirement: manifest files should be owned by root:root
    
    echo "Checking Kubernetes manifest file ownership..."
    
    MANIFEST_DIRS="/host/etc/kubernetes/manifests /host/etc/kubernetes/manifest"
    VIOLATIONS_FOUND=false
    
    for MANIFEST_DIR in $MANIFEST_DIRS; do
      if [ -d "$MANIFEST_DIR" ]; then
        echo "✓ Checking manifest directory: $MANIFEST_DIR"
        
        for file in "$MANIFEST_DIR"/*; do
          if [ -f "$file" ]; then
            filename=$(basename "$file")
            owner=$(stat -c "%U:%G" "$file" 2>/dev/null)
            
            if [ -n "$owner" ]; then
              echo "  File: $filename - Owner: $owner"
              
              if [ "$owner" != "root:root" ]; then
                echo "  ✗ VIOLATION: File $filename is not owned by root:root (current: $owner)"
                VIOLATIONS_FOUND=true
              else
                echo "  ✓ OK: File $filename is properly owned by root:root"
              fi
            fi
          fi
        done
      else
        echo "ℹ Manifest directory not found: $MANIFEST_DIR"
      fi
    done
    
    if [ "$VIOLATIONS_FOUND" = true ]; then
      echo "FAIL: Found manifest files not owned by root:root"
      exit 1
    else
      echo "PASS: All manifest files are properly owned by root:root"
      exit 0
    fi