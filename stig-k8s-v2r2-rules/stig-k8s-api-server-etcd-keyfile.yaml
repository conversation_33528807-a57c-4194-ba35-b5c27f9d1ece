apiVersion: compliance-operator.alauda.io/v1alpha1
kind: Rule
metadata:
  name: stig-k8s-api-server-etcd-keyfile
  namespace: compliance-system
spec:
  id: "V-242431"
  title: "Kubernetes etcd must have a key file for secure communication."
  description: |
    Kubernetes stores configuration and state information in a distributed key-value store called etcd. Anyone who can write to etcd can effectively control a Kubernetes cluster. Even just reading the contents of etcd could easily provide helpful hints to a would-be attacker. Using authenticity protection, the communication can be protected against man-in-the-middle attacks/session hijacking and the insertion of false information into sessions.

    The communication session is protected by utilizing transport encryption protocols, such as TLS. TLS provides the Kubernetes API Server and etcd with a means to be able to authenticate sessions and encrypt traffic. 

    To enable encrypted communication for etcd, the parameter "--etcd-keyfile" must be set. This parameter gives the location of the key file used to secure etcd communication.
  checkText: |
    Change to the /etc/kubernetes/manifests directory on the Kubernetes Control Plane. Run the command:
    grep -i etcd-keyfile * 

    If the setting "--etcd-keyfile" is not configured in the Kubernetes API Server manifest file, this is a finding.
  fixText: |
    Edit the Kubernetes API Server manifest file in the /etc/kubernetes/manifests directory on the Kubernetes Control Plane. 

    Set the value of "--etcd-keyfile" to the certificate to be used for communication with etcd.
  checkType: "platform"
  severity: "medium"
  checkScript: |
    #!/bin/bash
    # Check if API Server etcd key file is configured
    # STIG requirement: --etcd-keyfile must be set
    
    echo "Checking API Server etcd key file configuration..."
    
    etcd_keyfile_found=false
    
    # Method 1: Check via kubectl for API server pods
    if command -v kubectl >/dev/null 2>&1; then
      if kubectl get pod -n kube-system -l component=kube-apiserver -o yaml 2>/dev/null | grep -q "etcd-keyfile"; then
        key_path=$(kubectl get pod -n kube-system -l component=kube-apiserver -o yaml 2>/dev/null | grep "etcd-keyfile" | head -1 | awk '{print $2}')
        if [ -n "$key_path" ]; then
          echo "PASS: API Server etcd-keyfile is configured: $key_path (kubectl)"
          etcd_keyfile_found=true
        fi
      fi
    fi
    
    # Method 2: Check API server manifest file
    if [ -f /host/etc/kubernetes/manifests/kube-apiserver.yaml ]; then
      if grep -q "etcd-keyfile" /host/etc/kubernetes/manifests/kube-apiserver.yaml; then
        key_path=$(grep "etcd-keyfile" /host/etc/kubernetes/manifests/kube-apiserver.yaml | head -1 | awk '{print $2}')
        if [ -n "$key_path" ]; then
          echo "PASS: API Server etcd-keyfile is configured: $key_path (manifest)"
          etcd_keyfile_found=true
        fi
      fi
    fi
    
    # Method 3: Check API server process
    if pgrep kube-apiserver >/dev/null 2>&1; then
      if ps aux | grep kube-apiserver | grep -q "etcd-keyfile"; then
        echo "PASS: API Server etcd-keyfile is configured (process)"
        etcd_keyfile_found=true
      fi
    fi
    
    if [ "$etcd_keyfile_found" = true ]; then
      exit 0
    else
      echo "FAIL: API Server etcd-keyfile is not configured"
      exit 1
    fi