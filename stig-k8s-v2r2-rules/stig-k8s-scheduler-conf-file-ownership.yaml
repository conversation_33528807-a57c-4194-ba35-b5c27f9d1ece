apiVersion: compliance-operator.alauda.io/v1alpha1
kind: Rule
metadata:
  name: stig-k8s-scheduler-conf-file-ownership
  namespace: compliance-system
spec:
  id: "V-242412"
  title: "The Kubernetes scheduler.conf must be owned by root."
  description: |
    The scheduler.conf is the kubeconfig file for the Kubernetes Scheduler. The Kubernetes Scheduler is responsible for scheduling pods on available worker nodes. If an attacker can gain access to this file, changes can be made to open vulnerabilities and bypass user authorizations inherent within Kubernetes with RBAC implemented.
  checkText: |
    On the Control Plane, run the command:
    stat -c %U:%G /etc/kubernetes/scheduler.conf

    If the file is not owned by root:root, this is a finding.
  fixText: |
    On the Control Plane, run the command:
    chown root:root /etc/kubernetes/scheduler.conf
  severity: "medium"
  checkType: "node"
  checkScript: |
    #!/bin/bash
    # Check scheduler.conf file ownership
    # STIG requirement: scheduler.conf should be owned by root:root
    
    echo "Checking scheduler.conf file ownership..."
    
    SCHEDULER_CONF_PATHS=(
      "/host/etc/kubernetes/scheduler.conf"
      "/host/etc/kubernetes/scheduler.kubeconfig"
    )
    
    VIOLATIONS_FOUND=false
    FILES_CHECKED=0
    
    for scheduler_conf_path in "${SCHEDULER_CONF_PATHS[@]}"; do
      if [ -f "$scheduler_conf_path" ]; then
        FILES_CHECKED=$((FILES_CHECKED + 1))
        filename=$(basename "$scheduler_conf_path")
        owner=$(stat -c "%U:%G" "$scheduler_conf_path" 2>/dev/null)
        
        echo "✓ Found scheduler config file: $scheduler_conf_path"
        echo "  Owner: $owner"
        
        if [ "$owner" != "root:root" ]; then
          echo "  ✗ VIOLATION: Scheduler config file $filename is not owned by root:root (current: $owner)"
          VIOLATIONS_FOUND=true
        else
          echo "  ✓ OK: Scheduler config file $filename is properly owned by root:root"
        fi
      fi
    done
    
    # Try to find scheduler config from running process
    if [ -d "/host/proc" ]; then
      for pid in $(ls /host/proc | grep '^[0-9]*$'); do
        if [ -f "/host/proc/$pid/cmdline" ] && grep -q kube-scheduler "/host/proc/$pid/cmdline" 2>/dev/null; then
          cmdline=$(tr '\0' ' ' < "/host/proc/$pid/cmdline")
          if echo "$cmdline" | grep -q -- "--kubeconfig"; then
            config_file=$(echo "$cmdline" | sed 's/.*--kubeconfig[= ]\([^ ]*\).*/\1/')
            if [ -f "/host$config_file" ]; then
              FILES_CHECKED=$((FILES_CHECKED + 1))
              owner=$(stat -c "%U:%G" "/host$config_file" 2>/dev/null)
              echo "✓ Found scheduler config from process: /host$config_file"
              echo "  Owner: $owner"
              
              if [ "$owner" != "root:root" ]; then
                echo "  ✗ VIOLATION: Scheduler config file is not owned by root:root (current: $owner)"
                VIOLATIONS_FOUND=true
              else
                echo "  ✓ OK: Scheduler config file is properly owned by root:root"
              fi
            fi
          fi
          break
        fi
      done
    fi
    
    if [ "$FILES_CHECKED" -eq 0 ]; then
      echo "WARNING: No scheduler configuration files found"
      exit 0
    fi
    
    if [ "$VIOLATIONS_FOUND" = true ]; then
      echo "FAIL: Found scheduler configuration files not owned by root:root"
      exit 1
    else
      echo "PASS: All scheduler configuration files are properly owned by root:root"
      exit 0
    fi