apiVersion: compliance-operator.alauda.io/v1alpha1
kind: Rule
metadata:
  name: stig-k8s-controller-manager-conf-file-ownership
  namespace: compliance-system
spec:
  id: "V-242414"
  title: "The Kubernetes controller-manager.conf must be owned by root."
  description: |
    The controller-manager.conf is the kubeconfig file for the Controller Manager. The Controller Manager is responsible for running controller processes. If an attacker can gain access to this file, changes can be made to open vulnerabilities and bypass user authorizations inherent within Kubernetes with RBAC implemented.
  checkText: |
    On the Control Plane, run the command:
    stat -c %U:%G /etc/kubernetes/controller-manager.conf

    If the file is not owned by root:root, this is a finding.
  fixText: |
    On the Control Plane, run the command:
    chown root:root /etc/kubernetes/controller-manager.conf
  severity: "medium"
  checkType: "node"
  checkScript: |
    #!/bin/bash
    # Check controller-manager.conf file ownership
    # STIG requirement: controller-manager.conf should be owned by root:root
    
    echo "Checking controller-manager.conf file ownership..."
    
    CONTROLLER_MANAGER_CONF_PATHS=(
      "/host/etc/kubernetes/controller-manager.conf"
      "/host/etc/kubernetes/controller-manager.kubeconfig"
    )
    
    VIOLATIONS_FOUND=false
    FILES_CHECKED=0
    
    for cm_conf_path in "${CONTROLLER_MANAGER_CONF_PATHS[@]}"; do
      if [ -f "$cm_conf_path" ]; then
        FILES_CHECKED=$((FILES_CHECKED + 1))
        filename=$(basename "$cm_conf_path")
        owner=$(stat -c "%U:%G" "$cm_conf_path" 2>/dev/null)
        
        echo "✓ Found controller-manager config file: $cm_conf_path"
        echo "  Owner: $owner"
        
        if [ "$owner" != "root:root" ]; then
          echo "  ✗ VIOLATION: Controller-manager config file $filename is not owned by root:root (current: $owner)"
          VIOLATIONS_FOUND=true
        else
          echo "  ✓ OK: Controller-manager config file $filename is properly owned by root:root"
        fi
      fi
    done
    
    # Try to find controller-manager config from running process
    if [ -d "/host/proc" ]; then
      for pid in $(ls /host/proc | grep '^[0-9]*$'); do
        if [ -f "/host/proc/$pid/cmdline" ] && grep -q kube-controller-manager "/host/proc/$pid/cmdline" 2>/dev/null; then
          cmdline=$(tr '\0' ' ' < "/host/proc/$pid/cmdline")
          if echo "$cmdline" | grep -q -- "--kubeconfig"; then
            config_file=$(echo "$cmdline" | sed 's/.*--kubeconfig[= ]\([^ ]*\).*/\1/')
            if [ -f "/host$config_file" ]; then
              FILES_CHECKED=$((FILES_CHECKED + 1))
              owner=$(stat -c "%U:%G" "/host$config_file" 2>/dev/null)
              echo "✓ Found controller-manager config from process: /host$config_file"
              echo "  Owner: $owner"
              
              if [ "$owner" != "root:root" ]; then
                echo "  ✗ VIOLATION: Controller-manager config file is not owned by root:root (current: $owner)"
                VIOLATIONS_FOUND=true
              else
                echo "  ✓ OK: Controller-manager config file is properly owned by root:root"
              fi
            fi
          fi
          break
        fi
      done
    fi
    
    if [ "$FILES_CHECKED" -eq 0 ]; then
      echo "WARNING: No controller-manager configuration files found"
      exit 0
    fi
    
    if [ "$VIOLATIONS_FOUND" = true ]; then
      echo "FAIL: Found controller-manager configuration files not owned by root:root"
      exit 1
    else
      echo "PASS: All controller-manager configuration files are properly owned by root:root"
      exit 0
    fi