apiVersion: compliance-operator.alauda.io/v1alpha1
kind: Rule
metadata:
  name: stig-k8s-controller-manager-tls-min-version
  namespace: compliance-system
spec:
  id: "V-242376"
  title: "The Kubernetes Controller Manager must use TLS 1.2, at a minimum, to protect the confidentiality of sensitive data during electronic dissemination."
  description: |
    The Kubernetes Controller Manager will prohibit the use of SSL and unauthorized versions of TLS protocols to properly secure communication.
    
    The use of unsupported protocol exposes vulnerabilities to the Kubernetes by rogue traffic interceptions, man-in-the-middle attacks, and impersonation of users or services from the container platform runtime, registry, and key store. To enable the minimum version of TLS to be used by the Kubernetes Controller Manager, the setting "tls-min-version" must be set.
  checkText: |
    Change to the /etc/kubernetes/manifests/ directory on the Kubernetes Control Plane. Run the command:
    grep -i tls-min-version *
    
    If the setting "tls-min-version" is not configured in the Kubernetes Controller Manager manifest file or it is set to "VersionTLS10" or "VersionTLS11", this is a finding.
  fixText: |
    Edit the Kubernetes Controller Manager manifest file in the /etc/kubernetes/manifests directory on the Kubernetes Control Plane. Set the value of "--tls-min-version" to "VersionTLS12" or higher.
  severity: "medium"
  checkType: "platform"
  checkScript: |
    #!/bin/bash
    # Check if TLS minimum version is configured
    # Check controller manager via kubectl and manifest files
    if command -v kubectl >/dev/null 2>&1; then
      if kubectl get pod -n kube-system -l component=kube-controller-manager -o yaml 2>/dev/null | grep -q "\--tls-min-version=VersionTLS12"; then
        exit 0
      fi
    fi
    # Check controller manager manifest
    if [ -f /host/etc/kubernetes/manifests/kube-controller-manager.yaml ]; then
      if grep -q "\--tls-min-version=VersionTLS12" /host/etc/kubernetes/manifests/kube-controller-manager.yaml; then
        exit 0
      fi
    fi
    # Check running process via host proc
    if [ -d /host/proc ]; then
      for pid in $(ls /host/proc | grep '^[0-9]*$'); do
        if [ -f "/host/proc/$pid/cmdline" ] && grep -q kube-controller-manager "/host/proc/$pid/cmdline" 2>/dev/null; then
          if tr '\0' ' ' < "/host/proc/$pid/cmdline" | grep -q "\--tls-min-version=VersionTLS12"; then
            exit 0
          fi
        fi
      done
    fi
    # Additional check: ensure no weaker TLS versions are configured
    if command -v kubectl >/dev/null 2>&1; then
      if kubectl get pod -n kube-system -l component=kube-controller-manager -o yaml 2>/dev/null | grep -q "\--tls-min-version=VersionTLS1[01]"; then
        echo "FAIL: Weaker TLS version detected in Controller Manager (TLS 1.0 or 1.1)"
        exit 1
      fi
    fi
    echo "FAIL: Controller Manager TLS minimum version not set (--tls-min-version=VersionTLS12 not found)"
    exit 1
