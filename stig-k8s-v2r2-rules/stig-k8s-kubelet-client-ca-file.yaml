apiVersion: compliance-operator.alauda.io/v1alpha1
kind: Rule
metadata:
  name: stig-k8s-kubelet-client-ca-file
  namespace: compliance-system
spec:
  id: "V-242420"
  title: "Kubernetes Kubelet must have the SSL Certificate Authority set."
  description: |
    Kubernetes container and pod configuration are maintained by Kubelet. Kubelet agents register nodes with the API Server, mount volume storage, and perform health checks for containers and pods. Anyone who gains access to Kubelet agents can effectively control applications within the pods and containers. Using authenticity protection, the communication can be protected against man-in-the-middle attacks/session hijacking and the insertion of false information into sessions.

    The communication session is protected by utilizing transport encryption protocols such as TLS. TLS provides the Kubernetes API Server with a means to authenticate sessions and encrypt traffic.

    To enable encrypted communication for Kubelet, the clientCAFile must be set. This parameter gives the location of the SSL Certificate Authority file used to secure Kubelet communication.
  checkText: |
    On the Control Plane, run the command:
    ps -ef | grep kubelet

    If the "--client-ca-file" option exists, this is a finding.

    Note the path to the config file (identified by --config).

    Run the command:
    grep -i clientCAFile <path_to_config_file>

    If the setting "clientCAFile" is not set or contains no value, this is a finding.
  fixText: |
    On the Control Plane, run the command:
    ps -ef | grep kubelet

    Remove the "--client-ca-file" option if present.

    Note the path to the config file (identified by --config).

    Edit the Kubernetes Kubelet config file: 
    Set the value of "clientCAFile" to a path containing an Approved Organizational Certificate. 

    Restart the kubelet service using the following command:
    systemctl daemon-reload && systemctl restart kubelet
  severity: "medium"
  checkType: "node"
  checkScript: |
    #!/bin/bash
    # Check kubelet client CA file configuration
    # STIG requirement: clientCAFile should be set in config, not as command line option
    
    echo "Checking kubelet client CA file configuration..."
    
    CLIENT_CA_CONFIGURED=false
    COMMAND_LINE_OPTION_FOUND=false
    CONFIG_FOUND=false
    
    # Method 1: Check if --client-ca-file is used as command line option (this is a violation)
    if [ -d "/host/proc" ]; then
      for pid in $(ls /host/proc | grep '^[0-9]*$'); do
        if [ -f "/host/proc/$pid/cmdline" ] && grep -q kubelet "/host/proc/$pid/cmdline" 2>/dev/null; then
          cmdline=$(tr '\0' ' ' < "/host/proc/$pid/cmdline")
          echo "✓ Found kubelet process (PID: $pid)"
          
          if echo "$cmdline" | grep -q -- "--client-ca-file"; then
            ca_file=$(echo "$cmdline" | sed 's/.*--client-ca-file[= ]\([^ ]*\).*/\1/')
            echo "  ✗ VIOLATION: Found --client-ca-file as command line option: $ca_file"
            COMMAND_LINE_OPTION_FOUND=true
          fi
          break
        fi
      done
    fi
    
    # Method 2: Check kubelet config files for clientCAFile setting
    KUBELET_CONFIG_PATHS=(
      "/host/var/lib/kubelet/config.yaml"
      "/host/etc/kubernetes/kubelet/kubelet-config.yaml"
      "/host/etc/kubernetes/kubelet.yaml"
    )
    
    for config_path in "${KUBELET_CONFIG_PATHS[@]}"; do
      if [ -f "$config_path" ]; then
        CONFIG_FOUND=true
        echo "✓ Checking kubelet config file: $config_path"
        
        if grep -q "clientCAFile:" "$config_path"; then
          ca_file=$(grep "clientCAFile:" "$config_path" | sed 's/.*clientCAFile: *"\?\([^"]*\)"\?.*/\1/')
          if [ -n "$ca_file" ]; then
            echo "  ✓ Found clientCAFile in config: $ca_file"
            
            # Verify the CA file exists
            if [ -f "/host$ca_file" ]; then
              echo "  ✓ CA file exists: /host$ca_file"
              CLIENT_CA_CONFIGURED=true
            else
              echo "  ✗ WARNING: CA file does not exist: /host$ca_file"
            fi
          else
            echo "  ✗ clientCAFile is set but has no value"
          fi
        else
          echo "  ℹ No clientCAFile setting found in config file"
        fi
      fi
    done
    
    # Try to find kubelet config from running process
    if [ -d "/host/proc" ] && [ "$CONFIG_FOUND" = false ]; then
      for pid in $(ls /host/proc | grep '^[0-9]*$'); do
        if [ -f "/host/proc/$pid/cmdline" ] && grep -q kubelet "/host/proc/$pid/cmdline" 2>/dev/null; then
          cmdline=$(tr '\0' ' ' < "/host/proc/$pid/cmdline")
          if echo "$cmdline" | grep -q -- "--config"; then
            config_file=$(echo "$cmdline" | sed 's/.*--config[= ]\([^ ]*\).*/\1/')
            if [ -f "/host$config_file" ]; then
              CONFIG_FOUND=true
              echo "✓ Found kubelet config from process: /host$config_file"
              
              if grep -q "clientCAFile:" "/host$config_file"; then
                ca_file=$(grep "clientCAFile:" "/host$config_file" | sed 's/.*clientCAFile: *"\?\([^"]*\)"\?.*/\1/')
                if [ -n "$ca_file" ]; then
                  echo "  ✓ Found clientCAFile in config: $ca_file"
                  
                  if [ -f "/host$ca_file" ]; then
                    echo "  ✓ CA file exists: /host$ca_file"
                    CLIENT_CA_CONFIGURED=true
                  else
                    echo "  ✗ WARNING: CA file does not exist: /host$ca_file"
                  fi
                fi
              fi
            fi
          fi
          break
        fi
      done
    fi
    
    # Evaluate results
    echo "=== EVALUATION ==="
    echo "Command line option found: $COMMAND_LINE_OPTION_FOUND"
    echo "Config found: $CONFIG_FOUND"
    echo "Client CA configured: $CLIENT_CA_CONFIGURED"
    
    if [ "$COMMAND_LINE_OPTION_FOUND" = true ]; then
      echo "FAIL: Found --client-ca-file as command line option (should be in config file)"
      exit 1
    fi
    
    if [ "$CONFIG_FOUND" = false ]; then
      echo "FAIL: No kubelet configuration found"
      exit 1
    fi
    
    if [ "$CLIENT_CA_CONFIGURED" = false ]; then
      echo "FAIL: clientCAFile is not properly configured in kubelet config"
      exit 1
    fi
    
    echo "PASS: clientCAFile is properly configured in kubelet config file"
    exit 0