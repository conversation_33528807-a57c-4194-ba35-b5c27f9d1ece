apiVersion: compliance-operator.alauda.io/v1alpha1
kind: Rule
metadata:
  name: stig-k8s-kubelet-config-file-ownership
  namespace: compliance-system
spec:
  id: "V-242406"
  title: "The Kubernetes KubeletConfiguration file must be owned by root."
  description: |
    The kubelet configuration file contains the runtime configuration of the kubelet service. If an attacker can gain access to this file, changes can be made to open vulnerabilities and bypass user authorizations inherent within Kubernetes with RBAC implemented.
  checkText: |
    On the Kubernetes Control Plane and Worker nodes, run the command:
    ps -ef | grep kubelet

    Check the config file (path identified by: --config):

    Change to the directory identified by --config (example /etc/sysconfig/) run the command:
    ls -l kubelet

    Each kubelet configuration file must be owned by root:root.

    If any manifest file is not owned by root:root, this is a finding.
  fixText: |
    On the Control Plane and Worker nodes, change to the --config directory. Run the command:
    chown root:root kubelet

    To verify the change took place, run the command:
    ls -l kubelet

    The kubelet file should now be owned by root:root.
  severity: "medium"
  checkType: "node"
  checkScript: |
    #!/bin/bash
    # Check kubelet configuration file ownership
    # STIG requirement: kubelet config files should be owned by root:root
    
    echo "Checking kubelet configuration file ownership..."
    
    VIOLATIONS_FOUND=false
    CONFIG_FILES_CHECKED=0
    
    # Common kubelet config file locations
    KUBELET_CONFIG_PATHS=(
      "/host/var/lib/kubelet/config.yaml"
      "/host/etc/kubernetes/kubelet/kubelet-config.yaml"
      "/host/etc/kubernetes/kubelet.yaml"
      "/host/etc/sysconfig/kubelet"
      "/host/etc/default/kubelet"
    )
    
    # Check each potential kubelet config file
    for config_path in "${KUBELET_CONFIG_PATHS[@]}"; do
      if [ -f "$config_path" ]; then
        CONFIG_FILES_CHECKED=$((CONFIG_FILES_CHECKED + 1))
        filename=$(basename "$config_path")
        owner=$(stat -c "%U:%G" "$config_path" 2>/dev/null)
        
        echo "✓ Found kubelet config file: $config_path"
        echo "  Owner: $owner"
        
        if [ "$owner" != "root:root" ]; then
          echo "  ✗ VIOLATION: Kubelet config file $filename is not owned by root:root (current: $owner)"
          VIOLATIONS_FOUND=true
        else
          echo "  ✓ OK: Kubelet config file $filename is properly owned by root:root"
        fi
      fi
    done
    
    # Try to find kubelet config from running process
    if [ -d "/host/proc" ]; then
      for pid in $(ls /host/proc | grep '^[0-9]*$'); do
        if [ -f "/host/proc/$pid/cmdline" ] && grep -q kubelet "/host/proc/$pid/cmdline" 2>/dev/null; then
          cmdline=$(tr '\0' ' ' < "/host/proc/$pid/cmdline")
          if echo "$cmdline" | grep -q -- "--config"; then
            config_file=$(echo "$cmdline" | sed 's/.*--config[= ]\([^ ]*\).*/\1/')
            if [ -f "/host$config_file" ]; then
              CONFIG_FILES_CHECKED=$((CONFIG_FILES_CHECKED + 1))
              owner=$(stat -c "%U:%G" "/host$config_file" 2>/dev/null)
              echo "✓ Found kubelet config from process: /host$config_file"
              echo "  Owner: $owner"
              
              if [ "$owner" != "root:root" ]; then
                echo "  ✗ VIOLATION: Kubelet config file is not owned by root:root (current: $owner)"
                VIOLATIONS_FOUND=true
              else
                echo "  ✓ OK: Kubelet config file is properly owned by root:root"
              fi
            fi
          fi
          break
        fi
      done
    fi
    
    if [ "$CONFIG_FILES_CHECKED" -eq 0 ]; then
      echo "WARNING: No kubelet configuration files found"
      # Return exit code 2 for MANUAL status (needs human review)
      exit 2
    fi
    
    if [ "$VIOLATIONS_FOUND" = true ]; then
      echo "FAIL: Found kubelet configuration files not owned by root:root"
      exit 1
    else
      echo "PASS: All kubelet configuration files are properly owned by root:root"
      exit 0
    fi