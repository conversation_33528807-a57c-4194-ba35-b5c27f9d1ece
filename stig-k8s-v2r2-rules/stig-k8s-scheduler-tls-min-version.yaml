apiVersion: compliance-operator.alauda.io/v1alpha1
kind: Rule
metadata:
  name: stig-k8s-scheduler-tls-min-version
  namespace: compliance-system
spec:
  id: "V-242377"
  title: \"The Kubernetes Scheduler must use TLS 1.2, at a minimum, to protect the confidentiality of sensitive data during electronic dissemination.\"
  description: |
    The Kubernetes Scheduler will prohibit the use of SSL and unauthorized versions of TLS protocols to properly secure communication.
    
    The use of unsupported protocol exposes vulnerabilities to the Kubernetes by rogue traffic interceptions, man-in-the-middle attacks, and impersonation of users or services from the container platform runtime, registry, and key store. To enable the minimum version of TLS to be used by the Kubernetes Scheduler, the setting \"tls-min-version\" must be set.
  checkText: |
    Change to the /etc/kubernetes/manifests directory on the Kubernetes Control Plane. Run the command:
    grep -i tls-min-version *
    
    Review the \"--tls-min-version\" setting in the Kubernetes Scheduler manifest file.
    
    If the \"--tls-min-version\" setting does not exist or is not set to \"VersionTLS12\" or greater (e.g., \"VersionTLS13\"), this is a finding.
  fixText: |
    Edit the Kubernetes Scheduler manifest file in the /etc/kubernetes/manifests directory on the Kubernetes Control Plane.
    
    Set the value of \"--tls-min-version\" to \"VersionTLS12\" or greater (e.g., \"VersionTLS13\").
  severity: \"medium\"
  checkType: \"node\"
  checkScript: |
    #!/bin/bash
    # Check if TLS minimum version is configured
    # Check scheduler via kubectl and manifest files
    if command -v kubectl >/dev/null 2>&1; then
      if kubectl get pod -n kube-system -l component=kube-scheduler -o yaml 2>/dev/null | grep -q "\--tls-min-version=VersionTLS12"; then
        exit 0
      fi
    fi
    # Check scheduler manifest
    if [ -f /host/etc/kubernetes/manifests/kube-scheduler.yaml ]; then
      if grep -q "\--tls-min-version=VersionTLS12" /host/etc/kubernetes/manifests/kube-scheduler.yaml; then
        exit 0
      fi
    fi
    # Check running process via host proc
    if [ -d /host/proc ]; then
      for pid in $(ls /host/proc | grep '^[0-9]*$'); do
        if [ -f "/host/proc/$pid/cmdline" ] && grep -q kube-scheduler "/host/proc/$pid/cmdline" 2>/dev/null; then
          if tr '\0' ' ' < "/host/proc/$pid/cmdline" | grep -q "\--tls-min-version=VersionTLS12"; then
            exit 0
          fi
        fi
      done
    fi
    # Additional check: also accept TLS 1.3 as it's more secure than 1.2
    if command -v kubectl >/dev/null 2>&1; then
      if kubectl get pod -n kube-system -l component=kube-scheduler -o yaml 2>/dev/null | grep -q "\--tls-min-version=VersionTLS13"; then
        echo "INFO: TLS 1.3 is configured (more secure than required TLS 1.2)"
        exit 0
      fi
    fi
    # Check for insecure TLS versions
    if command -v kubectl >/dev/null 2>&1; then
      if kubectl get pod -n kube-system -l component=kube-scheduler -o yaml 2>/dev/null | grep -E "\--tls-min-version=VersionTLS1[01]"; then
        echo "FAIL: Scheduler is using insecure TLS version (TLS 1.0 or 1.1)"
        exit 1
      fi
    fi
    echo "FAIL: Scheduler TLS minimum version not set to TLS 1.2 or higher (--tls-min-version=VersionTLS12 not found)"
    exit 1
