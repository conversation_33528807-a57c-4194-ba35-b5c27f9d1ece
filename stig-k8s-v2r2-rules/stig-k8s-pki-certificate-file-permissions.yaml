apiVersion: compliance-operator.alauda.io/v1alpha1
kind: Rule
metadata:
  name: stig-k8s-pki-certificate-file-permissions
  namespace: compliance-system
spec:
  id: "V-242417"
  title: "The Kubernetes PKI CRT must have file permissions set to 644 or more restrictive."
  description: |
    The Kubernetes PKI directory contains all the public/private key pairs and certificates used by Kubernetes components. Certificate files contain public keys and should be readable but not writable by non-privileged users. If an attacker can modify these files, they could potentially compromise the security of the cluster.
  checkText: |
    On the Control Plane, run the command:
    stat -c %a /etc/kubernetes/pki/*.crt

    If any certificate file has permissions more permissive than "644", this is a finding.
  fixText: |
    On the Control Plane, run the command:
    chmod 644 /etc/kubernetes/pki/*.crt
  severity: "medium"
  checkType: "node"
  checkScript: |
    #!/bin/bash
    # Check PKI certificate file permissions
    # STIG requirement: Certificate files should have 644 permissions or more restrictive
    
    echo "Checking PKI certificate file permissions..."
    
    PKI_PATHS=(
      "/host/etc/kubernetes/pki"
      "/host/var/lib/kubernetes/pki"
    )
    
    VIOLATIONS_FOUND=false
    CERT_FILES_CHECKED=0
    
    for pki_path in "${PKI_PATHS[@]}"; do
      if [ -d "$pki_path" ]; then
        echo "✓ Found PKI directory: $pki_path"
        
        # Check certificate files (.crt, .pem)
        find "$pki_path" -name "*.crt" -o -name "*.pem" | grep -v "\.key" | while read -r cert_file; do
          CERT_FILES_CHECKED=$((CERT_FILES_CHECKED + 1))
          filename=$(basename "$cert_file")
          perms=$(stat -c "%a" "$cert_file" 2>/dev/null)
          
          echo "  ✓ Checking certificate file: $filename"
          echo "    Permissions: $perms"
          
          # Check if permissions are more permissive than 644
          if [ "$perms" -gt 644 ]; then
            echo "    ✗ VIOLATION: Certificate file $filename has overly permissive permissions: $perms (should be 644 or more restrictive)"
            VIOLATIONS_FOUND=true
          else
            echo "    ✓ OK: Certificate file $filename has appropriate permissions: $perms"
          fi
        done
      fi
    done
    
    # Check for additional certificate files in common locations
    ADDITIONAL_CERT_PATHS=(
      "/host/etc/ssl/certs/kubernetes"
      "/host/etc/kubernetes/ssl"
    )
    
    for additional_path in "${ADDITIONAL_CERT_PATHS[@]}"; do
      if [ -d "$additional_path" ]; then
        echo "✓ Found additional certificate directory: $additional_path"
        
        find "$additional_path" -name "*.crt" -o -name "*.pem" | grep -v "\.key" | while read -r cert_file; do
          CERT_FILES_CHECKED=$((CERT_FILES_CHECKED + 1))
          filename=$(basename "$cert_file")
          perms=$(stat -c "%a" "$cert_file" 2>/dev/null)
          
          echo "  ✓ Checking additional certificate file: $filename"
          echo "    Permissions: $perms"
          
          if [ "$perms" -gt 644 ]; then
            echo "    ✗ VIOLATION: Additional certificate file $filename has overly permissive permissions: $perms (should be 644 or more restrictive)"
            VIOLATIONS_FOUND=true
          fi
        done
      fi
    done
    
    if [ "$CERT_FILES_CHECKED" -eq 0 ]; then
      echo "WARNING: No certificate files found"
      exit 0
    fi
    
    if [ "$VIOLATIONS_FOUND" = true ]; then
      echo "FAIL: Found certificate files with overly permissive permissions"
      exit 1
    else
      echo "PASS: All certificate files have appropriate permissions (644 or more restrictive)"
      exit 0
    fi