apiVersion: compliance-operator.alauda.io/v1alpha1
kind: Rule
metadata:
  name: stig-k8s-api-server-cipher-suites
  namespace: compliance-system
spec:
  id: "V-242418"
  title: "The Kubernetes API server must use approved cipher suites."
  description: |
    The Kubernetes API server communicates to the kubelet service on the nodes to deploy, update, and delete resources. If an attacker were able to get between this communication and modify the request, the Kubernetes cluster could be compromised. Using approved cypher suites for the communication ensures the protection of the transmitted information, confidentiality, and integrity so that the attacker cannot read or alter this communication.
  checkText: |
    Change to the /etc/kubernetes/manifests/ directory on the Kubernetes Control Plane. Run the command:
    grep -i tls-cipher-suites *

    If the setting feature tls-cipher-suites is not set in the Kubernetes API server manifest file or contains no value or does not contain TLS_ECDHE_ECDSA_WITH_AES_128_GCM_SHA256,TLS_ECDHE_RSA_WITH_AES_128_GCM_SHA256,TLS_ECDHE_RSA_WITH_AES_256_GCM_SHA384,TLS_ECDHE_ECDSA_WITH_AES_256_GCM_SHA384, this is a finding.
  fixText: |
    Edit the Kubernetes API Server manifest file in the /etc/kubernetes/manifests directory on the Kubernetes Control Plane.

    Set the value of "--tls-cipher-suites" to:
    "TLS_ECDHE_ECDSA_WITH_AES_128_GCM_SHA256,TLS_ECDHE_RSA_WITH_AES_128_GCM_SHA256,TLS_ECDHE_RSA_WITH_AES_256_GCM_SHA384,TLS_ECDHE_ECDSA_WITH_AES_256_GCM_SHA384"
  severity: "medium"
  checkType: "platform"
  checkScript: |
    #!/bin/bash
    # Check if approved cipher suites are configured
    # Check API server via kubectl and manifest files
    if command -v kubectl >/dev/null 2>&1; then
      if kubectl get pod -n kube-system -l component=kube-apiserver -o yaml 2>/dev/null | grep -q "tls-cipher-suites"; then
        exit 0
      fi
    fi
    # Check API server manifest
    if [ -f /host/etc/kubernetes/manifests/kube-apiserver.yaml ]; then
      if grep -q "tls-cipher-suites" /host/etc/kubernetes/manifests/kube-apiserver.yaml; then
        exit 0
      fi
    fi
    # Check running process via host proc
    if [ -d /host/proc ]; then
      for pid in $(ls /host/proc | grep '^[0-9]*$'); do
        if [ -f "/host/proc/$pid/cmdline" ] && grep -q kube-apiserver "/host/proc/$pid/cmdline" 2>/dev/null; then
          if tr '\0' ' ' < "/host/proc/$pid/cmdline" | grep -q "tls-cipher-suites"; then
            exit 0
          fi
        fi
      done
    fi
    echo "FAIL: Approved cipher suites are not configured"
    exit 1
