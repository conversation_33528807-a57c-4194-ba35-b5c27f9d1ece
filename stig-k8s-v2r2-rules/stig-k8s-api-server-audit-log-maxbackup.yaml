apiVersion: compliance-operator.alauda.io/v1alpha1
kind: Rule
metadata:
  name: stig-k8s-api-server-audit-log-maxbackup
  namespace: compliance-system
spec:
  id: "V-242463"
  title: "Kubernetes API Server audit log maximum backup must be set."
  description: |
    Kubernetes API Server validates and configures pods and services for the API object. The REST operation provides frontend functionality to the cluster share state. Audit logs capture individual user, administrator, and system accesses to the API Server. The audit log maximum backup setting determines the maximum number of audit log files to retain. The backup retention must be configured to prevent the loss of audit data.
  checkText: |
    Change to the /etc/kubernetes/manifests directory on the Kubernetes Control Plane. Run the command:
    grep -i audit-log-maxbackup *

    If the setting audit-log-maxbackup is not set or is set to less than 10, this is a finding.
  fixText: |
    Edit the Kubernetes API Server manifest file in the /etc/kubernetes/manifests directory on the Kubernetes Control Plane. Set the argument "--audit-log-maxbackup" to "10" or more.
  severity: "medium"
  checkType: "platform"
  checkScript: |
    #!/bin/bash
    # Check API Server audit log maximum backup configuration
    # STIG requirement: --audit-log-maxbackup must be set to 10 or more
    
    echo "Checking API Server audit log maximum backup configuration..."
    
    AUDIT_MAXBACKUP_CONFIGURED=false
    AUDIT_MAXBACKUP_VALUE=""
    
    # Method 1: Check via kubectl for API server pods
    if command -v kubectl >/dev/null 2>&1; then
      if kubectl get pod -n kube-system -l component=kube-apiserver -o yaml 2>/dev/null | grep -q "audit-log-maxbackup"; then
        maxbackup_value=$(kubectl get pod -n kube-system -l component=kube-apiserver -o yaml 2>/dev/null | grep "audit-log-maxbackup" | head -1 | sed 's/.*--audit-log-maxbackup[= ]\([^ ]*\).*/\1/')
        if [ -n "$maxbackup_value" ]; then
          echo "✓ Found audit-log-maxbackup in API server: $maxbackup_value (kubectl)"
          AUDIT_MAXBACKUP_CONFIGURED=true
          AUDIT_MAXBACKUP_VALUE="$maxbackup_value"
        fi
      fi
    fi
    
    # Method 2: Check API server manifest file
    if [ -f "/host/etc/kubernetes/manifests/kube-apiserver.yaml" ]; then
      echo "✓ Checking API server manifest: /host/etc/kubernetes/manifests/kube-apiserver.yaml"
      
      if grep -q "audit-log-maxbackup" "/host/etc/kubernetes/manifests/kube-apiserver.yaml"; then
        maxbackup_value=$(grep "audit-log-maxbackup" "/host/etc/kubernetes/manifests/kube-apiserver.yaml" | head -1 | sed 's/.*--audit-log-maxbackup[= ]\([^ ]*\).*/\1/')
        if [ -n "$maxbackup_value" ]; then
          echo "  ✓ Found audit-log-maxbackup in manifest: $maxbackup_value"
          AUDIT_MAXBACKUP_CONFIGURED=true
          AUDIT_MAXBACKUP_VALUE="$maxbackup_value"
        fi
      else
        echo "  ✗ No audit-log-maxbackup found in manifest"
      fi
    fi
    
    # Method 3: Check running API server process
    if [ -d "/host/proc" ]; then
      for pid in $(ls /host/proc | grep '^[0-9]*$'); do
        if [ -f "/host/proc/$pid/cmdline" ] && grep -q kube-apiserver "/host/proc/$pid/cmdline" 2>/dev/null; then
          cmdline=$(tr '\0' ' ' < "/host/proc/$pid/cmdline")
          if echo "$cmdline" | grep -q "audit-log-maxbackup"; then
            maxbackup_value=$(echo "$cmdline" | sed 's/.*--audit-log-maxbackup[= ]\([^ ]*\).*/\1/')
            echo "✓ Found audit-log-maxbackup in API server process: $maxbackup_value"
            AUDIT_MAXBACKUP_CONFIGURED=true
            AUDIT_MAXBACKUP_VALUE="$maxbackup_value"
          fi
          break
        fi
      done
    fi
    
    if [ "$AUDIT_MAXBACKUP_CONFIGURED" = false ]; then
      echo "FAIL: API Server audit-log-maxbackup is not configured"
      exit 1
    fi
    
    # Check if the value is numeric and >= 10
    if ! [[ "$AUDIT_MAXBACKUP_VALUE" =~ ^[0-9]+$ ]]; then
      echo "FAIL: audit-log-maxbackup value is not numeric: $AUDIT_MAXBACKUP_VALUE"
      exit 1
    fi
    
    if [ "$AUDIT_MAXBACKUP_VALUE" -lt 10 ]; then
      echo "FAIL: audit-log-maxbackup is set to $AUDIT_MAXBACKUP_VALUE (must be 10 or more)"
      exit 1
    fi
    
    echo "PASS: API Server audit-log-maxbackup is properly configured: $AUDIT_MAXBACKUP_VALUE backups"
    exit 0