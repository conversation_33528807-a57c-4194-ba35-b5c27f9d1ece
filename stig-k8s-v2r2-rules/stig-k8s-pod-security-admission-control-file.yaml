apiVersion: compliance-operator.alauda.io/v1alpha1
kind: Rule
metadata:
  name: stig-k8s-pod-security-admission-control-file
  namespace: compliance-system
spec:
  id: "V-242405"
  title: "Kubernetes must have a Pod Security Admission control file configured."
  description: |
    Pod security restrictions are applied at the namespace level when pods are created. Pod Security Admission is an admission controller that validates pods against the Pod Security Standards and either allows, denies, or warns about the pod creation based on the configured policy.
    
    The admission control configuration file defines the policies and settings for various admission controllers, including Pod Security Admission. This file ensures that consistent security policies are applied across the cluster and that pods meet the defined security requirements before they are created.
    
    Without proper admission control configuration, pods may be created with insecure settings that could compromise the cluster's security posture.
  checkText: |
    Change to the /etc/kubernetes/manifests directory on the Kubernetes Control Plane. Run the command:
    grep -i admission-control-config-file *
    
    Review the \"--admission-control-config-file\" setting in the Kubernetes API Server manifest file.
    
    If the \"--admission-control-config-file\" setting is not configured, this is a finding.
    
    Verify that the referenced admission control configuration file exists and contains appropriate Pod Security Admission settings.
  fixText: |
    Create a Pod Security Admission control configuration file and configure the API Server to use it:
    
    1. Create an admission control configuration file (e.g., /etc/kubernetes/admission-control-config.yaml)
    2. Configure Pod Security Admission policies in the file
    3. Edit the Kubernetes API Server manifest file to include:
       --admission-control-config-file=/etc/kubernetes/admission-control-config.yaml
    
    Example admission control configuration:
    apiVersion: apiserver.config.k8s.io/v1
    kind: AdmissionConfiguration
    plugins:
    - name: PodSecurity
      configuration:
        apiVersion: pod-security.admission.config.k8s.io/v1beta1
        kind: PodSecurityConfiguration
        defaults:
          enforce: \"baseline\"
          audit: \"baseline\"
          warn: \"baseline\"
  severity: "high"
  checkType: "platform"
  checkScript: |
    #!/bin/bash
    # Check if Pod Security Admission control file is configured
    # Check API server via kubectl and manifest files
    if command -v kubectl >/dev/null 2>&1; then
      if kubectl get pod -n kube-system -l component=kube-apiserver -o yaml 2>/dev/null | grep -q "\--admission-control-config-file"; then
        exit 0
      fi
    fi
    # Check API server manifest
    if [ -f /host/etc/kubernetes/manifests/kube-apiserver.yaml ]; then
      if grep -q "\--admission-control-config-file" /host/etc/kubernetes/manifests/kube-apiserver.yaml; then
        exit 0
      fi
    fi
    # Check running process via host proc
    if [ -d /host/proc ]; then
      for pid in $(ls /host/proc | grep '^[0-9]*$'); do
        if [ -f "/host/proc/$pid/cmdline" ] && grep -q kube-apiserver "/host/proc/$pid/cmdline" 2>/dev/null; then
          if tr '\0' ' ' < "/host/proc/$pid/cmdline" | grep -q "\--admission-control-config-file"; then
            exit 0
          fi
        fi
      done
    fi
    # Additional check: verify the admission control config file exists and is valid
    if command -v kubectl >/dev/null 2>&1; then
      CONFIG_FILE=$(kubectl get pod -n kube-system -l component=kube-apiserver -o yaml 2>/dev/null | grep "\--admission-control-config-file" | sed 's/.*--admission-control-config-file=\([^ ]*\).*/\1/')
      if [ -n "$CONFIG_FILE" ] && [ -f "/host$CONFIG_FILE" ]; then
        echo "INFO: Pod Security Admission config file found at $CONFIG_FILE"
        exit 0
      fi
    fi
    echo "FAIL: Pod Security Admission control file not configured (--admission-control-config-file not found)"
    exit 1
