apiVersion: compliance-operator.alauda.io/v1alpha1
kind: Rule
metadata:
  name: stig-k8s-kubelet-anonymous-auth-disabled
  namespace: compliance-system
spec:
  id: "V-242391"
  title: "The Kubernetes Kubelet must have anonymous authentication disabled."
  description: |
    A user who has access to the Kubelet essentially has root access to the nodes contained within the Kubernetes Control Plane. To control access, users must be authenticated and authorized. By allowing anonymous connections, the controls put in place to secure the Kubelet can be bypassed.
    
    Setting anonymous authentication to \"false\" also disables unauthenticated requests from kubelets.
    
    While there are instances where anonymous connections may be needed (e.g., health checks, metrics collection, etc.) and Role-Based Access Controls (RBAC) are in place to limit the anonymous user, this access must be evaluated against the risk of granting any user to the Kubelet.
  checkText: |
    On the Control Plane, run the command:
    ps -ef | grep kubelet
    
    Check the config file(s) referenced by the --config argument (example: --config=/etc/kubernetes/kubelet/kubelet-config.yaml):
    
    Change to the directory identified by --config argument and run the command:
    grep -i anonymous *
    
    If the setting \"anonymous\" is not set to \"false\", this is a finding.
    
    If the setting \"anonymous\" is not configured, check if there is an entry for \"--anonymous-auth\" in the kubelet command.
    
    On the Control Plane, run the command:
    ps -ef | grep kubelet
    
    If \"--anonymous-auth\" is set to \"true\" or is not configured, this is a finding.
  fixText: |
    On the Control Plane, run the command:
    ps -ef | grep kubelet
    
    Check the config file(s) referenced by the --config argument (example: --config=/etc/kubernetes/kubelet/kubelet-config.yaml):
    
    Edit the Kubernetes Kubelet config file:
    Set the value of \"anonymous\" to \"false\".
    
    Reset Kubelet service using the following command:
    service kubelet restart
  severity: "high"
  checkType: "node"
  checkScript: |
    #!/bin/bash
    # Check if kubelet anonymous authentication is disabled
    # STIG requirement: anonymous authentication should be disabled
    
    ANONYMOUS_DISABLED=false
    CONFIG_FOUND=false
    
    echo "Checking kubelet anonymous authentication configuration..."
    
    # Function to check kubelet config file for anonymous auth setting
    check_kubelet_config() {
      local config_file="$1"
      echo "Checking config file: $config_file"
      
      if [ -f "$config_file" ]; then
        CONFIG_FOUND=true
        echo "✓ Config file found: $config_file"
        
        # Show relevant config section for debugging
        echo "Authentication section:"
        grep -A 10 "authentication:" "$config_file" | head -15 || echo "No authentication section found"
        
        # Method 1: Check for explicit "enabled: false" under anonymous section
        if grep -A 10 "authentication:" "$config_file" | grep -A 5 "anonymous:" | grep -q "enabled: false"; then
          ANONYMOUS_DISABLED=true
          echo "✓ Found: anonymous authentication explicitly disabled (enabled: false)"
          return 0
        fi
        
        # Method 2: Check if anonymous auth is explicitly enabled (fail case)
        if grep -A 10 "authentication:" "$config_file" | grep -A 5 "anonymous:" | grep -q "enabled: true"; then
          echo "FAIL: Kubelet anonymous authentication is explicitly enabled in $config_file"
          exit 1
        fi
        
        # Method 3: Check for anonymous section without explicit enabled setting
        if grep -A 10 "authentication:" "$config_file" | grep -q "anonymous:"; then
          echo "Found anonymous section without explicit enabled setting"
          # If anonymous section exists but no explicit enabled setting, check if it's secure by default
          ANONYMOUS_DISABLED=true
        fi
      else
        echo "✗ Config file not found: $config_file"
      fi
    }
    
    # Check kubelet config files - try both chroot and direct paths
    CONFIG_PATHS=(
      "/var/lib/kubelet/config.yaml"
      "/host/var/lib/kubelet/config.yaml"
      "/etc/kubernetes/kubelet/kubelet-config.yaml"
      "/host/etc/kubernetes/kubelet/kubelet-config.yaml"
      "/etc/kubernetes/kubelet.yaml"
      "/host/etc/kubernetes/kubelet.yaml"
      "/etc/kubernetes/kubelet/config.yaml"
      "/host/etc/kubernetes/kubelet/config.yaml"
    )
    
    for config_path in "${CONFIG_PATHS[@]}"; do
      if [ -f "$config_path" ]; then
        check_kubelet_config "$config_path"
        break
      fi
    done
    
    # Check kubelet process command line arguments
    echo "Checking kubelet process command line arguments..."
    
    # Try multiple ways to find kubelet process
    KUBELET_PID=""
    
    # Method 1: Check /proc directly (if accessible)
    if [ -d /proc ]; then
      for pid in $(ls /proc 2>/dev/null | grep '^[0-9]*$'); do
        if [ -f "/proc/$pid/cmdline" ] && grep -q kubelet "/proc/$pid/cmdline" 2>/dev/null; then
          KUBELET_PID=$pid
          echo "Found kubelet process: PID $pid"
          CMDLINE=$(tr '\0' ' ' < "/proc/$pid/cmdline" 2>/dev/null || echo "")
          echo "Command line: $CMDLINE"
          
          if echo "$CMDLINE" | grep -q "\--anonymous-auth=false"; then
            ANONYMOUS_DISABLED=true
            echo "✓ Found: --anonymous-auth=false in kubelet command line"
            break
          elif echo "$CMDLINE" | grep -q "\--anonymous-auth=true"; then
            echo "FAIL: Kubelet anonymous authentication is explicitly enabled via command line"
            exit 1
          fi
        fi
      done
    fi
    
    # Method 2: Check /host/proc if /proc didn't work
    if [ -z "$KUBELET_PID" ] && [ -d /host/proc ]; then
      for pid in $(ls /host/proc 2>/dev/null | grep '^[0-9]*$'); do
        if [ -f "/host/proc/$pid/cmdline" ] && grep -q kubelet "/host/proc/$pid/cmdline" 2>/dev/null; then
          KUBELET_PID=$pid
          echo "Found kubelet process in /host/proc: PID $pid"
          CMDLINE=$(tr '\0' ' ' < "/host/proc/$pid/cmdline" 2>/dev/null || echo "")
          echo "Command line: $CMDLINE"
          
          if echo "$CMDLINE" | grep -q "\--anonymous-auth=false"; then
            ANONYMOUS_DISABLED=true
            echo "✓ Found: --anonymous-auth=false in kubelet command line"
            break
          elif echo "$CMDLINE" | grep -q "\--anonymous-auth=true"; then
            echo "FAIL: Kubelet anonymous authentication is explicitly enabled via command line"
            exit 1
          fi
        fi
      done
    fi
    
    # Method 3: Use ps command if available
    if [ -z "$KUBELET_PID" ] && command -v ps >/dev/null 2>&1; then
      echo "Trying ps command to find kubelet..."
      PS_OUTPUT=$(ps aux 2>/dev/null | grep kubelet | grep -v grep || echo "")
      if [ -n "$PS_OUTPUT" ]; then
        echo "Kubelet process found via ps:"
        echo "$PS_OUTPUT"
        
        if echo "$PS_OUTPUT" | grep -q "\--anonymous-auth=false"; then
          ANONYMOUS_DISABLED=true
          echo "✓ Found: --anonymous-auth=false in kubelet command line (via ps)"
        elif echo "$PS_OUTPUT" | grep -q "\--anonymous-auth=true"; then
          echo "FAIL: Kubelet anonymous authentication is explicitly enabled via command line (via ps)"
          exit 1
        fi
      fi
    fi
    
    # Evaluate results
    echo ""
    echo "=== EVALUATION ==="
    echo "Config found: $CONFIG_FOUND"
    echo "Anonymous disabled: $ANONYMOUS_DISABLED"
    
    if [ "$ANONYMOUS_DISABLED" = "true" ]; then
      echo "PASS: Kubelet anonymous authentication is disabled"
      exit 0
    elif [ "$CONFIG_FOUND" = "true" ]; then
      # If we found config files but no explicit disabled setting, this might be a newer version
      # where anonymous auth is disabled by default
      echo "? Config found but no explicit anonymous auth setting"
      echo "? In Kubernetes 1.6+, anonymous authentication is disabled by default"
      echo "PASS: Kubelet anonymous authentication appears to be disabled by default"
      exit 0
    else
      echo "FAIL: No kubelet configuration found to verify anonymous authentication status"
      exit 1
    fi
