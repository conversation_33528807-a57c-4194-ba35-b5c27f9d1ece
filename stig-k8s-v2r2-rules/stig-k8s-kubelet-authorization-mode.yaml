apiVersion: compliance-operator.alauda.io/v1alpha1
kind: Rule
metadata:
  name: stig-k8s-kubelet-authorization-mode
  namespace: compliance-system
spec:
  id: "V-242392"
  title: "The Kubernetes Kubelet must have the authorization mode set to Webhook."
  description: |
    A user who has access to the Kubelet essentially has root access to the nodes contained within the Kubernetes Control Plane. To control access, users must be authenticated and authorized. By allowing anonymous connections, the controls put in place to secure the Kubelet can be bypassed.
    
    Setting the authorization mode to Webhook ensures that the Kubelet will use the SubjectAccessReview API to determine if the requestor has permission to make the requested API call. This allows for fine-grained access control to the Kubelet API.
    
    The AlwaysAllow mode allows all authenticated requests to the Kubelet API, which is a security risk.
  checkText: |
    On the Control Plane, run the command:
    ps -ef | grep kubelet
    
    Check the config file(s) referenced by the --config argument (example: --config=/etc/kubernetes/kubelet/kubelet-config.yaml):
    
    Change to the directory identified by --config argument and run the command:
    grep -i authorization *
    
    If the setting \"mode\" is not set to \"Webhook\", this is a finding.
    
    If the setting \"mode\" is not configured, check if there is an entry for \"--authorization-mode\" in the kubelet command.
    
    On the Control Plane, run the command:
    ps -ef | grep kubelet
    
    If \"--authorization-mode\" is not set to \"Webhook\", this is a finding.
  fixText: |
    On the Control Plane, run the command:
    ps -ef | grep kubelet
    
    Check the config file(s) referenced by the --config argument (example: --config=/etc/kubernetes/kubelet/kubelet-config.yaml):
    
    Edit the Kubernetes Kubelet config file:
    Set the value of \"mode\" to \"Webhook\".
    
    Reset Kubelet service using the following command:
    service kubelet restart
  severity: "high"
  checkType: "node"
  checkScript: |
    #!/bin/bash
    # Check if kubelet authorization mode is set to Webhook
    # STIG requirement: authorization mode should be set to Webhook
    
    WEBHOOK_MODE_CONFIGURED=false
    CONFIG_FOUND=false
    
    echo "Checking kubelet authorization mode configuration..."
    
    # Function to check kubelet config file for authorization mode setting
    check_kubelet_auth_config() {
      local config_file="$1"
      echo "Checking config file: $config_file"
      
      if [ -f "$config_file" ]; then
        CONFIG_FOUND=true
        echo "✓ Config file found: $config_file"
        
        # Show relevant config section for debugging
        echo "Authorization section:"
        grep -A 10 "authorization:" "$config_file" | head -15 || echo "No authorization section found"
        
        # Check for explicit "mode: Webhook" under authorization section
        if grep -A 10 "authorization:" "$config_file" | grep -q "mode: Webhook"; then
          WEBHOOK_MODE_CONFIGURED=true
          echo "✓ Found: authorization mode explicitly set to Webhook"
          return 0
        fi
        
        # Check if authorization mode is set to AlwaysAllow (fail case)
        if grep -A 10 "authorization:" "$config_file" | grep -q "mode: AlwaysAllow"; then
          echo "FAIL: Kubelet authorization mode is set to insecure value (AlwaysAllow) in $config_file"
          exit 1
        fi
        
        # Check for authorization section without explicit mode setting
        if grep -A 10 "authorization:" "$config_file" | grep -q "authorization:"; then
          echo "Found authorization section without explicit mode setting"
        fi
      else
        echo "✗ Config file not found: $config_file"
      fi
    }
    
    # Check kubelet config files - try both chroot and direct paths
    CONFIG_PATHS=(
      "/var/lib/kubelet/config.yaml"
      "/host/var/lib/kubelet/config.yaml"
      "/etc/kubernetes/kubelet/kubelet-config.yaml"
      "/host/etc/kubernetes/kubelet/kubelet-config.yaml"
      "/etc/kubernetes/kubelet.yaml"
      "/host/etc/kubernetes/kubelet.yaml"
      "/etc/kubernetes/kubelet/config.yaml"
      "/host/etc/kubernetes/kubelet/config.yaml"
    )
    
    for config_path in "${CONFIG_PATHS[@]}"; do
      if [ -f "$config_path" ]; then
        check_kubelet_auth_config "$config_path"
        break
      fi
    done
    
    # Check kubelet process command line arguments
    echo "Checking kubelet process command line arguments..."
    
    # Try multiple ways to find kubelet process
    KUBELET_PID=""
    
    # Method 1: Check /proc directly (if accessible)
    if [ -d /proc ]; then
      for pid in $(ls /proc 2>/dev/null | grep '^[0-9]*$'); do
        if [ -f "/proc/$pid/cmdline" ] && grep -q kubelet "/proc/$pid/cmdline" 2>/dev/null; then
          KUBELET_PID=$pid
          echo "Found kubelet process: PID $pid"
          CMDLINE=$(tr '\0' ' ' < "/proc/$pid/cmdline" 2>/dev/null || echo "")
          echo "Command line: $CMDLINE"
          
          if echo "$CMDLINE" | grep -q "\--authorization-mode=Webhook"; then
            WEBHOOK_MODE_CONFIGURED=true
            echo "✓ Found: --authorization-mode=Webhook in kubelet command line"
            break
          elif echo "$CMDLINE" | grep -q "\--authorization-mode=AlwaysAllow"; then
            echo "FAIL: Kubelet authorization mode is set to insecure value (AlwaysAllow) via command line"
            exit 1
          fi
        fi
      done
    fi
    
    # Method 2: Check /host/proc if /proc didn't work
    if [ -z "$KUBELET_PID" ] && [ -d /host/proc ]; then
      for pid in $(ls /host/proc 2>/dev/null | grep '^[0-9]*$'); do
        if [ -f "/host/proc/$pid/cmdline" ] && grep -q kubelet "/host/proc/$pid/cmdline" 2>/dev/null; then
          KUBELET_PID=$pid
          echo "Found kubelet process in /host/proc: PID $pid"
          CMDLINE=$(tr '\0' ' ' < "/host/proc/$pid/cmdline" 2>/dev/null || echo "")
          echo "Command line: $CMDLINE"
          
          if echo "$CMDLINE" | grep -q "\--authorization-mode=Webhook"; then
            WEBHOOK_MODE_CONFIGURED=true
            echo "✓ Found: --authorization-mode=Webhook in kubelet command line"
            break
          elif echo "$CMDLINE" | grep -q "\--authorization-mode=AlwaysAllow"; then
            echo "FAIL: Kubelet authorization mode is set to insecure value (AlwaysAllow) via command line"
            exit 1
          fi
        fi
      done
    fi
    
    # Method 3: Use ps command if available
    if [ -z "$KUBELET_PID" ] && command -v ps >/dev/null 2>&1; then
      echo "Trying ps command to find kubelet..."
      PS_OUTPUT=$(ps aux 2>/dev/null | grep kubelet | grep -v grep || echo "")
      if [ -n "$PS_OUTPUT" ]; then
        echo "Kubelet process found via ps:"
        echo "$PS_OUTPUT"
        
        if echo "$PS_OUTPUT" | grep -q "\--authorization-mode=Webhook"; then
          WEBHOOK_MODE_CONFIGURED=true
          echo "✓ Found: --authorization-mode=Webhook in kubelet command line (via ps)"
        elif echo "$PS_OUTPUT" | grep -q "\--authorization-mode=AlwaysAllow"; then
          echo "FAIL: Kubelet authorization mode is set to insecure value (AlwaysAllow) via command line (via ps)"
          exit 1
        fi
      fi
    fi
    
    # Evaluate results
    echo ""
    echo "=== EVALUATION ==="
    echo "Config found: $CONFIG_FOUND"
    echo "Webhook mode configured: $WEBHOOK_MODE_CONFIGURED"
    
    if [ "$WEBHOOK_MODE_CONFIGURED" = "true" ]; then
      echo "PASS: Kubelet authorization mode is set to Webhook"
      exit 0
    elif [ "$CONFIG_FOUND" = "true" ]; then
      # If we found config files but no explicit mode setting, check default behavior
      echo "? Config found but no explicit authorization mode setting"
      echo "? In Kubernetes 1.6+, authorization mode defaults to Webhook"
      echo "PASS: Kubelet authorization mode appears to be set to default (Webhook)"
      exit 0
    else
      echo "FAIL: No kubelet configuration found to verify authorization mode"
      exit 1
    fi
