apiVersion: compliance-operator.alauda.io/v1alpha1
kind: Rule
metadata:
  name: stig-k8s-controller-manager-service-account-private-key
  namespace: compliance-system
spec:
  id: "V-242381"
  title: "The Kubernetes Controller Manager must create unique service accounts for each work payload."
  description: |
    The Kubernetes Controller Manager is a background process that embeds core control loops regulating cluster system state through the API Server. Every process executed in a pod has an associated service account. By default, service accounts use the same credentials for authentication. Implementing the default settings poses a High risk to the Kubernetes Controller Manager. Setting the "--use-service-account-credential" value lowers the attack surface by generating unique service accounts settings for each controller instance.
  checkText: |
    Change to the /etc/kubernetes/manifests directory on the Kubernetes Control Plane. Run the command:
    grep -i use-service-account-credentials *
    
    If the setting "--use-service-account-credentials" is not configured in the Kubernetes Controller Manager manifest file or it is set to "false", this is a finding.
  fixText: |
    Edit the Kubernetes Controller Manager manifest file in the /etc/kubernetes/manifests directory on the Kubernetes Control Plane.
    
    Set the value of "--use-service-account-credentials" to "true".
  severity: "high"
  checkType: "platform"
  checkScript: |
    #!/bin/bash
    # Check if service account private key is configured
    # Check controller manager via kubectl and manifest files
    if command -v kubectl >/dev/null 2>&1; then
      if kubectl get pod -n kube-system -l component=kube-controller-manager -o yaml 2>/dev/null | grep -q "\--service-account-private-key-file"; then
        exit 0
      fi
    fi
    # Check controller manager manifest
    if [ -f /host/etc/kubernetes/manifests/kube-controller-manager.yaml ]; then
      if grep -q "\--service-account-private-key-file" /host/etc/kubernetes/manifests/kube-controller-manager.yaml; then
        exit 0
      fi
    fi
    # Check running process via host proc
    if [ -d /host/proc ]; then
      for pid in $(ls /host/proc | grep '^[0-9]*$'); do
        if [ -f "/host/proc/$pid/cmdline" ] && grep -q kube-controller-manager "/host/proc/$pid/cmdline" 2>/dev/null; then
          if tr '\0' ' ' < "/host/proc/$pid/cmdline" | grep -q "\--service-account-private-key-file"; then
            exit 0
          fi
        fi
      done
    fi
    # Additional check: verify the key file exists and has proper permissions
    if command -v kubectl >/dev/null 2>&1; then
      KEY_FILE=$(kubectl get pod -n kube-system -l component=kube-controller-manager -o yaml 2>/dev/null | grep "\--service-account-private-key-file" | sed 's/.*--service-account-private-key-file=\([^ ]*\).*/\1/')
      if [ -n "$KEY_FILE" ] && [ ! -f "/host$KEY_FILE" ]; then
        echo "FAIL: Service account private key file does not exist: $KEY_FILE"
        exit 1
      fi
    fi
    echo "FAIL: Service account private key file not configured (--service-account-private-key-file not found)"
    exit 1

# =============================================================================
# PLATFORM CONTROLS - SCHEDULER
# =============================================================================
