apiVersion: compliance-operator.alauda.io/v1alpha1
kind: Rule
metadata:
  name: stig-k8s-api-server-etcd-certfile
  namespace: compliance-system
spec:
  id: "V-242430"
  title: "Kubernetes etcd must have a certificate for communication."
  description: |
    Kubernetes stores configuration and state information in a distributed key-value store called etcd. Anyone who can write to etcd can effectively control the Kubernetes cluster. Even just reading the contents of etcd could easily provide helpful hints to a would-be attacker. Using authenticity protection, the communication can be protected against man-in-the-middle attacks/session hijacking and the insertion of false information into sessions.

    The communication session is protected by utilizing transport encryption protocols, such as TLS. TLS provides the Kubernetes API Server and etcd with a means to be able to authenticate sessions and encrypt traffic. 

    To enable encrypted communication for etcd, the parameter "--etcd-certfile" must be set. This parameter gives the location of the SSL certification file used to secure etcd communication.
  checkText: |
    Change to the /etc/kubernetes/manifests directory on the Kubernetes Control Plane. Run the command:
    grep -i etcd-certfile * 

    If the setting "--etcd-certfile" is not set in the Kubernetes API Server manifest file, this is a finding.
  fixText: |
    Edit the Kubernetes API Server manifest file in the /etc/kubernetes/manifests directory on the Kubernetes Control Plane. 

    Set the value of "--etcd-certfile" to the certificate to be used for communication with etcd.
  checkType: "platform"
  severity: "medium"
  checkScript: |
    #!/bin/bash
    # Check if API Server etcd certificate file is configured
    # STIG requirement: --etcd-certfile must be set
    
    echo "Checking API Server etcd certificate file configuration..."
    
    etcd_certfile_found=false
    
    # Method 1: Check via kubectl for API server pods
    if command -v kubectl >/dev/null 2>&1; then
      if kubectl get pod -n kube-system -l component=kube-apiserver -o yaml 2>/dev/null | grep -q "etcd-certfile"; then
        cert_path=$(kubectl get pod -n kube-system -l component=kube-apiserver -o yaml 2>/dev/null | grep "etcd-certfile" | head -1 | awk '{print $2}')
        if [ -n "$cert_path" ]; then
          echo "PASS: API Server etcd-certfile is configured: $cert_path (kubectl)"
          etcd_certfile_found=true
        fi
      fi
    fi
    
    # Method 2: Check API server manifest file
    if [ -f /host/etc/kubernetes/manifests/kube-apiserver.yaml ]; then
      if grep -q "etcd-certfile" /host/etc/kubernetes/manifests/kube-apiserver.yaml; then
        cert_path=$(grep "etcd-certfile" /host/etc/kubernetes/manifests/kube-apiserver.yaml | head -1 | awk '{print $2}')
        if [ -n "$cert_path" ]; then
          echo "PASS: API Server etcd-certfile is configured: $cert_path (manifest)"
          etcd_certfile_found=true
        fi
      fi
    fi
    
    # Method 3: Check API server process
    if pgrep kube-apiserver >/dev/null 2>&1; then
      if ps aux | grep kube-apiserver | grep -q "etcd-certfile"; then
        echo "PASS: API Server etcd-certfile is configured (process)"
        etcd_certfile_found=true
      fi
    fi
    
    if [ "$etcd_certfile_found" = true ]; then
      exit 0
    else
      echo "FAIL: API Server etcd-certfile is not configured"
      exit 1
    fi