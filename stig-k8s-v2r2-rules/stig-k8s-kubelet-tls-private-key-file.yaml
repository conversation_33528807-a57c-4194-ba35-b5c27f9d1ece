apiVersion: compliance-operator.alauda.io/v1alpha1
kind: Rule
metadata:
  name: stig-k8s-kubelet-tls-private-key-file
  namespace: compliance-system
spec:
  id: "V-242431"
  title: "Kubernetes Kubelet must enable tlsPrivateKeyFile for client authentication to secure service."
  description: |
    Kubernetes container and pod configuration are maintained by Kubelet. Kubelet agents register nodes with the API Server, mount volume storage, and perform health checks for containers and pods. Anyone who gains access to Kubelet agents can effectively control applications within the pods and containers. Using authenticity protection, the communication can be protected against man-in-the-middle attacks/session hijacking and the insertion of false information into sessions.
    
    The communication session is protected by utilizing transport encryption protocols, such as TLS. TLS provides the Kubernetes API Server with a means to be able to authenticate sessions and encrypt traffic.
    
    To enable encrypted communication for Kubelet, the parameter tlsPrivateKeyFile must be set. This parameter gives the location of the SSL Private Key file used to secure Kubelet communication.
  checkText: |
    On the Control Plane and Worker Nodes, run the command:
    ps -ef | grep kubelet
    
    Check the config file(s) referenced by the --config argument (example: --config=/etc/kubernetes/kubelet/kubelet-config.yaml):
    
    Change to the directory identified by --config argument and run the command:
    grep -i tlsprivatekeyfile *
    
    If the setting tlsPrivateKeyFile is not configured, this is a finding.
  fixText: |
    On the Control Plane and Worker Nodes, run the command:
    ps -ef | grep kubelet
    
    Check the config file(s) referenced by the --config argument (example: --config=/etc/kubernetes/kubelet/kubelet-config.yaml):
    
    Edit the Kubernetes Kubelet config file:
    Set the value of tlsPrivateKeyFile to the Approved Organizational Private Key.
    
    Reset Kubelet service using the following command:
    service kubelet restart
  severity: "medium"
  checkType: "node"
  checkScript: |
    #!/bin/bash
    # Check if kubelet TLS private key file is configured
    # Check kubelet config file first
    if [ -f /host/var/lib/kubelet/config.yaml ]; then
      if grep -q "tlsPrivateKeyFile:" /host/var/lib/kubelet/config.yaml; then
        KEY_FILE=$(grep "tlsPrivateKeyFile:" /host/var/lib/kubelet/config.yaml | sed 's/.*tlsPrivateKeyFile: *"\?\([^"]*\)"\?.*/\1/')
        if [ -n "$KEY_FILE" ] && [ -f "/host$KEY_FILE" ]; then
          # Check private key file permissions (should be 600 or more restrictive)
          KEY_PERMS=$(stat -c "%a" "/host$KEY_FILE" 2>/dev/null || echo "000")
          if [ "$KEY_PERMS" -gt 600 ]; then
            echo "FAIL: Kubelet TLS private key file has insecure permissions: $KEY_PERMS (should be 600 or more restrictive)"
            exit 1
          fi
          exit 0
        elif [ -n "$KEY_FILE" ] && [ ! -f "/host$KEY_FILE" ]; then
          echo "FAIL: Kubelet TLS private key file does not exist: $KEY_FILE"
          exit 1
        fi
      fi
    fi
    # Check alternative kubelet config locations
    for config_path in "/host/etc/kubernetes/kubelet/kubelet-config.yaml" "/host/etc/kubernetes/kubelet.yaml"; do
      if [ -f "$config_path" ]; then
        if grep -q "tlsPrivateKeyFile:" "$config_path"; then
          KEY_FILE=$(grep "tlsPrivateKeyFile:" "$config_path" | sed 's/.*tlsPrivateKeyFile: *"\?\([^"]*\)"\?.*/\1/')
          if [ -n "$KEY_FILE" ] && [ -f "/host$KEY_FILE" ]; then
            # Check private key file permissions
            KEY_PERMS=$(stat -c "%a" "/host$KEY_FILE" 2>/dev/null || echo "000")
            if [ "$KEY_PERMS" -gt 600 ]; then
              echo "FAIL: Kubelet TLS private key file has insecure permissions: $KEY_PERMS (should be 600 or more restrictive)"
              exit 1
            fi
            exit 0
          elif [ -n "$KEY_FILE" ] && [ ! -f "/host$KEY_FILE" ]; then
            echo "FAIL: Kubelet TLS private key file does not exist: $KEY_FILE"
            exit 1
          fi
        fi
      fi
    done
    # Check kubelet process via host proc filesystem
    if [ -d /host/proc ]; then
      for pid in $(ls /host/proc | grep '^[0-9]*$'); do
        if [ -f "/host/proc/$pid/cmdline" ] && grep -q kubelet "/host/proc/$pid/cmdline" 2>/dev/null; then
          if tr '\0' ' ' < "/host/proc/$pid/cmdline" | grep -q "\--tls-private-key-file"; then
            # Extract private key file path from command line
            KEY_FILE=$(tr '\0' ' ' < "/host/proc/$pid/cmdline" | grep -o "\--tls-private-key-file=[^ ]*" | sed 's/--tls-private-key-file=//')
            if [ -n "$KEY_FILE" ] && [ -f "/host$KEY_FILE" ]; then
              # Check private key file permissions
              KEY_PERMS=$(stat -c "%a" "/host$KEY_FILE" 2>/dev/null || echo "000")
              if [ "$KEY_PERMS" -gt 600 ]; then
                echo "FAIL: Kubelet TLS private key file has insecure permissions: $KEY_PERMS (should be 600 or more restrictive)"
                exit 1
              fi
              exit 0
            elif [ -n "$KEY_FILE" ] && [ ! -f "/host$KEY_FILE" ]; then
              echo "FAIL: Kubelet TLS private key file does not exist: $KEY_FILE"
              exit 1
            fi
          fi
        fi
      done
    fi
    echo "FAIL: Kubelet TLS private key file not configured (tlsPrivateKeyFile or --tls-private-key-file not found)"
    exit 1

# =============================================================================
# SECURITY CONTROLS - POD SECURITY
# =============================================================================
