
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Compliance Scan Report - stig-k8s-v2r2-node-scan</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Helvetica Neue', Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            background-color: #f8f9fa;
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
            min-height: 100vh;
        }
        
         
        .header {
            background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 50%, #ec4899 100%);
            color: white;
            padding: 40px 30px;
            text-align: center;
            position: relative;
            overflow: hidden;
        }
        
        .header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
            opacity: 0.3;
        }
        
        .header-content {
            position: relative;
            z-index: 1;
        }
        
        .header h1 {
            font-size: 3em;
            font-weight: 300;
            margin-bottom: 10px;
            text-shadow: 0 2px 4px rgba(0,0,0,0.3);
        }
        
        .header .subtitle {
            font-size: 1.2em;
            opacity: 0.9;
            font-weight: 300;
        }
        
         
        .overview {
            padding: 40px 30px;
            background: linear-gradient(to bottom, #fff 0%, #f8f9fa 100%);
        }
        
        .section-title {
            font-size: 2em;
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 30px;
            border-bottom: 3px solid #4f46e5;
            padding-bottom: 10px;
        }
        
        .overview-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 25px;
            margin-bottom: 30px;
        }
        
        .overview-card {
            background: white;
            border: 1px solid #e5e7eb;
            border-radius: 12px;
            padding: 25px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.05);
            transition: transform 0.2s, box-shadow 0.2s;
        }
        
        .overview-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 15px rgba(0,0,0,0.1);
        }
        
        .overview-card h3 {
            color: #4f46e5;
            font-size: 1.3em;
            margin-bottom: 15px;
            font-weight: 600;
        }
        
        .overview-card .info-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 0;
            border-bottom: 1px solid #f3f4f6;
        }
        
        .overview-card .info-item:last-child {
            border-bottom: none;
        }
        
        .info-label {
            font-weight: 500;
            color: #6b7280;
        }
        
        .info-value {
            font-weight: 600;
            color: #1f2937;
        }
        
         
        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 20px;
            margin-top: 30px;
        }
        
        .stat-card {
            background: white;
            border-radius: 12px;
            padding: 25px 20px;
            text-align: center;
            border: 2px solid transparent;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }
        
        .stat-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
        }
        
        .stat-card.total::before { background: #6b7280; }
        .stat-card.pass::before { background: #10b981; }
        .stat-card.fail::before { background: #ef4444; }
        .stat-card.error::before { background: #f59e0b; }
        .stat-card.manual::before { background: #8b5cf6; }
        .stat-card.na::before { background: #6b7280; }
        
        .stat-card h4 {
            font-size: 2.5em;
            font-weight: 700;
            margin-bottom: 5px;
        }
        
        .stat-card.total h4 { color: #6b7280; }
        .stat-card.pass h4 { color: #10b981; }
        .stat-card.fail h4 { color: #ef4444; }
        .stat-card.error h4 { color: #f59e0b; }
        .stat-card.manual h4 { color: #8b5cf6; }
        .stat-card.na h4 { color: #6b7280; }
        
        .stat-card p {
            color: #6b7280;
            font-weight: 500;
            text-transform: uppercase;
            font-size: 0.9em;
            letter-spacing: 0.5px;
        }
        
         
        .findings {
            padding: 40px 30px;
            background: #f8f9fa;
        }
        
        .finding-item {
            background: white;
            border-radius: 12px;
            margin-bottom: 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.08);
            overflow: hidden;
            transition: box-shadow 0.3s ease;
        }
        
        .finding-item:hover {
            box-shadow: 0 4px 20px rgba(0,0,0,0.12);
        }
        
        .finding-header {
            padding: 20px 25px;
            background: linear-gradient(135deg, #f8f9fa 0%, #e5e7eb 100%);
            border-bottom: 1px solid #e5e7eb;
            cursor: pointer;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .finding-header:hover {
            background: linear-gradient(135deg, #f3f4f6 0%, #d1d5db 100%);
        }
        
        .finding-title-section {
            flex: 1;
        }
        
        .finding-id {
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
            background: #4f46e5;
            color: white;
            padding: 6px 12px;
            border-radius: 6px;
            font-weight: 600;
            font-size: 0.9em;
            margin-bottom: 8px;
            display: inline-block;
        }
        
        .finding-title {
            font-size: 1.2em;
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 5px;
        }
        
        .finding-meta {
            display: flex;
            gap: 15px;
            align-items: center;
        }
        
        .severity-badge, .status-badge {
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 0.8em;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
        
        .severity-high { background: #fecaca; color: #991b1b; }
        .severity-medium { background: #fed7aa; color: #9a3412; }
        .severity-low { background: #bbf7d0; color: #166534; }
        
        .status-pass { background: #dcfce7; color: #166534; }
        .status-fail { background: #fecaca; color: #991b1b; }
        .status-error { background: #fed7aa; color: #9a3412; }
        .status-manual { background: #e0e7ff; color: #3730a3; }
        .status-not-applicable { background: #f3f4f6; color: #374151; }
        
        .expand-icon {
            font-size: 1.2em;
            transition: transform 0.3s ease;
            color: #6b7280;
        }
        
        .finding-header.expanded .expand-icon {
            transform: rotate(180deg);
        }
        
        .finding-content {
            display: none;
            padding: 25px;
            background: white;
        }
        
        .finding-content.expanded {
            display: block;
        }
        
        .finding-details {
            display: grid;
            gap: 20px;
        }
        
        .detail-section {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 20px;
            border-left: 4px solid #4f46e5;
        }
        
        .detail-section h4 {
            color: #4f46e5;
            font-weight: 600;
            margin-bottom: 10px;
            font-size: 1.1em;
        }
        
        .detail-section p {
            line-height: 1.6;
            color: #374151;
        }
        
        .detail-section pre {
            background: white;
            padding: 15px;
            border-radius: 6px;
            border: 1px solid #e5e7eb;
            overflow-x: auto;
            font-size: 0.9em;
            line-height: 1.4;
        }
        
         
        .node-results {
            margin-top: 20px;
        }
        
        .node-results-table {
            width: 100%;
            border-collapse: collapse;
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }
        
        .node-results-table th {
            background: #f8f9fa;
            padding: 15px;
            text-align: left;
            font-weight: 600;
            color: #374151;
            border-bottom: 2px solid #e5e7eb;
        }
        
        .node-results-table td {
            padding: 12px 15px;
            border-bottom: 1px solid #f3f4f6;
            vertical-align: top;
        }
        
        .node-results-table tr:hover {
            background: #f9fafb;
        }
        
        .node-name {
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
            font-weight: 600;
            color: #4f46e5;
        }
        
         
        .footer {
            background: #1f2937;
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .footer p {
            opacity: 0.8;
        }
        
         
        @media (max-width: 768px) {
            .container { margin: 0; }
            .header, .overview, .findings { padding: 20px; }
            .header h1 { font-size: 2em; }
            .overview-grid { grid-template-columns: 1fr; }
            .stats { grid-template-columns: repeat(2, 1fr); }
            .finding-meta { flex-direction: column; align-items: flex-start; gap: 10px; }
        }
        
         
        @media print {
            .finding-content { display: block !important; }
            .expand-icon { display: none; }
            .finding-header { cursor: default; }
        }
    </style>
    <script>
        function toggleFinding(element) {
            const content = element.nextElementSibling;
            const icon = element.querySelector('.expand-icon');
            
            if (content.classList.contains('expanded')) {
                content.classList.remove('expanded');
                element.classList.remove('expanded');
            } else {
                content.classList.add('expanded');
                element.classList.add('expanded');
            }
        }
        
        
        document.addEventListener('DOMContentLoaded', function() {
            const failedFindings = document.querySelectorAll('.finding-item');
            failedFindings.forEach(function(finding) {
                const statusBadge = finding.querySelector('.status-fail');
                if (statusBadge) {
                    const header = finding.querySelector('.finding-header');
                    const content = finding.querySelector('.finding-content');
                    content.classList.add('expanded');
                    header.classList.add('expanded');
                }
            });
        });
    </script>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="header-content">
                <h1>Compliance Scan Report</h1>
                <div class="subtitle">Security Technical Implementation Guide (STIG) Assessment</div>
            </div>
        </div>

        <div class="overview">
            <h2 class="section-title">Scan Overview</h2>
            <div class="overview-grid">
                <div class="overview-card">
                    <h3>Scan Information</h3>
                    <div class="info-item">
                        <span class="info-label">Scan Name</span>
                        <span class="info-value">stig-k8s-v2r2-node-scan</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">Profile</span>
                        <span class="info-value">stig-k8s-v2r2-node</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">Scan ID</span>
                        <span class="info-value">stig-k8s-v2r2-node-scan-6ccad4d4-20250626-112613-81bb</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">Scan Time</span>
                        <span class="info-value">2025-06-26 19:26:59 UTC</span>
                    </div>
                </div>
                
                <div class="overview-card">
                    <h3>Cluster Information</h3>
                    <div class="info-item">
                        <span class="info-label">Kubernetes Version</span>
                        <span class="info-value">v1.31.6</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">Node Count</span>
                        <span class="info-value">4</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">Container Runtime</span>
                        <span class="info-value">containerd://1.7.23-4</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">Master Node</span>
                        <span class="info-value">**************</span>
                    </div>
                </div>
                
                <div class="overview-card">
                    <h3>Node Details</h3>
                    
                    <div class="info-item">
                        <span class="info-label">************** (worker)</span>
                        <span class="info-value">**************</span>
                    </div>
                    
                    <div class="info-item">
                        <span class="info-label">************** (control-plane)</span>
                        <span class="info-value">**************</span>
                    </div>
                    
                    <div class="info-item">
                        <span class="info-label">*************** (worker)</span>
                        <span class="info-value">***************</span>
                    </div>
                    
                    <div class="info-item">
                        <span class="info-label">*************** (worker)</span>
                        <span class="info-value">***************</span>
                    </div>
                    
                </div>
            </div>

            <div class="stats">
                <div class="stat-card total">
                    <h4>25</h4>
                    <p>Total</p>
                </div>
                <div class="stat-card pass">
                    <h4>25</h4>
                    <p>Pass</p>
                </div>
                <div class="stat-card fail">
                    <h4>0</h4>
                    <p>Fail</p>
                </div>
                <div class="stat-card error">
                    <h4>0</h4>
                    <p>Error</p>
                </div>
                <div class="stat-card manual">
                    <h4>0</h4>
                    <p>Manual</p>
                </div>
                <div class="stat-card na">
                    <h4>0</h4>
                    <p>N/A</p>
                </div>
            </div>
        </div>

        <div class="findings">
            <h2 class="section-title">Findings - All (25)</h2>
            
                
                
                <div class="finding-item">
                    <div class="finding-header" onclick="toggleFinding(this)">
                        <div class="finding-title-section">
                            <div class="finding-id">V-242391</div>
                            <div class="finding-title">The Kubernetes Kubelet must have anonymous authentication disabled.</div>
                            <div class="finding-meta">
                                <span class="severity-badge severity-high">high</span>
                                <span class="status-badge status-pass-pass">PASS</span>
                                <span class="info-value">node</span>
                            </div>
                        </div>
                        <div class="expand-icon">▼</div>
                    </div>
                    <div class="finding-content">
                        <div class="finding-details">
                            <div class="detail-section">
                                <h4>Description</h4>
                                <p>A user who has access to the Kubelet essentially has root access to the nodes contained within the Kubernetes Control Plane. To control access, users must be authenticated and authorized. By allowing anonymous connections, the controls put in place to secure the Kubelet can be bypassed.

Setting anonymous authentication to \&#34;false\&#34; also disables unauthenticated requests from kubelets.

While there are instances where anonymous connections may be needed (e.g., health checks, metrics collection, etc.) and Role-Based Access Controls (RBAC) are in place to limit the anonymous user, this access must be evaluated against the risk of granting any user to the Kubelet.
</p>
                            </div>
                            
                            
                            <div class="detail-section">
                                <h4>Check Text</h4>
                                <pre>On the Control Plane, run the command:
ps -ef | grep kubelet

Check the config file(s) referenced by the --config argument (example: --config=/etc/kubernetes/kubelet/kubelet-config.yaml):

Change to the directory identified by --config argument and run the command:
grep -i anonymous *

If the setting \&#34;anonymous\&#34; is not set to \&#34;false\&#34;, this is a finding.

If the setting \&#34;anonymous\&#34; is not configured, check if there is an entry for \&#34;--anonymous-auth\&#34; in the kubelet command.

On the Control Plane, run the command:
ps -ef | grep kubelet

If \&#34;--anonymous-auth\&#34; is set to \&#34;true\&#34; or is not configured, this is a finding.
</pre>
                            </div>
                            
                            
                            
                            <div class="detail-section">
                                <h4>Fix Text</h4>
                                <pre>On the Control Plane, run the command:
ps -ef | grep kubelet

Check the config file(s) referenced by the --config argument (example: --config=/etc/kubernetes/kubelet/kubelet-config.yaml):

Edit the Kubernetes Kubelet config file:
Set the value of \&#34;anonymous\&#34; to \&#34;false\&#34;.

Reset Kubelet service using the following command:
service kubelet restart
</pre>
                            </div>
                            
                            
                            
                            <div class="detail-section">
                                <h4>Result Message</h4>
                                <p>PASS: Kubelet anonymous authentication is disabled</p>
                            </div>
                            
                            
                            
                                
                                <div class="detail-section">
                                    <h4>Node Results</h4>
                                    <table class="node-results-table">
                                        <thead>
                                            <tr>
                                                <th>Node Name</th>
                                                <th>Status</th>
                                                <th>Message</th>
                                                <th>Evidence</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            
                                            <tr>
                                                <td class="node-name">**************</td>
                                                <td><span class="status-badge status-pass">PASS</span></td>
                                                <td>PASS: Kubelet anonymous authentication is disabled</td>
                                                <td>Checking kubelet anonymous authentication configuration...
Checking config file: /var/lib/kubelet/config.yaml
✓ Config file found: /var/lib/kubelet/config.yaml
Authentication section:
authentication:
  anonymous:
    enabled: false
  webhook:
    cacheTTL: 0s
    enabled: true
  x509:
    clientCAFile: /etc/kubernetes/pki/ca.crt
authorization:
  mode: Webhook
  webhook:
✓ Found: anonymous authentication explicitly disabled (enabled: false)
Checking kubelet process command line arguments...
Found kubelet process: PID 11187
Command line: bash /kube-ovn/start-cniserver.sh --enable-mirror=false --mirror-iface=mirror0 --node-switch=join --encap-checksum=true --service-cluster-ip-range=********/16 --iface=eth0 --dpdk-tunnel-iface=br-phy --network-type=geneve --default-interface-name=eth0 --logtostderr=false --alsologtostderr=true --log_file=/var/log/kube-ovn/kube-ovn-cni.log --log_file_max_size=200 --enable-metrics=true --kubelet-dir=/var/lib/kubelet --enable-tproxy=false --ovs-vsctl-concurrency=100 --secure-serving=true --enable-ovn-ipsec=false --set-vxlan-tx-off=false --tls-min-version=TLS12 --tls-max-version=TLS12 --tls-cipher-suites=TLS_ECDHE_ECDSA_WITH_AES_256_GCM_SHA384 --tls-cipher-suites=TLS_ECDHE_RSA_WITH_AES_256_GCM_SHA384 --tls-cipher-suites=TLS_ECDHE_RSA_WITH_CHACHA20_POLY1305_SHA256 --tls-cipher-suites=TLS_ECDHE_ECDSA_WITH_CHACHA20_POLY1305_SHA256 
Found kubelet process: PID 11236
Command line: ./kube-ovn-daemon --ovs-socket=/run/openvswitch/db.sock --bind-socket=/run/openvswitch/kube-ovn-daemon.sock --enable-mirror=false --mirror-iface=mirror0 --node-switch=join --encap-checksum=true --service-cluster-ip-range=********/16 --iface=eth0 --dpdk-tunnel-iface=br-phy --network-type=geneve --default-interface-name=eth0 --logtostderr=false --alsologtostderr=true --log_file=/var/log/kube-ovn/kube-ovn-cni.log --log_file_max_size=200 --enable-metrics=true --kubelet-dir=/var/lib/kubelet --enable-tproxy=false --ovs-vsctl-concurrency=100 --secure-serving=true --enable-ovn-ipsec=false --set-vxlan-tx-off=false --tls-min-version=TLS12 --tls-max-version=TLS12 --tls-cipher-suites=TLS_ECDHE_ECDSA_WITH_AES_256_GCM_SHA384 --tls-cipher-suites=TLS_ECDHE_RSA_WITH_AES_256_GCM_SHA384 --tls-cipher-suites=TLS_ECDHE_RSA_WITH_CHACHA20_POLY1305_SHA256 --tls-cipher-suites=TLS_ECDHE_ECDSA_WITH_CHACHA20_POLY1305_SHA256 
Found kubelet process: PID 1940729
Command line: /bin/bash /usr/local/bin/unified-scanner.sh #!/bin/bash
# Check if kubelet anonymous authentication is disabled
# STIG requirement: anonymous authentication should be disabled

ANONYMOUS_DISABLED=false
CONFIG_FOUND=false

echo &#34;Checking kubelet anonymous authentication configuration...&#34;

# Function to check kubelet config file for anonymous auth setting
check_kubelet_config() {
  local config_file=&#34;$1&#34;
  echo &#34;Checking config file: $config_file&#34;
  
  if [ -f &#34;$config_file&#34; ]; then
    CONFIG_FOUND=true
    echo &#34;✓ Config file found: $config_file&#34;
    
    # Show relevant config section for debugging
    echo &#34;Authentication section:&#34;
    grep -A 10 &#34;authentication:&#34; &#34;$config_file&#34; | head -15 || echo &#34;No authentication section found&#34;
    
    # Method 1: Check for explicit &#34;enabled: false&#34; under anonymous section
    if grep -A 10 &#34;authentication:&#34; &#34;$config_file&#34; | grep -A 5 &#34;anonymous:&#34; | grep -q &#34;enabled: false&#34;; then
      ANONYMOUS_DISABLED=true
      echo &#34;✓ Found: anonymous authentication explicitly disabled (enabled: false)&#34;
      return 0
    fi
    
    # Method 2: Check if anonymous auth is explicitly enabled (fail case)
    if grep -A 10 &#34;authentication:&#34; &#34;$config_file&#34; | grep -A 5 &#34;anonymous:&#34; | grep -q &#34;enabled: true&#34;; then
      echo &#34;FAIL: Kubelet anonymous authentication is explicitly enabled in $config_file&#34;
      exit 1
    fi
    
    # Method 3: Check for anonymous section without explicit enabled setting
    if grep -A 10 &#34;authentication:&#34; &#34;$config_file&#34; | grep -q &#34;anonymous:&#34;; then
      echo &#34;Found anonymous section without explicit enabled setting&#34;
      # If anonymous section exists but no explicit enabled setting, check if it&#39;s secure by default
      ANONYMOUS_DISABLED=true
    fi
  else
    echo &#34;✗ Config file not found: $config_file&#34;
  fi
}

# Check kubelet config files - try both chroot and direct paths
CONFIG_PATHS=(
  &#34;/var/lib/kubelet/config.yaml&#34;
  &#34;/host/var/lib/kubelet/config.yaml&#34;
  &#34;/etc/kubernetes/kubelet/kubelet-config.yaml&#34;
  &#34;/host/etc/kubernetes/kubelet/kubelet-config.yaml&#34;
  &#34;/etc/kubernetes/kubelet.yaml&#34;
  &#34;/host/etc/kubernetes/kubelet.yaml&#34;
  &#34;/etc/kubernetes/kubelet/config.yaml&#34;
  &#34;/host/etc/kubernetes/kubelet/config.yaml&#34;
)

for config_path in &#34;${CONFIG_PATHS[@]}&#34;; do
  if [ -f &#34;$config_path&#34; ]; then
    check_kubelet_config &#34;$config_path&#34;
    break
  fi
done

# Check kubelet process command line arguments
echo &#34;Checking kubelet process command line arguments...&#34;

# Try multiple ways to find kubelet process
KUBELET_PID=&#34;&#34;

# Method 1: Check /proc directly (if accessible)
if [ -d /proc ]; then
  for pid in $(ls /proc 2&gt;/dev/null | grep &#39;^[0-9]*$&#39;); do
    if [ -f &#34;/proc/$pid/cmdline&#34; ] &amp;&amp; grep -q kubelet &#34;/proc/$pid/cmdline&#34; 2&gt;/dev/null; then
      KUBELET_PID=$pid
      echo &#34;Found kubelet process: PID $pid&#34;
      CMDLINE=$(tr &#39;\0&#39; &#39; &#39; &lt; &#34;/proc/$pid/cmdline&#34; 2&gt;/dev/null || echo &#34;&#34;)
      echo &#34;Command line: $CMDLINE&#34;
      
      if echo &#34;$CMDLINE&#34; | grep -q &#34;\--anonymous-auth=false&#34;; then
        ANONYMOUS_DISABLED=true
        echo &#34;✓ Found: --anonymous-auth=false in kubelet command line&#34;
        break
      elif echo &#34;$CMDLINE&#34; | grep -q &#34;\--anonymous-auth=true&#34;; then
        echo &#34;FAIL: Kubelet anonymous authentication is explicitly enabled via command line&#34;
        exit 1
      fi
    fi
  done
fi

# Method 2: Check /host/proc if /proc didn&#39;t work
if [ -z &#34;$KUBELET_PID&#34; ] &amp;&amp; [ -d /host/proc ]; then
  for pid in $(ls /host/proc 2&gt;/dev/null | grep &#39;^[0-9]*$&#39;); do
    if [ -f &#34;/host/proc/$pid/cmdline&#34; ] &amp;&amp; grep -q kubelet &#34;/host/proc/$pid/cmdline&#34; 2&gt;/dev/null; then
      KUBELET_PID=$pid
      echo &#34;Found kubelet process in /host/proc: PID $pid&#34;
      CMDLINE=$(tr &#39;\0&#39; &#39; &#39; &lt; &#34;/host/proc/$pid/cmdline&#34; 2&gt;/dev/null || echo &#34;&#34;)
      echo &#34;Command line: $CMDLINE&#34;
      
      if echo &#34;$CMDLINE&#34; | grep -q &#34;\--anonymous-auth=false&#34;; then
        ANONYMOUS_DISABLED=true
        echo &#34;✓ Found: --anonymous-auth=false in kubelet command line&#34;
        break
      elif echo &#34;$CMDLINE&#34; | grep -q &#34;\--anonymous-auth=true&#34;; then
        echo &#34;FAIL: Kubelet anonymous authentication is explicitly enabled via command line&#34;
        exit 1
      fi
    fi
  done
fi

# Method 3: Use ps command if available
if [ -z &#34;$KUBELET_PID&#34; ] &amp;&amp; command -v ps &gt;/dev/null 2&gt;&amp;1; then
  echo &#34;Trying ps command to find kubelet...&#34;
  PS_OUTPUT=$(ps aux 2&gt;/dev/null | grep kubelet | grep -v grep || echo &#34;&#34;)
  if [ -n &#34;$PS_OUTPUT&#34; ]; then
    echo &#34;Kubelet process found via ps:&#34;
    echo &#34;$PS_OUTPUT&#34;
    
    if echo &#34;$PS_OUTPUT&#34; | grep -q &#34;\--anonymous-auth=false&#34;; then
      ANONYMOUS_DISABLED=true
      echo &#34;✓ Found: --anonymous-auth=false in kubelet command line (via ps)&#34;
    elif echo &#34;$PS_OUTPUT&#34; | grep -q &#34;\--anonymous-auth=true&#34;; then
      echo &#34;FAIL: Kubelet anonymous authentication is explicitly enabled via command line (via ps)&#34;
      exit 1
    fi
  fi
fi

# Evaluate results
echo &#34;&#34;
echo &#34;=== EVALUATION ===&#34;
echo &#34;Config found: $CONFIG_FOUND&#34;
echo &#34;Anonymous disabled: $ANONYMOUS_DISABLED&#34;

if [ &#34;$ANONYMOUS_DISABLED&#34; = &#34;true&#34; ]; then
  echo &#34;PASS: Kubelet anonymous authentication is disabled&#34;
  exit 0
elif [ &#34;$CONFIG_FOUND&#34; = &#34;true&#34; ]; then
  # If we found config files but no explicit disabled setting, this might be a newer version
  # where anonymous auth is disabled by default
  echo &#34;? Config found but no explicit anonymous auth setting&#34;
  echo &#34;? In Kubernetes 1.6&#43;, anonymous authentication is disabled by default&#34;
  echo &#34;PASS: Kubelet anonymous authentication appears to be disabled by default&#34;
  exit 0
else
  echo &#34;FAIL: No kubelet configuration found to verify anonymous authentication status&#34;
  exit 1
fi
 
✓ Found: --anonymous-auth=false in kubelet command line

=== EVALUATION ===
Config found: true
Anonymous disabled: true
PASS: Kubelet anonymous authentication is disabled</td>
                                            </tr>
                                            
                                            <tr>
                                                <td class="node-name">***************</td>
                                                <td><span class="status-badge status-pass">PASS</span></td>
                                                <td>PASS: Kubelet anonymous authentication is disabled</td>
                                                <td>Checking kubelet anonymous authentication configuration...
Checking config file: /var/lib/kubelet/config.yaml
✓ Config file found: /var/lib/kubelet/config.yaml
Authentication section:
authentication:
  anonymous:
    enabled: false
  webhook:
    cacheTTL: 0s
    enabled: true
  x509:
    clientCAFile: /etc/kubernetes/pki/ca.crt
authorization:
  mode: Webhook
  webhook:
✓ Found: anonymous authentication explicitly disabled (enabled: false)
Checking kubelet process command line arguments...
Found kubelet process: PID 11027
Command line: bash /kube-ovn/start-cniserver.sh --enable-mirror=false --mirror-iface=mirror0 --node-switch=join --encap-checksum=true --service-cluster-ip-range=********/16 --iface=eth0 --dpdk-tunnel-iface=br-phy --network-type=geneve --default-interface-name=eth0 --logtostderr=false --alsologtostderr=true --log_file=/var/log/kube-ovn/kube-ovn-cni.log --log_file_max_size=200 --enable-metrics=true --kubelet-dir=/var/lib/kubelet --enable-tproxy=false --ovs-vsctl-concurrency=100 --secure-serving=true --enable-ovn-ipsec=false --set-vxlan-tx-off=false --tls-min-version=TLS12 --tls-max-version=TLS12 --tls-cipher-suites=TLS_ECDHE_ECDSA_WITH_AES_256_GCM_SHA384 --tls-cipher-suites=TLS_ECDHE_RSA_WITH_AES_256_GCM_SHA384 --tls-cipher-suites=TLS_ECDHE_RSA_WITH_CHACHA20_POLY1305_SHA256 --tls-cipher-suites=TLS_ECDHE_ECDSA_WITH_CHACHA20_POLY1305_SHA256 
Found kubelet process: PID 11052
Command line: ./kube-ovn-daemon --ovs-socket=/run/openvswitch/db.sock --bind-socket=/run/openvswitch/kube-ovn-daemon.sock --enable-mirror=false --mirror-iface=mirror0 --node-switch=join --encap-checksum=true --service-cluster-ip-range=********/16 --iface=eth0 --dpdk-tunnel-iface=br-phy --network-type=geneve --default-interface-name=eth0 --logtostderr=false --alsologtostderr=true --log_file=/var/log/kube-ovn/kube-ovn-cni.log --log_file_max_size=200 --enable-metrics=true --kubelet-dir=/var/lib/kubelet --enable-tproxy=false --ovs-vsctl-concurrency=100 --secure-serving=true --enable-ovn-ipsec=false --set-vxlan-tx-off=false --tls-min-version=TLS12 --tls-max-version=TLS12 --tls-cipher-suites=TLS_ECDHE_ECDSA_WITH_AES_256_GCM_SHA384 --tls-cipher-suites=TLS_ECDHE_RSA_WITH_AES_256_GCM_SHA384 --tls-cipher-suites=TLS_ECDHE_RSA_WITH_CHACHA20_POLY1305_SHA256 --tls-cipher-suites=TLS_ECDHE_ECDSA_WITH_CHACHA20_POLY1305_SHA256 
Found kubelet process: PID 2032104
Command line: /bin/bash /usr/local/bin/unified-scanner.sh #!/bin/bash
# Check if kubelet anonymous authentication is disabled
# STIG requirement: anonymous authentication should be disabled

ANONYMOUS_DISABLED=false
CONFIG_FOUND=false

echo &#34;Checking kubelet anonymous authentication configuration...&#34;

# Function to check kubelet config file for anonymous auth setting
check_kubelet_config() {
  local config_file=&#34;$1&#34;
  echo &#34;Checking config file: $config_file&#34;
  
  if [ -f &#34;$config_file&#34; ]; then
    CONFIG_FOUND=true
    echo &#34;✓ Config file found: $config_file&#34;
    
    # Show relevant config section for debugging
    echo &#34;Authentication section:&#34;
    grep -A 10 &#34;authentication:&#34; &#34;$config_file&#34; | head -15 || echo &#34;No authentication section found&#34;
    
    # Method 1: Check for explicit &#34;enabled: false&#34; under anonymous section
    if grep -A 10 &#34;authentication:&#34; &#34;$config_file&#34; | grep -A 5 &#34;anonymous:&#34; | grep -q &#34;enabled: false&#34;; then
      ANONYMOUS_DISABLED=true
      echo &#34;✓ Found: anonymous authentication explicitly disabled (enabled: false)&#34;
      return 0
    fi
    
    # Method 2: Check if anonymous auth is explicitly enabled (fail case)
    if grep -A 10 &#34;authentication:&#34; &#34;$config_file&#34; | grep -A 5 &#34;anonymous:&#34; | grep -q &#34;enabled: true&#34;; then
      echo &#34;FAIL: Kubelet anonymous authentication is explicitly enabled in $config_file&#34;
      exit 1
    fi
    
    # Method 3: Check for anonymous section without explicit enabled setting
    if grep -A 10 &#34;authentication:&#34; &#34;$config_file&#34; | grep -q &#34;anonymous:&#34;; then
      echo &#34;Found anonymous section without explicit enabled setting&#34;
      # If anonymous section exists but no explicit enabled setting, check if it&#39;s secure by default
      ANONYMOUS_DISABLED=true
    fi
  else
    echo &#34;✗ Config file not found: $config_file&#34;
  fi
}

# Check kubelet config files - try both chroot and direct paths
CONFIG_PATHS=(
  &#34;/var/lib/kubelet/config.yaml&#34;
  &#34;/host/var/lib/kubelet/config.yaml&#34;
  &#34;/etc/kubernetes/kubelet/kubelet-config.yaml&#34;
  &#34;/host/etc/kubernetes/kubelet/kubelet-config.yaml&#34;
  &#34;/etc/kubernetes/kubelet.yaml&#34;
  &#34;/host/etc/kubernetes/kubelet.yaml&#34;
  &#34;/etc/kubernetes/kubelet/config.yaml&#34;
  &#34;/host/etc/kubernetes/kubelet/config.yaml&#34;
)

for config_path in &#34;${CONFIG_PATHS[@]}&#34;; do
  if [ -f &#34;$config_path&#34; ]; then
    check_kubelet_config &#34;$config_path&#34;
    break
  fi
done

# Check kubelet process command line arguments
echo &#34;Checking kubelet process command line arguments...&#34;

# Try multiple ways to find kubelet process
KUBELET_PID=&#34;&#34;

# Method 1: Check /proc directly (if accessible)
if [ -d /proc ]; then
  for pid in $(ls /proc 2&gt;/dev/null | grep &#39;^[0-9]*$&#39;); do
    if [ -f &#34;/proc/$pid/cmdline&#34; ] &amp;&amp; grep -q kubelet &#34;/proc/$pid/cmdline&#34; 2&gt;/dev/null; then
      KUBELET_PID=$pid
      echo &#34;Found kubelet process: PID $pid&#34;
      CMDLINE=$(tr &#39;\0&#39; &#39; &#39; &lt; &#34;/proc/$pid/cmdline&#34; 2&gt;/dev/null || echo &#34;&#34;)
      echo &#34;Command line: $CMDLINE&#34;
      
      if echo &#34;$CMDLINE&#34; | grep -q &#34;\--anonymous-auth=false&#34;; then
        ANONYMOUS_DISABLED=true
        echo &#34;✓ Found: --anonymous-auth=false in kubelet command line&#34;
        break
      elif echo &#34;$CMDLINE&#34; | grep -q &#34;\--anonymous-auth=true&#34;; then
        echo &#34;FAIL: Kubelet anonymous authentication is explicitly enabled via command line&#34;
        exit 1
      fi
    fi
  done
fi

# Method 2: Check /host/proc if /proc didn&#39;t work
if [ -z &#34;$KUBELET_PID&#34; ] &amp;&amp; [ -d /host/proc ]; then
  for pid in $(ls /host/proc 2&gt;/dev/null | grep &#39;^[0-9]*$&#39;); do
    if [ -f &#34;/host/proc/$pid/cmdline&#34; ] &amp;&amp; grep -q kubelet &#34;/host/proc/$pid/cmdline&#34; 2&gt;/dev/null; then
      KUBELET_PID=$pid
      echo &#34;Found kubelet process in /host/proc: PID $pid&#34;
      CMDLINE=$(tr &#39;\0&#39; &#39; &#39; &lt; &#34;/host/proc/$pid/cmdline&#34; 2&gt;/dev/null || echo &#34;&#34;)
      echo &#34;Command line: $CMDLINE&#34;
      
      if echo &#34;$CMDLINE&#34; | grep -q &#34;\--anonymous-auth=false&#34;; then
        ANONYMOUS_DISABLED=true
        echo &#34;✓ Found: --anonymous-auth=false in kubelet command line&#34;
        break
      elif echo &#34;$CMDLINE&#34; | grep -q &#34;\--anonymous-auth=true&#34;; then
        echo &#34;FAIL: Kubelet anonymous authentication is explicitly enabled via command line&#34;
        exit 1
      fi
    fi
  done
fi

# Method 3: Use ps command if available
if [ -z &#34;$KUBELET_PID&#34; ] &amp;&amp; command -v ps &gt;/dev/null 2&gt;&amp;1; then
  echo &#34;Trying ps command to find kubelet...&#34;
  PS_OUTPUT=$(ps aux 2&gt;/dev/null | grep kubelet | grep -v grep || echo &#34;&#34;)
  if [ -n &#34;$PS_OUTPUT&#34; ]; then
    echo &#34;Kubelet process found via ps:&#34;
    echo &#34;$PS_OUTPUT&#34;
    
    if echo &#34;$PS_OUTPUT&#34; | grep -q &#34;\--anonymous-auth=false&#34;; then
      ANONYMOUS_DISABLED=true
      echo &#34;✓ Found: --anonymous-auth=false in kubelet command line (via ps)&#34;
    elif echo &#34;$PS_OUTPUT&#34; | grep -q &#34;\--anonymous-auth=true&#34;; then
      echo &#34;FAIL: Kubelet anonymous authentication is explicitly enabled via command line (via ps)&#34;
      exit 1
    fi
  fi
fi

# Evaluate results
echo &#34;&#34;
echo &#34;=== EVALUATION ===&#34;
echo &#34;Config found: $CONFIG_FOUND&#34;
echo &#34;Anonymous disabled: $ANONYMOUS_DISABLED&#34;

if [ &#34;$ANONYMOUS_DISABLED&#34; = &#34;true&#34; ]; then
  echo &#34;PASS: Kubelet anonymous authentication is disabled&#34;
  exit 0
elif [ &#34;$CONFIG_FOUND&#34; = &#34;true&#34; ]; then
  # If we found config files but no explicit disabled setting, this might be a newer version
  # where anonymous auth is disabled by default
  echo &#34;? Config found but no explicit anonymous auth setting&#34;
  echo &#34;? In Kubernetes 1.6&#43;, anonymous authentication is disabled by default&#34;
  echo &#34;PASS: Kubelet anonymous authentication appears to be disabled by default&#34;
  exit 0
else
  echo &#34;FAIL: No kubelet configuration found to verify anonymous authentication status&#34;
  exit 1
fi
 
✓ Found: --anonymous-auth=false in kubelet command line

=== EVALUATION ===
Config found: true
Anonymous disabled: true
PASS: Kubelet anonymous authentication is disabled</td>
                                            </tr>
                                            
                                            <tr>
                                                <td class="node-name">***************</td>
                                                <td><span class="status-badge status-pass">PASS</span></td>
                                                <td>PASS: Kubelet anonymous authentication is disabled</td>
                                                <td>Checking kubelet anonymous authentication configuration...
Checking config file: /var/lib/kubelet/config.yaml
✓ Config file found: /var/lib/kubelet/config.yaml
Authentication section:
authentication:
  anonymous:
    enabled: false
  webhook:
    cacheTTL: 0s
    enabled: true
  x509:
    clientCAFile: /etc/kubernetes/pki/ca.crt
authorization:
  mode: Webhook
  webhook:
✓ Found: anonymous authentication explicitly disabled (enabled: false)
Checking kubelet process command line arguments...
Found kubelet process: PID 16224
Command line: bash /kube-ovn/start-cniserver.sh --enable-mirror=false --mirror-iface=mirror0 --node-switch=join --encap-checksum=true --service-cluster-ip-range=********/16 --iface=eth0 --dpdk-tunnel-iface=br-phy --network-type=geneve --default-interface-name=eth0 --logtostderr=false --alsologtostderr=true --log_file=/var/log/kube-ovn/kube-ovn-cni.log --log_file_max_size=200 --enable-metrics=true --kubelet-dir=/var/lib/kubelet --enable-tproxy=false --ovs-vsctl-concurrency=100 --secure-serving=true --enable-ovn-ipsec=false --set-vxlan-tx-off=false --tls-min-version=TLS12 --tls-max-version=TLS12 --tls-cipher-suites=TLS_ECDHE_ECDSA_WITH_AES_256_GCM_SHA384 --tls-cipher-suites=TLS_ECDHE_RSA_WITH_AES_256_GCM_SHA384 --tls-cipher-suites=TLS_ECDHE_RSA_WITH_CHACHA20_POLY1305_SHA256 --tls-cipher-suites=TLS_ECDHE_ECDSA_WITH_CHACHA20_POLY1305_SHA256 
Found kubelet process: PID 16249
Command line: ./kube-ovn-daemon --ovs-socket=/run/openvswitch/db.sock --bind-socket=/run/openvswitch/kube-ovn-daemon.sock --enable-mirror=false --mirror-iface=mirror0 --node-switch=join --encap-checksum=true --service-cluster-ip-range=********/16 --iface=eth0 --dpdk-tunnel-iface=br-phy --network-type=geneve --default-interface-name=eth0 --logtostderr=false --alsologtostderr=true --log_file=/var/log/kube-ovn/kube-ovn-cni.log --log_file_max_size=200 --enable-metrics=true --kubelet-dir=/var/lib/kubelet --enable-tproxy=false --ovs-vsctl-concurrency=100 --secure-serving=true --enable-ovn-ipsec=false --set-vxlan-tx-off=false --tls-min-version=TLS12 --tls-max-version=TLS12 --tls-cipher-suites=TLS_ECDHE_ECDSA_WITH_AES_256_GCM_SHA384 --tls-cipher-suites=TLS_ECDHE_RSA_WITH_AES_256_GCM_SHA384 --tls-cipher-suites=TLS_ECDHE_RSA_WITH_CHACHA20_POLY1305_SHA256 --tls-cipher-suites=TLS_ECDHE_ECDSA_WITH_CHACHA20_POLY1305_SHA256 
Found kubelet process: PID 3332157
Command line: /bin/bash /usr/local/bin/unified-scanner.sh #!/bin/bash
# Check if kubelet read-only port is disabled
# STIG requirement: read-only port should be disabled (set to 0)

READONLY_PORT_DISABLED=false
CONFIG_FOUND=false

echo &#34;Checking kubelet read-only port configuration...&#34;

# Function to check kubelet config file for read-only port setting
check_kubelet_readonly_config() {
  local config_file=&#34;$1&#34;
  if [ -f &#34;$config_file&#34; ]; then
    CONFIG_FOUND=true
    echo &#34;✓ Config file found: $config_file&#34;
    
    # Check if readOnlyPort is explicitly set to 0
    if grep -q &#34;readOnlyPort: 0&#34; &#34;$config_file&#34;; then
      echo &#34;✓ Found: readOnlyPort explicitly disabled (readOnlyPort: 0)&#34;
      READONLY_PORT_DISABLED=true
      return 0
    fi
    
    # Check if readOnlyPort is set to a non-zero value
    if grep -q &#34;readOnlyPort:&#34; &#34;$config_file&#34;; then
      local port_value=$(grep &#34;readOnlyPort:&#34; &#34;$config_file&#34; | sed &#39;s/.*readOnlyPort: *\([0-9]*\).*/\1/&#39;)
      if [ &#34;$port_value&#34; != &#34;0&#34; ] 2&gt;/dev/null; then
        echo &#34;✗ Found: readOnlyPort enabled with value: $port_value&#34;
        return 1
      fi
    else
      echo &#34;ℹ No explicit readOnlyPort setting found (using default behavior)&#34;
    fi
  fi
  return 0
}

# Function to check kubelet process for read-only port parameter
check_kubelet_readonly_process() {
  if [ -d /host/proc ]; then
    for pid in $(ls /host/proc | grep &#39;^[0-9]*$&#39;); do
      if [ -f &#34;/host/proc/$pid/cmdline&#34; ] &amp;&amp; grep -q kubelet &#34;/host/proc/$pid/cmdline&#34; 2&gt;/dev/null; then
        echo &#34;✓ Found kubelet process: PID $pid&#34;
        
        # Check for explicit --read-only-port=0
        if tr &#39;\0&#39; &#39; &#39; &lt; &#34;/host/proc/$pid/cmdline&#34; | grep -q -- &#34;--read-only-port=0&#34;; then
          echo &#34;✓ Found: --read-only-port=0 in kubelet command line&#34;
          READONLY_PORT_DISABLED=true
          return 0
        fi
        
        # Check for non-zero read-only port
        if tr &#39;\0&#39; &#39; &#39; &lt; &#34;/host/proc/$pid/cmdline&#34; | grep -q -- &#34;--read-only-port=[1-9]&#34;; then
          echo &#34;✗ Found: read-only port enabled via command line&#34;
          return 1
        fi
        
        echo &#34;ℹ No explicit --read-only-port parameter found (using default)&#34;
      fi
    done
  fi
  return 0
}

# Function to test if read-only port is actually accessible
test_readonly_port_access() {
  # Test common read-only port (10255)
  if command -v netstat &gt;/dev/null 2&gt;&amp;1; then
    if netstat -tlnp 2&gt;/dev/null | grep -q &#34;:10255&#34;; then
      echo &#34;✗ Read-only port 10255 is listening&#34;
      return 1
    else
      echo &#34;✓ Read-only port 10255 is not listening&#34;
      READONLY_PORT_DISABLED=true
      return 0
    fi
  fi
  
  # Alternative check using /proc/net/tcp
  if [ -f /host/proc/net/tcp ]; then
    # 10255 in hex is 280F
    if grep -q &#34;:280F &#34; /host/proc/net/tcp; then
      echo &#34;✗ Read-only port 10255 is listening (found in /proc/net/tcp)&#34;
      return 1
    else
      echo &#34;✓ Read-only port 10255 is not listening (checked /proc/net/tcp)&#34;
      READONLY_PORT_DISABLED=true
      return 0
    fi
  fi
  
  return 0
}

# Check kubelet configuration files
for config_path in &#34;/host/var/lib/kubelet/config.yaml&#34; &#34;/host/etc/kubernetes/kubelet/kubelet-config.yaml&#34; &#34;/host/etc/kubernetes/kubelet.yaml&#34;; do
  if ! check_kubelet_readonly_config &#34;$config_path&#34;; then
    echo &#34;FAIL: Read-only port is enabled in configuration&#34;
    exit 1
  fi
done

# Check kubelet process parameters
if ! check_kubelet_readonly_process; then
  echo &#34;FAIL: Read-only port is enabled via command line&#34;
  exit 1
fi

# Test actual port accessibility
if ! test_readonly_port_access; then
  echo &#34;FAIL: Read-only port is accessible&#34;
  exit 1
fi

# Final evaluation
echo &#34;&#34;
echo &#34;=== EVALUATION ===&#34;
echo &#34;Config found: $CONFIG_FOUND&#34;
echo &#34;Read-only port disabled: $READONLY_PORT_DISABLED&#34;

if [ &#34;$READONLY_PORT_DISABLED&#34; = &#34;true&#34; ]; then
  echo &#34;✓ PASS: Kubelet read-only port is disabled&#34;
  exit 0
elif [ &#34;$CONFIG_FOUND&#34; = &#34;true&#34; ]; then
  # If config was found but no explicit setting, check Kubernetes version behavior
  echo &#34;ℹ No explicit readOnlyPort setting found&#34;
  echo &#34;ℹ Kubernetes 1.20&#43; disables read-only port by default&#34;
  echo &#34;✓ PASS: Read-only port disabled by default (Kubernetes security improvement)&#34;
  exit 0
else
  echo &#34;FAIL: No kubelet configuration found to verify read-only port status&#34;
  exit 1
fi
 
Found kubelet process: PID 3332159
Command line: /bin/bash /usr/local/bin/unified-scanner.sh #!/bin/bash
# Check if kubelet anonymous authentication is disabled
# STIG requirement: anonymous authentication should be disabled

ANONYMOUS_DISABLED=false
CONFIG_FOUND=false

echo &#34;Checking kubelet anonymous authentication configuration...&#34;

# Function to check kubelet config file for anonymous auth setting
check_kubelet_config() {
  local config_file=&#34;$1&#34;
  echo &#34;Checking config file: $config_file&#34;
  
  if [ -f &#34;$config_file&#34; ]; then
    CONFIG_FOUND=true
    echo &#34;✓ Config file found: $config_file&#34;
    
    # Show relevant config section for debugging
    echo &#34;Authentication section:&#34;
    grep -A 10 &#34;authentication:&#34; &#34;$config_file&#34; | head -15 || echo &#34;No authentication section found&#34;
    
    # Method 1: Check for explicit &#34;enabled: false&#34; under anonymous section
    if grep -A 10 &#34;authentication:&#34; &#34;$config_file&#34; | grep -A 5 &#34;anonymous:&#34; | grep -q &#34;enabled: false&#34;; then
      ANONYMOUS_DISABLED=true
      echo &#34;✓ Found: anonymous authentication explicitly disabled (enabled: false)&#34;
      return 0
    fi
    
    # Method 2: Check if anonymous auth is explicitly enabled (fail case)
    if grep -A 10 &#34;authentication:&#34; &#34;$config_file&#34; | grep -A 5 &#34;anonymous:&#34; | grep -q &#34;enabled: true&#34;; then
      echo &#34;FAIL: Kubelet anonymous authentication is explicitly enabled in $config_file&#34;
      exit 1
    fi
    
    # Method 3: Check for anonymous section without explicit enabled setting
    if grep -A 10 &#34;authentication:&#34; &#34;$config_file&#34; | grep -q &#34;anonymous:&#34;; then
      echo &#34;Found anonymous section without explicit enabled setting&#34;
      # If anonymous section exists but no explicit enabled setting, check if it&#39;s secure by default
      ANONYMOUS_DISABLED=true
    fi
  else
    echo &#34;✗ Config file not found: $config_file&#34;
  fi
}

# Check kubelet config files - try both chroot and direct paths
CONFIG_PATHS=(
  &#34;/var/lib/kubelet/config.yaml&#34;
  &#34;/host/var/lib/kubelet/config.yaml&#34;
  &#34;/etc/kubernetes/kubelet/kubelet-config.yaml&#34;
  &#34;/host/etc/kubernetes/kubelet/kubelet-config.yaml&#34;
  &#34;/etc/kubernetes/kubelet.yaml&#34;
  &#34;/host/etc/kubernetes/kubelet.yaml&#34;
  &#34;/etc/kubernetes/kubelet/config.yaml&#34;
  &#34;/host/etc/kubernetes/kubelet/config.yaml&#34;
)

for config_path in &#34;${CONFIG_PATHS[@]}&#34;; do
  if [ -f &#34;$config_path&#34; ]; then
    check_kubelet_config &#34;$config_path&#34;
    break
  fi
done

# Check kubelet process command line arguments
echo &#34;Checking kubelet process command line arguments...&#34;

# Try multiple ways to find kubelet process
KUBELET_PID=&#34;&#34;

# Method 1: Check /proc directly (if accessible)
if [ -d /proc ]; then
  for pid in $(ls /proc 2&gt;/dev/null | grep &#39;^[0-9]*$&#39;); do
    if [ -f &#34;/proc/$pid/cmdline&#34; ] &amp;&amp; grep -q kubelet &#34;/proc/$pid/cmdline&#34; 2&gt;/dev/null; then
      KUBELET_PID=$pid
      echo &#34;Found kubelet process: PID $pid&#34;
      CMDLINE=$(tr &#39;\0&#39; &#39; &#39; &lt; &#34;/proc/$pid/cmdline&#34; 2&gt;/dev/null || echo &#34;&#34;)
      echo &#34;Command line: $CMDLINE&#34;
      
      if echo &#34;$CMDLINE&#34; | grep -q &#34;\--anonymous-auth=false&#34;; then
        ANONYMOUS_DISABLED=true
        echo &#34;✓ Found: --anonymous-auth=false in kubelet command line&#34;
        break
      elif echo &#34;$CMDLINE&#34; | grep -q &#34;\--anonymous-auth=true&#34;; then
        echo &#34;FAIL: Kubelet anonymous authentication is explicitly enabled via command line&#34;
        exit 1
      fi
    fi
  done
fi

# Method 2: Check /host/proc if /proc didn&#39;t work
if [ -z &#34;$KUBELET_PID&#34; ] &amp;&amp; [ -d /host/proc ]; then
  for pid in $(ls /host/proc 2&gt;/dev/null | grep &#39;^[0-9]*$&#39;); do
    if [ -f &#34;/host/proc/$pid/cmdline&#34; ] &amp;&amp; grep -q kubelet &#34;/host/proc/$pid/cmdline&#34; 2&gt;/dev/null; then
      KUBELET_PID=$pid
      echo &#34;Found kubelet process in /host/proc: PID $pid&#34;
      CMDLINE=$(tr &#39;\0&#39; &#39; &#39; &lt; &#34;/host/proc/$pid/cmdline&#34; 2&gt;/dev/null || echo &#34;&#34;)
      echo &#34;Command line: $CMDLINE&#34;
      
      if echo &#34;$CMDLINE&#34; | grep -q &#34;\--anonymous-auth=false&#34;; then
        ANONYMOUS_DISABLED=true
        echo &#34;✓ Found: --anonymous-auth=false in kubelet command line&#34;
        break
      elif echo &#34;$CMDLINE&#34; | grep -q &#34;\--anonymous-auth=true&#34;; then
        echo &#34;FAIL: Kubelet anonymous authentication is explicitly enabled via command line&#34;
        exit 1
      fi
    fi
  done
fi

# Method 3: Use ps command if available
if [ -z &#34;$KUBELET_PID&#34; ] &amp;&amp; command -v ps &gt;/dev/null 2&gt;&amp;1; then
  echo &#34;Trying ps command to find kubelet...&#34;
  PS_OUTPUT=$(ps aux 2&gt;/dev/null | grep kubelet | grep -v grep || echo &#34;&#34;)
  if [ -n &#34;$PS_OUTPUT&#34; ]; then
    echo &#34;Kubelet process found via ps:&#34;
    echo &#34;$PS_OUTPUT&#34;
    
    if echo &#34;$PS_OUTPUT&#34; | grep -q &#34;\--anonymous-auth=false&#34;; then
      ANONYMOUS_DISABLED=true
      echo &#34;✓ Found: --anonymous-auth=false in kubelet command line (via ps)&#34;
    elif echo &#34;$PS_OUTPUT&#34; | grep -q &#34;\--anonymous-auth=true&#34;; then
      echo &#34;FAIL: Kubelet anonymous authentication is explicitly enabled via command line (via ps)&#34;
      exit 1
    fi
  fi
fi

# Evaluate results
echo &#34;&#34;
echo &#34;=== EVALUATION ===&#34;
echo &#34;Config found: $CONFIG_FOUND&#34;
echo &#34;Anonymous disabled: $ANONYMOUS_DISABLED&#34;

if [ &#34;$ANONYMOUS_DISABLED&#34; = &#34;true&#34; ]; then
  echo &#34;PASS: Kubelet anonymous authentication is disabled&#34;
  exit 0
elif [ &#34;$CONFIG_FOUND&#34; = &#34;true&#34; ]; then
  # If we found config files but no explicit disabled setting, this might be a newer version
  # where anonymous auth is disabled by default
  echo &#34;? Config found but no explicit anonymous auth setting&#34;
  echo &#34;? In Kubernetes 1.6&#43;, anonymous authentication is disabled by default&#34;
  echo &#34;PASS: Kubelet anonymous authentication appears to be disabled by default&#34;
  exit 0
else
  echo &#34;FAIL: No kubelet configuration found to verify anonymous authentication status&#34;
  exit 1
fi
 
✓ Found: --anonymous-auth=false in kubelet command line

=== EVALUATION ===
Config found: true
Anonymous disabled: true
PASS: Kubelet anonymous authentication is disabled</td>
                                            </tr>
                                            
                                            <tr>
                                                <td class="node-name">**************</td>
                                                <td><span class="status-badge status-pass">PASS</span></td>
                                                <td>PASS: Kubelet anonymous authentication is disabled</td>
                                                <td>Checking kubelet anonymous authentication configuration...
Checking config file: /var/lib/kubelet/config.yaml
✓ Config file found: /var/lib/kubelet/config.yaml
Authentication section:
authentication:
  anonymous:
    enabled: false
  webhook:
    cacheTTL: 0s
    enabled: true
  x509:
    clientCAFile: /etc/kubernetes/pki/ca.crt
authorization:
  mode: Webhook
  webhook:
✓ Found: anonymous authentication explicitly disabled (enabled: false)
Checking kubelet process command line arguments...
Found kubelet process: PID 19527
Command line: kube-apiserver --admission-control-config-file=/etc/kubernetes/admission/psa-config.yaml --advertise-address=************** --allow-privileged=true --audit-log-format=json --audit-log-maxage=30 --audit-log-maxbackup=10 --audit-log-maxsize=200 --audit-log-mode=batch --audit-log-path=/etc/kubernetes/audit/audit.log --audit-policy-file=/etc/kubernetes/audit/policy.yaml --authorization-mode=Node,RBAC --client-ca-file=/etc/kubernetes/pki/ca.crt --default-not-ready-toleration-seconds=30 --default-unreachable-toleration-seconds=30 --enable-admission-plugins=NodeRestriction --enable-bootstrap-token-auth=true --encryption-provider-config=/etc/kubernetes/encryption-provider.conf --etcd-cafile=/etc/kubernetes/pki/etcd/ca.crt --etcd-certfile=/etc/kubernetes/pki/apiserver-etcd-client.crt --etcd-keyfile=/etc/kubernetes/pki/apiserver-etcd-client.key --etcd-servers=https://127.0.0.1:2379 --kubelet-certificate-authority=/etc/kubernetes/pki/ca.crt --kubelet-client-certificate=/etc/kubernetes/pki/apiserver-kubelet-client.crt --kubelet-client-key=/etc/kubernetes/pki/apiserver-kubelet-client.key --kubelet-preferred-address-types=InternalIP,ExternalIP,Hostname --profiling=false --proxy-client-cert-file=/etc/kubernetes/pki/front-proxy-client.crt --proxy-client-key-file=/etc/kubernetes/pki/front-proxy-client.key --requestheader-allowed-names=front-proxy-client --requestheader-client-ca-file=/etc/kubernetes/pki/front-proxy-ca.crt --requestheader-extra-headers-prefix=X-Remote-Extra- --requestheader-group-headers=X-Remote-Group --requestheader-username-headers=X-Remote-User --secure-port=6443 --service-account-issuer=https://kubernetes.default.svc.cluster.local --service-account-key-file=/etc/kubernetes/pki/sa.pub --service-account-signing-key-file=/etc/kubernetes/pki/sa.key --service-cluster-ip-range=********/16 --tls-cert-file=/etc/kubernetes/pki/apiserver.crt --tls-cipher-suites=TLS_ECDHE_ECDSA_WITH_AES_128_GCM_SHA256,TLS_ECDHE_RSA_WITH_AES_128_GCM_SHA256,TLS_ECDHE_ECDSA_WITH_CHACHA20_POLY1305,TLS_ECDHE_RSA_WITH_AES_256_GCM_SHA384,TLS_ECDHE_RSA_WITH_CHACHA20_POLY1305,TLS_ECDHE_ECDSA_WITH_AES_256_GCM_SHA384 --tls-min-version=VersionTLS12 --tls-private-key-file=/etc/kubernetes/pki/apiserver.key 
Found kubelet process: PID 21139
Command line: /usr/bin/kubelet --bootstrap-kubeconfig=/etc/kubernetes/bootstrap-kubelet.conf --kubeconfig=/etc/kubernetes/kubelet.conf --config=/var/lib/kubelet/config.yaml --address=************** --container-log-max-files=2 --container-log-max-size=100Mi --container-runtime-endpoint=unix:///run/containerd/containerd.sock --hostname-override=************** --kube-reserved=cpu=100m,memory=902Mi --node-ip=************** --node-labels=platform.tkestack.io/machine-ip=************** --node-status-update-frequency=8s --pod-infra-container-image=registry.alauda.cn:60070/tkestack/pause:3.10 --pod-max-pids=16384 --protect-kernel-defaults=true --streaming-connection-idle-timeout=5m0s --system-reserved=cpu=100m,memory=902Mi --tls-cipher-suites=TLS_ECDHE_ECDSA_WITH_AES_128_GCM_SHA256,TLS_ECDHE_RSA_WITH_AES_128_GCM_SHA256,TLS_ECDHE_ECDSA_WITH_CHACHA20_POLY1305,TLS_ECDHE_RSA_WITH_AES_256_GCM_SHA384,TLS_ECDHE_RSA_WITH_CHACHA20_POLY1305,TLS_ECDHE_ECDSA_WITH_AES_256_GCM_SHA384 --tls-min-version=VersionTLS12 
Found kubelet process: PID 25363
Command line: bash /kube-ovn/start-cniserver.sh --enable-mirror=false --mirror-iface=mirror0 --node-switch=join --encap-checksum=true --service-cluster-ip-range=********/16 --iface=eth0 --dpdk-tunnel-iface=br-phy --network-type=geneve --default-interface-name=eth0 --logtostderr=false --alsologtostderr=true --log_file=/var/log/kube-ovn/kube-ovn-cni.log --log_file_max_size=200 --enable-metrics=true --kubelet-dir=/var/lib/kubelet --enable-tproxy=false --ovs-vsctl-concurrency=100 --secure-serving=true --enable-ovn-ipsec=false --set-vxlan-tx-off=false --tls-min-version=TLS12 --tls-max-version=TLS12 --tls-cipher-suites=TLS_ECDHE_ECDSA_WITH_AES_256_GCM_SHA384 --tls-cipher-suites=TLS_ECDHE_RSA_WITH_AES_256_GCM_SHA384 --tls-cipher-suites=TLS_ECDHE_RSA_WITH_CHACHA20_POLY1305_SHA256 --tls-cipher-suites=TLS_ECDHE_ECDSA_WITH_CHACHA20_POLY1305_SHA256 
Found kubelet process: PID 25396
Command line: ./kube-ovn-daemon --ovs-socket=/run/openvswitch/db.sock --bind-socket=/run/openvswitch/kube-ovn-daemon.sock --enable-mirror=false --mirror-iface=mirror0 --node-switch=join --encap-checksum=true --service-cluster-ip-range=********/16 --iface=eth0 --dpdk-tunnel-iface=br-phy --network-type=geneve --default-interface-name=eth0 --logtostderr=false --alsologtostderr=true --log_file=/var/log/kube-ovn/kube-ovn-cni.log --log_file_max_size=200 --enable-metrics=true --kubelet-dir=/var/lib/kubelet --enable-tproxy=false --ovs-vsctl-concurrency=100 --secure-serving=true --enable-ovn-ipsec=false --set-vxlan-tx-off=false --tls-min-version=TLS12 --tls-max-version=TLS12 --tls-cipher-suites=TLS_ECDHE_ECDSA_WITH_AES_256_GCM_SHA384 --tls-cipher-suites=TLS_ECDHE_RSA_WITH_AES_256_GCM_SHA384 --tls-cipher-suites=TLS_ECDHE_RSA_WITH_CHACHA20_POLY1305_SHA256 --tls-cipher-suites=TLS_ECDHE_ECDSA_WITH_CHACHA20_POLY1305_SHA256 
Found kubelet process: PID 3046891
Command line: /bin/bash /usr/local/bin/unified-scanner.sh #!/bin/bash
# Check if kubelet anonymous authentication is disabled
# STIG requirement: anonymous authentication should be disabled

ANONYMOUS_DISABLED=false
CONFIG_FOUND=false

echo &#34;Checking kubelet anonymous authentication configuration...&#34;

# Function to check kubelet config file for anonymous auth setting
check_kubelet_config() {
  local config_file=&#34;$1&#34;
  echo &#34;Checking config file: $config_file&#34;
  
  if [ -f &#34;$config_file&#34; ]; then
    CONFIG_FOUND=true
    echo &#34;✓ Config file found: $config_file&#34;
    
    # Show relevant config section for debugging
    echo &#34;Authentication section:&#34;
    grep -A 10 &#34;authentication:&#34; &#34;$config_file&#34; | head -15 || echo &#34;No authentication section found&#34;
    
    # Method 1: Check for explicit &#34;enabled: false&#34; under anonymous section
    if grep -A 10 &#34;authentication:&#34; &#34;$config_file&#34; | grep -A 5 &#34;anonymous:&#34; | grep -q &#34;enabled: false&#34;; then
      ANONYMOUS_DISABLED=true
      echo &#34;✓ Found: anonymous authentication explicitly disabled (enabled: false)&#34;
      return 0
    fi
    
    # Method 2: Check if anonymous auth is explicitly enabled (fail case)
    if grep -A 10 &#34;authentication:&#34; &#34;$config_file&#34; | grep -A 5 &#34;anonymous:&#34; | grep -q &#34;enabled: true&#34;; then
      echo &#34;FAIL: Kubelet anonymous authentication is explicitly enabled in $config_file&#34;
      exit 1
    fi
    
    # Method 3: Check for anonymous section without explicit enabled setting
    if grep -A 10 &#34;authentication:&#34; &#34;$config_file&#34; | grep -q &#34;anonymous:&#34;; then
      echo &#34;Found anonymous section without explicit enabled setting&#34;
      # If anonymous section exists but no explicit enabled setting, check if it&#39;s secure by default
      ANONYMOUS_DISABLED=true
    fi
  else
    echo &#34;✗ Config file not found: $config_file&#34;
  fi
}

# Check kubelet config files - try both chroot and direct paths
CONFIG_PATHS=(
  &#34;/var/lib/kubelet/config.yaml&#34;
  &#34;/host/var/lib/kubelet/config.yaml&#34;
  &#34;/etc/kubernetes/kubelet/kubelet-config.yaml&#34;
  &#34;/host/etc/kubernetes/kubelet/kubelet-config.yaml&#34;
  &#34;/etc/kubernetes/kubelet.yaml&#34;
  &#34;/host/etc/kubernetes/kubelet.yaml&#34;
  &#34;/etc/kubernetes/kubelet/config.yaml&#34;
  &#34;/host/etc/kubernetes/kubelet/config.yaml&#34;
)

for config_path in &#34;${CONFIG_PATHS[@]}&#34;; do
  if [ -f &#34;$config_path&#34; ]; then
    check_kubelet_config &#34;$config_path&#34;
    break
  fi
done

# Check kubelet process command line arguments
echo &#34;Checking kubelet process command line arguments...&#34;

# Try multiple ways to find kubelet process
KUBELET_PID=&#34;&#34;

# Method 1: Check /proc directly (if accessible)
if [ -d /proc ]; then
  for pid in $(ls /proc 2&gt;/dev/null | grep &#39;^[0-9]*$&#39;); do
    if [ -f &#34;/proc/$pid/cmdline&#34; ] &amp;&amp; grep -q kubelet &#34;/proc/$pid/cmdline&#34; 2&gt;/dev/null; then
      KUBELET_PID=$pid
      echo &#34;Found kubelet process: PID $pid&#34;
      CMDLINE=$(tr &#39;\0&#39; &#39; &#39; &lt; &#34;/proc/$pid/cmdline&#34; 2&gt;/dev/null || echo &#34;&#34;)
      echo &#34;Command line: $CMDLINE&#34;
      
      if echo &#34;$CMDLINE&#34; | grep -q &#34;\--anonymous-auth=false&#34;; then
        ANONYMOUS_DISABLED=true
        echo &#34;✓ Found: --anonymous-auth=false in kubelet command line&#34;
        break
      elif echo &#34;$CMDLINE&#34; | grep -q &#34;\--anonymous-auth=true&#34;; then
        echo &#34;FAIL: Kubelet anonymous authentication is explicitly enabled via command line&#34;
        exit 1
      fi
    fi
  done
fi

# Method 2: Check /host/proc if /proc didn&#39;t work
if [ -z &#34;$KUBELET_PID&#34; ] &amp;&amp; [ -d /host/proc ]; then
  for pid in $(ls /host/proc 2&gt;/dev/null | grep &#39;^[0-9]*$&#39;); do
    if [ -f &#34;/host/proc/$pid/cmdline&#34; ] &amp;&amp; grep -q kubelet &#34;/host/proc/$pid/cmdline&#34; 2&gt;/dev/null; then
      KUBELET_PID=$pid
      echo &#34;Found kubelet process in /host/proc: PID $pid&#34;
      CMDLINE=$(tr &#39;\0&#39; &#39; &#39; &lt; &#34;/host/proc/$pid/cmdline&#34; 2&gt;/dev/null || echo &#34;&#34;)
      echo &#34;Command line: $CMDLINE&#34;
      
      if echo &#34;$CMDLINE&#34; | grep -q &#34;\--anonymous-auth=false&#34;; then
        ANONYMOUS_DISABLED=true
        echo &#34;✓ Found: --anonymous-auth=false in kubelet command line&#34;
        break
      elif echo &#34;$CMDLINE&#34; | grep -q &#34;\--anonymous-auth=true&#34;; then
        echo &#34;FAIL: Kubelet anonymous authentication is explicitly enabled via command line&#34;
        exit 1
      fi
    fi
  done
fi

# Method 3: Use ps command if available
if [ -z &#34;$KUBELET_PID&#34; ] &amp;&amp; command -v ps &gt;/dev/null 2&gt;&amp;1; then
  echo &#34;Trying ps command to find kubelet...&#34;
  PS_OUTPUT=$(ps aux 2&gt;/dev/null | grep kubelet | grep -v grep || echo &#34;&#34;)
  if [ -n &#34;$PS_OUTPUT&#34; ]; then
    echo &#34;Kubelet process found via ps:&#34;
    echo &#34;$PS_OUTPUT&#34;
    
    if echo &#34;$PS_OUTPUT&#34; | grep -q &#34;\--anonymous-auth=false&#34;; then
      ANONYMOUS_DISABLED=true
      echo &#34;✓ Found: --anonymous-auth=false in kubelet command line (via ps)&#34;
    elif echo &#34;$PS_OUTPUT&#34; | grep -q &#34;\--anonymous-auth=true&#34;; then
      echo &#34;FAIL: Kubelet anonymous authentication is explicitly enabled via command line (via ps)&#34;
      exit 1
    fi
  fi
fi

# Evaluate results
echo &#34;&#34;
echo &#34;=== EVALUATION ===&#34;
echo &#34;Config found: $CONFIG_FOUND&#34;
echo &#34;Anonymous disabled: $ANONYMOUS_DISABLED&#34;

if [ &#34;$ANONYMOUS_DISABLED&#34; = &#34;true&#34; ]; then
  echo &#34;PASS: Kubelet anonymous authentication is disabled&#34;
  exit 0
elif [ &#34;$CONFIG_FOUND&#34; = &#34;true&#34; ]; then
  # If we found config files but no explicit disabled setting, this might be a newer version
  # where anonymous auth is disabled by default
  echo &#34;? Config found but no explicit anonymous auth setting&#34;
  echo &#34;? In Kubernetes 1.6&#43;, anonymous authentication is disabled by default&#34;
  echo &#34;PASS: Kubelet anonymous authentication appears to be disabled by default&#34;
  exit 0
else
  echo &#34;FAIL: No kubelet configuration found to verify anonymous authentication status&#34;
  exit 1
fi
 
✓ Found: --anonymous-auth=false in kubelet command line

=== EVALUATION ===
Config found: true
Anonymous disabled: true
PASS: Kubelet anonymous authentication is disabled</td>
                                            </tr>
                                            
                                        </tbody>
                                    </table>
                                </div>
                                
                            
                        </div>
                    </div>
                </div>
                
                
                <div class="finding-item">
                    <div class="finding-header" onclick="toggleFinding(this)">
                        <div class="finding-title-section">
                            <div class="finding-id">V-242392</div>
                            <div class="finding-title">The Kubernetes Kubelet must have the authorization mode set to Webhook.</div>
                            <div class="finding-meta">
                                <span class="severity-badge severity-high">high</span>
                                <span class="status-badge status-pass-pass">PASS</span>
                                <span class="info-value">node</span>
                            </div>
                        </div>
                        <div class="expand-icon">▼</div>
                    </div>
                    <div class="finding-content">
                        <div class="finding-details">
                            <div class="detail-section">
                                <h4>Description</h4>
                                <p>A user who has access to the Kubelet essentially has root access to the nodes contained within the Kubernetes Control Plane. To control access, users must be authenticated and authorized. By allowing anonymous connections, the controls put in place to secure the Kubelet can be bypassed.

Setting the authorization mode to Webhook ensures that the Kubelet will use the SubjectAccessReview API to determine if the requestor has permission to make the requested API call. This allows for fine-grained access control to the Kubelet API.

The AlwaysAllow mode allows all authenticated requests to the Kubelet API, which is a security risk.
</p>
                            </div>
                            
                            
                            <div class="detail-section">
                                <h4>Check Text</h4>
                                <pre>On the Control Plane, run the command:
ps -ef | grep kubelet

Check the config file(s) referenced by the --config argument (example: --config=/etc/kubernetes/kubelet/kubelet-config.yaml):

Change to the directory identified by --config argument and run the command:
grep -i authorization *

If the setting \&#34;mode\&#34; is not set to \&#34;Webhook\&#34;, this is a finding.

If the setting \&#34;mode\&#34; is not configured, check if there is an entry for \&#34;--authorization-mode\&#34; in the kubelet command.

On the Control Plane, run the command:
ps -ef | grep kubelet

If \&#34;--authorization-mode\&#34; is not set to \&#34;Webhook\&#34;, this is a finding.
</pre>
                            </div>
                            
                            
                            
                            <div class="detail-section">
                                <h4>Fix Text</h4>
                                <pre>On the Control Plane, run the command:
ps -ef | grep kubelet

Check the config file(s) referenced by the --config argument (example: --config=/etc/kubernetes/kubelet/kubelet-config.yaml):

Edit the Kubernetes Kubelet config file:
Set the value of \&#34;mode\&#34; to \&#34;Webhook\&#34;.

Reset Kubelet service using the following command:
service kubelet restart
</pre>
                            </div>
                            
                            
                            
                            <div class="detail-section">
                                <h4>Result Message</h4>
                                <p>PASS: Kubelet authorization mode is set to Webhook</p>
                            </div>
                            
                            
                            
                                
                                <div class="detail-section">
                                    <h4>Node Results</h4>
                                    <table class="node-results-table">
                                        <thead>
                                            <tr>
                                                <th>Node Name</th>
                                                <th>Status</th>
                                                <th>Message</th>
                                                <th>Evidence</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            
                                            <tr>
                                                <td class="node-name">***************</td>
                                                <td><span class="status-badge status-pass">PASS</span></td>
                                                <td>PASS: Kubelet authorization mode is set to Webhook</td>
                                                <td>Checking kubelet authorization mode configuration...
Checking config file: /var/lib/kubelet/config.yaml
✓ Config file found: /var/lib/kubelet/config.yaml
Authorization section:
authorization:
  mode: Webhook
  webhook:
    cacheAuthorizedTTL: 0s
    cacheUnauthorizedTTL: 0s
cgroupDriver: systemd
clusterDNS:
- *********
clusterDomain: cluster.local
containerRuntimeEndpoint: &#34;&#34;
cpuManagerReconcilePeriod: 0s
✓ Found: authorization mode explicitly set to Webhook
Checking kubelet process command line arguments...
Found kubelet process: PID 16224
Command line: bash /kube-ovn/start-cniserver.sh --enable-mirror=false --mirror-iface=mirror0 --node-switch=join --encap-checksum=true --service-cluster-ip-range=********/16 --iface=eth0 --dpdk-tunnel-iface=br-phy --network-type=geneve --default-interface-name=eth0 --logtostderr=false --alsologtostderr=true --log_file=/var/log/kube-ovn/kube-ovn-cni.log --log_file_max_size=200 --enable-metrics=true --kubelet-dir=/var/lib/kubelet --enable-tproxy=false --ovs-vsctl-concurrency=100 --secure-serving=true --enable-ovn-ipsec=false --set-vxlan-tx-off=false --tls-min-version=TLS12 --tls-max-version=TLS12 --tls-cipher-suites=TLS_ECDHE_ECDSA_WITH_AES_256_GCM_SHA384 --tls-cipher-suites=TLS_ECDHE_RSA_WITH_AES_256_GCM_SHA384 --tls-cipher-suites=TLS_ECDHE_RSA_WITH_CHACHA20_POLY1305_SHA256 --tls-cipher-suites=TLS_ECDHE_ECDSA_WITH_CHACHA20_POLY1305_SHA256 
Found kubelet process: PID 16249
Command line: ./kube-ovn-daemon --ovs-socket=/run/openvswitch/db.sock --bind-socket=/run/openvswitch/kube-ovn-daemon.sock --enable-mirror=false --mirror-iface=mirror0 --node-switch=join --encap-checksum=true --service-cluster-ip-range=********/16 --iface=eth0 --dpdk-tunnel-iface=br-phy --network-type=geneve --default-interface-name=eth0 --logtostderr=false --alsologtostderr=true --log_file=/var/log/kube-ovn/kube-ovn-cni.log --log_file_max_size=200 --enable-metrics=true --kubelet-dir=/var/lib/kubelet --enable-tproxy=false --ovs-vsctl-concurrency=100 --secure-serving=true --enable-ovn-ipsec=false --set-vxlan-tx-off=false --tls-min-version=TLS12 --tls-max-version=TLS12 --tls-cipher-suites=TLS_ECDHE_ECDSA_WITH_AES_256_GCM_SHA384 --tls-cipher-suites=TLS_ECDHE_RSA_WITH_AES_256_GCM_SHA384 --tls-cipher-suites=TLS_ECDHE_RSA_WITH_CHACHA20_POLY1305_SHA256 --tls-cipher-suites=TLS_ECDHE_ECDSA_WITH_CHACHA20_POLY1305_SHA256 
Found kubelet process: PID 3331098
Command line: /bin/bash /usr/local/bin/unified-scanner.sh #!/bin/bash
# Check if kubelet authorization mode is set to Webhook
# STIG requirement: authorization mode should be set to Webhook

WEBHOOK_MODE_CONFIGURED=false
CONFIG_FOUND=false

echo &#34;Checking kubelet authorization mode configuration...&#34;

# Function to check kubelet config file for authorization mode setting
check_kubelet_auth_config() {
  local config_file=&#34;$1&#34;
  echo &#34;Checking config file: $config_file&#34;
  
  if [ -f &#34;$config_file&#34; ]; then
    CONFIG_FOUND=true
    echo &#34;✓ Config file found: $config_file&#34;
    
    # Show relevant config section for debugging
    echo &#34;Authorization section:&#34;
    grep -A 10 &#34;authorization:&#34; &#34;$config_file&#34; | head -15 || echo &#34;No authorization section found&#34;
    
    # Check for explicit &#34;mode: Webhook&#34; under authorization section
    if grep -A 10 &#34;authorization:&#34; &#34;$config_file&#34; | grep -q &#34;mode: Webhook&#34;; then
      WEBHOOK_MODE_CONFIGURED=true
      echo &#34;✓ Found: authorization mode explicitly set to Webhook&#34;
      return 0
    fi
    
    # Check if authorization mode is set to AlwaysAllow (fail case)
    if grep -A 10 &#34;authorization:&#34; &#34;$config_file&#34; | grep -q &#34;mode: AlwaysAllow&#34;; then
      echo &#34;FAIL: Kubelet authorization mode is set to insecure value (AlwaysAllow) in $config_file&#34;
      exit 1
    fi
    
    # Check for authorization section without explicit mode setting
    if grep -A 10 &#34;authorization:&#34; &#34;$config_file&#34; | grep -q &#34;authorization:&#34;; then
      echo &#34;Found authorization section without explicit mode setting&#34;
    fi
  else
    echo &#34;✗ Config file not found: $config_file&#34;
  fi
}

# Check kubelet config files - try both chroot and direct paths
CONFIG_PATHS=(
  &#34;/var/lib/kubelet/config.yaml&#34;
  &#34;/host/var/lib/kubelet/config.yaml&#34;
  &#34;/etc/kubernetes/kubelet/kubelet-config.yaml&#34;
  &#34;/host/etc/kubernetes/kubelet/kubelet-config.yaml&#34;
  &#34;/etc/kubernetes/kubelet.yaml&#34;
  &#34;/host/etc/kubernetes/kubelet.yaml&#34;
  &#34;/etc/kubernetes/kubelet/config.yaml&#34;
  &#34;/host/etc/kubernetes/kubelet/config.yaml&#34;
)

for config_path in &#34;${CONFIG_PATHS[@]}&#34;; do
  if [ -f &#34;$config_path&#34; ]; then
    check_kubelet_auth_config &#34;$config_path&#34;
    break
  fi
done

# Check kubelet process command line arguments
echo &#34;Checking kubelet process command line arguments...&#34;

# Try multiple ways to find kubelet process
KUBELET_PID=&#34;&#34;

# Method 1: Check /proc directly (if accessible)
if [ -d /proc ]; then
  for pid in $(ls /proc 2&gt;/dev/null | grep &#39;^[0-9]*$&#39;); do
    if [ -f &#34;/proc/$pid/cmdline&#34; ] &amp;&amp; grep -q kubelet &#34;/proc/$pid/cmdline&#34; 2&gt;/dev/null; then
      KUBELET_PID=$pid
      echo &#34;Found kubelet process: PID $pid&#34;
      CMDLINE=$(tr &#39;\0&#39; &#39; &#39; &lt; &#34;/proc/$pid/cmdline&#34; 2&gt;/dev/null || echo &#34;&#34;)
      echo &#34;Command line: $CMDLINE&#34;
      
      if echo &#34;$CMDLINE&#34; | grep -q &#34;\--authorization-mode=Webhook&#34;; then
        WEBHOOK_MODE_CONFIGURED=true
        echo &#34;✓ Found: --authorization-mode=Webhook in kubelet command line&#34;
        break
      elif echo &#34;$CMDLINE&#34; | grep -q &#34;\--authorization-mode=AlwaysAllow&#34;; then
        echo &#34;FAIL: Kubelet authorization mode is set to insecure value (AlwaysAllow) via command line&#34;
        exit 1
      fi
    fi
  done
fi

# Method 2: Check /host/proc if /proc didn&#39;t work
if [ -z &#34;$KUBELET_PID&#34; ] &amp;&amp; [ -d /host/proc ]; then
  for pid in $(ls /host/proc 2&gt;/dev/null | grep &#39;^[0-9]*$&#39;); do
    if [ -f &#34;/host/proc/$pid/cmdline&#34; ] &amp;&amp; grep -q kubelet &#34;/host/proc/$pid/cmdline&#34; 2&gt;/dev/null; then
      KUBELET_PID=$pid
      echo &#34;Found kubelet process in /host/proc: PID $pid&#34;
      CMDLINE=$(tr &#39;\0&#39; &#39; &#39; &lt; &#34;/host/proc/$pid/cmdline&#34; 2&gt;/dev/null || echo &#34;&#34;)
      echo &#34;Command line: $CMDLINE&#34;
      
      if echo &#34;$CMDLINE&#34; | grep -q &#34;\--authorization-mode=Webhook&#34;; then
        WEBHOOK_MODE_CONFIGURED=true
        echo &#34;✓ Found: --authorization-mode=Webhook in kubelet command line&#34;
        break
      elif echo &#34;$CMDLINE&#34; | grep -q &#34;\--authorization-mode=AlwaysAllow&#34;; then
        echo &#34;FAIL: Kubelet authorization mode is set to insecure value (AlwaysAllow) via command line&#34;
        exit 1
      fi
    fi
  done
fi

# Method 3: Use ps command if available
if [ -z &#34;$KUBELET_PID&#34; ] &amp;&amp; command -v ps &gt;/dev/null 2&gt;&amp;1; then
  echo &#34;Trying ps command to find kubelet...&#34;
  PS_OUTPUT=$(ps aux 2&gt;/dev/null | grep kubelet | grep -v grep || echo &#34;&#34;)
  if [ -n &#34;$PS_OUTPUT&#34; ]; then
    echo &#34;Kubelet process found via ps:&#34;
    echo &#34;$PS_OUTPUT&#34;
    
    if echo &#34;$PS_OUTPUT&#34; | grep -q &#34;\--authorization-mode=Webhook&#34;; then
      WEBHOOK_MODE_CONFIGURED=true
      echo &#34;✓ Found: --authorization-mode=Webhook in kubelet command line (via ps)&#34;
    elif echo &#34;$PS_OUTPUT&#34; | grep -q &#34;\--authorization-mode=AlwaysAllow&#34;; then
      echo &#34;FAIL: Kubelet authorization mode is set to insecure value (AlwaysAllow) via command line (via ps)&#34;
      exit 1
    fi
  fi
fi

# Evaluate results
echo &#34;&#34;
echo &#34;=== EVALUATION ===&#34;
echo &#34;Config found: $CONFIG_FOUND&#34;
echo &#34;Webhook mode configured: $WEBHOOK_MODE_CONFIGURED&#34;

if [ &#34;$WEBHOOK_MODE_CONFIGURED&#34; = &#34;true&#34; ]; then
  echo &#34;PASS: Kubelet authorization mode is set to Webhook&#34;
  exit 0
elif [ &#34;$CONFIG_FOUND&#34; = &#34;true&#34; ]; then
  # If we found config files but no explicit mode setting, check default behavior
  echo &#34;? Config found but no explicit authorization mode setting&#34;
  echo &#34;? In Kubernetes 1.6&#43;, authorization mode defaults to Webhook&#34;
  echo &#34;PASS: Kubelet authorization mode appears to be set to default (Webhook)&#34;
  exit 0
else
  echo &#34;FAIL: No kubelet configuration found to verify authorization mode&#34;
  exit 1
fi
 
✓ Found: --authorization-mode=Webhook in kubelet command line

=== EVALUATION ===
Config found: true
Webhook mode configured: true
PASS: Kubelet authorization mode is set to Webhook</td>
                                            </tr>
                                            
                                            <tr>
                                                <td class="node-name">**************</td>
                                                <td><span class="status-badge status-pass">PASS</span></td>
                                                <td>PASS: Kubelet authorization mode is set to Webhook</td>
                                                <td>Checking kubelet authorization mode configuration...
Checking config file: /var/lib/kubelet/config.yaml
✓ Config file found: /var/lib/kubelet/config.yaml
Authorization section:
authorization:
  mode: Webhook
  webhook:
    cacheAuthorizedTTL: 0s
    cacheUnauthorizedTTL: 0s
cgroupDriver: systemd
clusterDNS:
- *********
clusterDomain: cluster.local
containerRuntimeEndpoint: &#34;&#34;
cpuManagerReconcilePeriod: 0s
✓ Found: authorization mode explicitly set to Webhook
Checking kubelet process command line arguments...
Found kubelet process: PID 19527
Command line: kube-apiserver --admission-control-config-file=/etc/kubernetes/admission/psa-config.yaml --advertise-address=************** --allow-privileged=true --audit-log-format=json --audit-log-maxage=30 --audit-log-maxbackup=10 --audit-log-maxsize=200 --audit-log-mode=batch --audit-log-path=/etc/kubernetes/audit/audit.log --audit-policy-file=/etc/kubernetes/audit/policy.yaml --authorization-mode=Node,RBAC --client-ca-file=/etc/kubernetes/pki/ca.crt --default-not-ready-toleration-seconds=30 --default-unreachable-toleration-seconds=30 --enable-admission-plugins=NodeRestriction --enable-bootstrap-token-auth=true --encryption-provider-config=/etc/kubernetes/encryption-provider.conf --etcd-cafile=/etc/kubernetes/pki/etcd/ca.crt --etcd-certfile=/etc/kubernetes/pki/apiserver-etcd-client.crt --etcd-keyfile=/etc/kubernetes/pki/apiserver-etcd-client.key --etcd-servers=https://127.0.0.1:2379 --kubelet-certificate-authority=/etc/kubernetes/pki/ca.crt --kubelet-client-certificate=/etc/kubernetes/pki/apiserver-kubelet-client.crt --kubelet-client-key=/etc/kubernetes/pki/apiserver-kubelet-client.key --kubelet-preferred-address-types=InternalIP,ExternalIP,Hostname --profiling=false --proxy-client-cert-file=/etc/kubernetes/pki/front-proxy-client.crt --proxy-client-key-file=/etc/kubernetes/pki/front-proxy-client.key --requestheader-allowed-names=front-proxy-client --requestheader-client-ca-file=/etc/kubernetes/pki/front-proxy-ca.crt --requestheader-extra-headers-prefix=X-Remote-Extra- --requestheader-group-headers=X-Remote-Group --requestheader-username-headers=X-Remote-User --secure-port=6443 --service-account-issuer=https://kubernetes.default.svc.cluster.local --service-account-key-file=/etc/kubernetes/pki/sa.pub --service-account-signing-key-file=/etc/kubernetes/pki/sa.key --service-cluster-ip-range=********/16 --tls-cert-file=/etc/kubernetes/pki/apiserver.crt --tls-cipher-suites=TLS_ECDHE_ECDSA_WITH_AES_128_GCM_SHA256,TLS_ECDHE_RSA_WITH_AES_128_GCM_SHA256,TLS_ECDHE_ECDSA_WITH_CHACHA20_POLY1305,TLS_ECDHE_RSA_WITH_AES_256_GCM_SHA384,TLS_ECDHE_RSA_WITH_CHACHA20_POLY1305,TLS_ECDHE_ECDSA_WITH_AES_256_GCM_SHA384 --tls-min-version=VersionTLS12 --tls-private-key-file=/etc/kubernetes/pki/apiserver.key 
Found kubelet process: PID 21139
Command line: /usr/bin/kubelet --bootstrap-kubeconfig=/etc/kubernetes/bootstrap-kubelet.conf --kubeconfig=/etc/kubernetes/kubelet.conf --config=/var/lib/kubelet/config.yaml --address=************** --container-log-max-files=2 --container-log-max-size=100Mi --container-runtime-endpoint=unix:///run/containerd/containerd.sock --hostname-override=************** --kube-reserved=cpu=100m,memory=902Mi --node-ip=************** --node-labels=platform.tkestack.io/machine-ip=************** --node-status-update-frequency=8s --pod-infra-container-image=registry.alauda.cn:60070/tkestack/pause:3.10 --pod-max-pids=16384 --protect-kernel-defaults=true --streaming-connection-idle-timeout=5m0s --system-reserved=cpu=100m,memory=902Mi --tls-cipher-suites=TLS_ECDHE_ECDSA_WITH_AES_128_GCM_SHA256,TLS_ECDHE_RSA_WITH_AES_128_GCM_SHA256,TLS_ECDHE_ECDSA_WITH_CHACHA20_POLY1305,TLS_ECDHE_RSA_WITH_AES_256_GCM_SHA384,TLS_ECDHE_RSA_WITH_CHACHA20_POLY1305,TLS_ECDHE_ECDSA_WITH_AES_256_GCM_SHA384 --tls-min-version=VersionTLS12 
Found kubelet process: PID 25363
Command line: bash /kube-ovn/start-cniserver.sh --enable-mirror=false --mirror-iface=mirror0 --node-switch=join --encap-checksum=true --service-cluster-ip-range=********/16 --iface=eth0 --dpdk-tunnel-iface=br-phy --network-type=geneve --default-interface-name=eth0 --logtostderr=false --alsologtostderr=true --log_file=/var/log/kube-ovn/kube-ovn-cni.log --log_file_max_size=200 --enable-metrics=true --kubelet-dir=/var/lib/kubelet --enable-tproxy=false --ovs-vsctl-concurrency=100 --secure-serving=true --enable-ovn-ipsec=false --set-vxlan-tx-off=false --tls-min-version=TLS12 --tls-max-version=TLS12 --tls-cipher-suites=TLS_ECDHE_ECDSA_WITH_AES_256_GCM_SHA384 --tls-cipher-suites=TLS_ECDHE_RSA_WITH_AES_256_GCM_SHA384 --tls-cipher-suites=TLS_ECDHE_RSA_WITH_CHACHA20_POLY1305_SHA256 --tls-cipher-suites=TLS_ECDHE_ECDSA_WITH_CHACHA20_POLY1305_SHA256 
Found kubelet process: PID 25396
Command line: ./kube-ovn-daemon --ovs-socket=/run/openvswitch/db.sock --bind-socket=/run/openvswitch/kube-ovn-daemon.sock --enable-mirror=false --mirror-iface=mirror0 --node-switch=join --encap-checksum=true --service-cluster-ip-range=********/16 --iface=eth0 --dpdk-tunnel-iface=br-phy --network-type=geneve --default-interface-name=eth0 --logtostderr=false --alsologtostderr=true --log_file=/var/log/kube-ovn/kube-ovn-cni.log --log_file_max_size=200 --enable-metrics=true --kubelet-dir=/var/lib/kubelet --enable-tproxy=false --ovs-vsctl-concurrency=100 --secure-serving=true --enable-ovn-ipsec=false --set-vxlan-tx-off=false --tls-min-version=TLS12 --tls-max-version=TLS12 --tls-cipher-suites=TLS_ECDHE_ECDSA_WITH_AES_256_GCM_SHA384 --tls-cipher-suites=TLS_ECDHE_RSA_WITH_AES_256_GCM_SHA384 --tls-cipher-suites=TLS_ECDHE_RSA_WITH_CHACHA20_POLY1305_SHA256 --tls-cipher-suites=TLS_ECDHE_ECDSA_WITH_CHACHA20_POLY1305_SHA256 
Found kubelet process: PID 3046891
Command line: /bin/bash /usr/local/bin/unified-scanner.sh #!/bin/bash
# Check if kubelet anonymous authentication is disabled
# STIG requirement: anonymous authentication should be disabled

ANONYMOUS_DISABLED=false
CONFIG_FOUND=false

echo &#34;Checking kubelet anonymous authentication configuration...&#34;

# Function to check kubelet config file for anonymous auth setting
check_kubelet_config() {
  local config_file=&#34;$1&#34;
  echo &#34;Checking config file: $config_file&#34;
  
  if [ -f &#34;$config_file&#34; ]; then
    CONFIG_FOUND=true
    echo &#34;✓ Config file found: $config_file&#34;
    
    # Show relevant config section for debugging
    echo &#34;Authentication section:&#34;
    grep -A 10 &#34;authentication:&#34; &#34;$config_file&#34; | head -15 || echo &#34;No authentication section found&#34;
    
    # Method 1: Check for explicit &#34;enabled: false&#34; under anonymous section
    if grep -A 10 &#34;authentication:&#34; &#34;$config_file&#34; | grep -A 5 &#34;anonymous:&#34; | grep -q &#34;enabled: false&#34;; then
      ANONYMOUS_DISABLED=true
      echo &#34;✓ Found: anonymous authentication explicitly disabled (enabled: false)&#34;
      return 0
    fi
    
    # Method 2: Check if anonymous auth is explicitly enabled (fail case)
    if grep -A 10 &#34;authentication:&#34; &#34;$config_file&#34; | grep -A 5 &#34;anonymous:&#34; | grep -q &#34;enabled: true&#34;; then
      echo &#34;FAIL: Kubelet anonymous authentication is explicitly enabled in $config_file&#34;
      exit 1
    fi
    
    # Method 3: Check for anonymous section without explicit enabled setting
    if grep -A 10 &#34;authentication:&#34; &#34;$config_file&#34; | grep -q &#34;anonymous:&#34;; then
      echo &#34;Found anonymous section without explicit enabled setting&#34;
      # If anonymous section exists but no explicit enabled setting, check if it&#39;s secure by default
      ANONYMOUS_DISABLED=true
    fi
  else
    echo &#34;✗ Config file not found: $config_file&#34;
  fi
}

# Check kubelet config files - try both chroot and direct paths
CONFIG_PATHS=(
  &#34;/var/lib/kubelet/config.yaml&#34;
  &#34;/host/var/lib/kubelet/config.yaml&#34;
  &#34;/etc/kubernetes/kubelet/kubelet-config.yaml&#34;
  &#34;/host/etc/kubernetes/kubelet/kubelet-config.yaml&#34;
  &#34;/etc/kubernetes/kubelet.yaml&#34;
  &#34;/host/etc/kubernetes/kubelet.yaml&#34;
  &#34;/etc/kubernetes/kubelet/config.yaml&#34;
  &#34;/host/etc/kubernetes/kubelet/config.yaml&#34;
)

for config_path in &#34;${CONFIG_PATHS[@]}&#34;; do
  if [ -f &#34;$config_path&#34; ]; then
    check_kubelet_config &#34;$config_path&#34;
    break
  fi
done

# Check kubelet process command line arguments
echo &#34;Checking kubelet process command line arguments...&#34;

# Try multiple ways to find kubelet process
KUBELET_PID=&#34;&#34;

# Method 1: Check /proc directly (if accessible)
if [ -d /proc ]; then
  for pid in $(ls /proc 2&gt;/dev/null | grep &#39;^[0-9]*$&#39;); do
    if [ -f &#34;/proc/$pid/cmdline&#34; ] &amp;&amp; grep -q kubelet &#34;/proc/$pid/cmdline&#34; 2&gt;/dev/null; then
      KUBELET_PID=$pid
      echo &#34;Found kubelet process: PID $pid&#34;
      CMDLINE=$(tr &#39;\0&#39; &#39; &#39; &lt; &#34;/proc/$pid/cmdline&#34; 2&gt;/dev/null || echo &#34;&#34;)
      echo &#34;Command line: $CMDLINE&#34;
      
      if echo &#34;$CMDLINE&#34; | grep -q &#34;\--anonymous-auth=false&#34;; then
        ANONYMOUS_DISABLED=true
        echo &#34;✓ Found: --anonymous-auth=false in kubelet command line&#34;
        break
      elif echo &#34;$CMDLINE&#34; | grep -q &#34;\--anonymous-auth=true&#34;; then
        echo &#34;FAIL: Kubelet anonymous authentication is explicitly enabled via command line&#34;
        exit 1
      fi
    fi
  done
fi

# Method 2: Check /host/proc if /proc didn&#39;t work
if [ -z &#34;$KUBELET_PID&#34; ] &amp;&amp; [ -d /host/proc ]; then
  for pid in $(ls /host/proc 2&gt;/dev/null | grep &#39;^[0-9]*$&#39;); do
    if [ -f &#34;/host/proc/$pid/cmdline&#34; ] &amp;&amp; grep -q kubelet &#34;/host/proc/$pid/cmdline&#34; 2&gt;/dev/null; then
      KUBELET_PID=$pid
      echo &#34;Found kubelet process in /host/proc: PID $pid&#34;
      CMDLINE=$(tr &#39;\0&#39; &#39; &#39; &lt; &#34;/host/proc/$pid/cmdline&#34; 2&gt;/dev/null || echo &#34;&#34;)
      echo &#34;Command line: $CMDLINE&#34;
      
      if echo &#34;$CMDLINE&#34; | grep -q &#34;\--anonymous-auth=false&#34;; then
        ANONYMOUS_DISABLED=true
        echo &#34;✓ Found: --anonymous-auth=false in kubelet command line&#34;
        break
      elif echo &#34;$CMDLINE&#34; | grep -q &#34;\--anonymous-auth=true&#34;; then
        echo &#34;FAIL: Kubelet anonymous authentication is explicitly enabled via command line&#34;
        exit 1
      fi
    fi
  done
fi

# Method 3: Use ps command if available
if [ -z &#34;$KUBELET_PID&#34; ] &amp;&amp; command -v ps &gt;/dev/null 2&gt;&amp;1; then
  echo &#34;Trying ps command to find kubelet...&#34;
  PS_OUTPUT=$(ps aux 2&gt;/dev/null | grep kubelet | grep -v grep || echo &#34;&#34;)
  if [ -n &#34;$PS_OUTPUT&#34; ]; then
    echo &#34;Kubelet process found via ps:&#34;
    echo &#34;$PS_OUTPUT&#34;
    
    if echo &#34;$PS_OUTPUT&#34; | grep -q &#34;\--anonymous-auth=false&#34;; then
      ANONYMOUS_DISABLED=true
      echo &#34;✓ Found: --anonymous-auth=false in kubelet command line (via ps)&#34;
    elif echo &#34;$PS_OUTPUT&#34; | grep -q &#34;\--anonymous-auth=true&#34;; then
      echo &#34;FAIL: Kubelet anonymous authentication is explicitly enabled via command line (via ps)&#34;
      exit 1
    fi
  fi
fi

# Evaluate results
echo &#34;&#34;
echo &#34;=== EVALUATION ===&#34;
echo &#34;Config found: $CONFIG_FOUND&#34;
echo &#34;Anonymous disabled: $ANONYMOUS_DISABLED&#34;

if [ &#34;$ANONYMOUS_DISABLED&#34; = &#34;true&#34; ]; then
  echo &#34;PASS: Kubelet anonymous authentication is disabled&#34;
  exit 0
elif [ &#34;$CONFIG_FOUND&#34; = &#34;true&#34; ]; then
  # If we found config files but no explicit disabled setting, this might be a newer version
  # where anonymous auth is disabled by default
  echo &#34;? Config found but no explicit anonymous auth setting&#34;
  echo &#34;? In Kubernetes 1.6&#43;, anonymous authentication is disabled by default&#34;
  echo &#34;PASS: Kubelet anonymous authentication appears to be disabled by default&#34;
  exit 0
else
  echo &#34;FAIL: No kubelet configuration found to verify anonymous authentication status&#34;
  exit 1
fi
 
Found kubelet process: PID 3047987
Command line: /bin/bash /usr/local/bin/unified-scanner.sh #!/bin/bash
# Check if kubelet authorization mode is set to Webhook
# STIG requirement: authorization mode should be set to Webhook

WEBHOOK_MODE_CONFIGURED=false
CONFIG_FOUND=false

echo &#34;Checking kubelet authorization mode configuration...&#34;

# Function to check kubelet config file for authorization mode setting
check_kubelet_auth_config() {
  local config_file=&#34;$1&#34;
  echo &#34;Checking config file: $config_file&#34;
  
  if [ -f &#34;$config_file&#34; ]; then
    CONFIG_FOUND=true
    echo &#34;✓ Config file found: $config_file&#34;
    
    # Show relevant config section for debugging
    echo &#34;Authorization section:&#34;
    grep -A 10 &#34;authorization:&#34; &#34;$config_file&#34; | head -15 || echo &#34;No authorization section found&#34;
    
    # Check for explicit &#34;mode: Webhook&#34; under authorization section
    if grep -A 10 &#34;authorization:&#34; &#34;$config_file&#34; | grep -q &#34;mode: Webhook&#34;; then
      WEBHOOK_MODE_CONFIGURED=true
      echo &#34;✓ Found: authorization mode explicitly set to Webhook&#34;
      return 0
    fi
    
    # Check if authorization mode is set to AlwaysAllow (fail case)
    if grep -A 10 &#34;authorization:&#34; &#34;$config_file&#34; | grep -q &#34;mode: AlwaysAllow&#34;; then
      echo &#34;FAIL: Kubelet authorization mode is set to insecure value (AlwaysAllow) in $config_file&#34;
      exit 1
    fi
    
    # Check for authorization section without explicit mode setting
    if grep -A 10 &#34;authorization:&#34; &#34;$config_file&#34; | grep -q &#34;authorization:&#34;; then
      echo &#34;Found authorization section without explicit mode setting&#34;
    fi
  else
    echo &#34;✗ Config file not found: $config_file&#34;
  fi
}

# Check kubelet config files - try both chroot and direct paths
CONFIG_PATHS=(
  &#34;/var/lib/kubelet/config.yaml&#34;
  &#34;/host/var/lib/kubelet/config.yaml&#34;
  &#34;/etc/kubernetes/kubelet/kubelet-config.yaml&#34;
  &#34;/host/etc/kubernetes/kubelet/kubelet-config.yaml&#34;
  &#34;/etc/kubernetes/kubelet.yaml&#34;
  &#34;/host/etc/kubernetes/kubelet.yaml&#34;
  &#34;/etc/kubernetes/kubelet/config.yaml&#34;
  &#34;/host/etc/kubernetes/kubelet/config.yaml&#34;
)

for config_path in &#34;${CONFIG_PATHS[@]}&#34;; do
  if [ -f &#34;$config_path&#34; ]; then
    check_kubelet_auth_config &#34;$config_path&#34;
    break
  fi
done

# Check kubelet process command line arguments
echo &#34;Checking kubelet process command line arguments...&#34;

# Try multiple ways to find kubelet process
KUBELET_PID=&#34;&#34;

# Method 1: Check /proc directly (if accessible)
if [ -d /proc ]; then
  for pid in $(ls /proc 2&gt;/dev/null | grep &#39;^[0-9]*$&#39;); do
    if [ -f &#34;/proc/$pid/cmdline&#34; ] &amp;&amp; grep -q kubelet &#34;/proc/$pid/cmdline&#34; 2&gt;/dev/null; then
      KUBELET_PID=$pid
      echo &#34;Found kubelet process: PID $pid&#34;
      CMDLINE=$(tr &#39;\0&#39; &#39; &#39; &lt; &#34;/proc/$pid/cmdline&#34; 2&gt;/dev/null || echo &#34;&#34;)
      echo &#34;Command line: $CMDLINE&#34;
      
      if echo &#34;$CMDLINE&#34; | grep -q &#34;\--authorization-mode=Webhook&#34;; then
        WEBHOOK_MODE_CONFIGURED=true
        echo &#34;✓ Found: --authorization-mode=Webhook in kubelet command line&#34;
        break
      elif echo &#34;$CMDLINE&#34; | grep -q &#34;\--authorization-mode=AlwaysAllow&#34;; then
        echo &#34;FAIL: Kubelet authorization mode is set to insecure value (AlwaysAllow) via command line&#34;
        exit 1
      fi
    fi
  done
fi

# Method 2: Check /host/proc if /proc didn&#39;t work
if [ -z &#34;$KUBELET_PID&#34; ] &amp;&amp; [ -d /host/proc ]; then
  for pid in $(ls /host/proc 2&gt;/dev/null | grep &#39;^[0-9]*$&#39;); do
    if [ -f &#34;/host/proc/$pid/cmdline&#34; ] &amp;&amp; grep -q kubelet &#34;/host/proc/$pid/cmdline&#34; 2&gt;/dev/null; then
      KUBELET_PID=$pid
      echo &#34;Found kubelet process in /host/proc: PID $pid&#34;
      CMDLINE=$(tr &#39;\0&#39; &#39; &#39; &lt; &#34;/host/proc/$pid/cmdline&#34; 2&gt;/dev/null || echo &#34;&#34;)
      echo &#34;Command line: $CMDLINE&#34;
      
      if echo &#34;$CMDLINE&#34; | grep -q &#34;\--authorization-mode=Webhook&#34;; then
        WEBHOOK_MODE_CONFIGURED=true
        echo &#34;✓ Found: --authorization-mode=Webhook in kubelet command line&#34;
        break
      elif echo &#34;$CMDLINE&#34; | grep -q &#34;\--authorization-mode=AlwaysAllow&#34;; then
        echo &#34;FAIL: Kubelet authorization mode is set to insecure value (AlwaysAllow) via command line&#34;
        exit 1
      fi
    fi
  done
fi

# Method 3: Use ps command if available
if [ -z &#34;$KUBELET_PID&#34; ] &amp;&amp; command -v ps &gt;/dev/null 2&gt;&amp;1; then
  echo &#34;Trying ps command to find kubelet...&#34;
  PS_OUTPUT=$(ps aux 2&gt;/dev/null | grep kubelet | grep -v grep || echo &#34;&#34;)
  if [ -n &#34;$PS_OUTPUT&#34; ]; then
    echo &#34;Kubelet process found via ps:&#34;
    echo &#34;$PS_OUTPUT&#34;
    
    if echo &#34;$PS_OUTPUT&#34; | grep -q &#34;\--authorization-mode=Webhook&#34;; then
      WEBHOOK_MODE_CONFIGURED=true
      echo &#34;✓ Found: --authorization-mode=Webhook in kubelet command line (via ps)&#34;
    elif echo &#34;$PS_OUTPUT&#34; | grep -q &#34;\--authorization-mode=AlwaysAllow&#34;; then
      echo &#34;FAIL: Kubelet authorization mode is set to insecure value (AlwaysAllow) via command line (via ps)&#34;
      exit 1
    fi
  fi
fi

# Evaluate results
echo &#34;&#34;
echo &#34;=== EVALUATION ===&#34;
echo &#34;Config found: $CONFIG_FOUND&#34;
echo &#34;Webhook mode configured: $WEBHOOK_MODE_CONFIGURED&#34;

if [ &#34;$WEBHOOK_MODE_CONFIGURED&#34; = &#34;true&#34; ]; then
  echo &#34;PASS: Kubelet authorization mode is set to Webhook&#34;
  exit 0
elif [ &#34;$CONFIG_FOUND&#34; = &#34;true&#34; ]; then
  # If we found config files but no explicit mode setting, check default behavior
  echo &#34;? Config found but no explicit authorization mode setting&#34;
  echo &#34;? In Kubernetes 1.6&#43;, authorization mode defaults to Webhook&#34;
  echo &#34;PASS: Kubelet authorization mode appears to be set to default (Webhook)&#34;
  exit 0
else
  echo &#34;FAIL: No kubelet configuration found to verify authorization mode&#34;
  exit 1
fi
 
✓ Found: --authorization-mode=Webhook in kubelet command line

=== EVALUATION ===
Config found: true
Webhook mode configured: true
PASS: Kubelet authorization mode is set to Webhook</td>
                                            </tr>
                                            
                                            <tr>
                                                <td class="node-name">***************</td>
                                                <td><span class="status-badge status-pass">PASS</span></td>
                                                <td>PASS: Kubelet authorization mode is set to Webhook</td>
                                                <td>Checking kubelet authorization mode configuration...
Checking config file: /var/lib/kubelet/config.yaml
✓ Config file found: /var/lib/kubelet/config.yaml
Authorization section:
authorization:
  mode: Webhook
  webhook:
    cacheAuthorizedTTL: 0s
    cacheUnauthorizedTTL: 0s
cgroupDriver: systemd
clusterDNS:
- *********
clusterDomain: cluster.local
containerRuntimeEndpoint: &#34;&#34;
cpuManagerReconcilePeriod: 0s
✓ Found: authorization mode explicitly set to Webhook
Checking kubelet process command line arguments...
Found kubelet process: PID 11027
Command line: bash /kube-ovn/start-cniserver.sh --enable-mirror=false --mirror-iface=mirror0 --node-switch=join --encap-checksum=true --service-cluster-ip-range=********/16 --iface=eth0 --dpdk-tunnel-iface=br-phy --network-type=geneve --default-interface-name=eth0 --logtostderr=false --alsologtostderr=true --log_file=/var/log/kube-ovn/kube-ovn-cni.log --log_file_max_size=200 --enable-metrics=true --kubelet-dir=/var/lib/kubelet --enable-tproxy=false --ovs-vsctl-concurrency=100 --secure-serving=true --enable-ovn-ipsec=false --set-vxlan-tx-off=false --tls-min-version=TLS12 --tls-max-version=TLS12 --tls-cipher-suites=TLS_ECDHE_ECDSA_WITH_AES_256_GCM_SHA384 --tls-cipher-suites=TLS_ECDHE_RSA_WITH_AES_256_GCM_SHA384 --tls-cipher-suites=TLS_ECDHE_RSA_WITH_CHACHA20_POLY1305_SHA256 --tls-cipher-suites=TLS_ECDHE_ECDSA_WITH_CHACHA20_POLY1305_SHA256 
Found kubelet process: PID 11052
Command line: ./kube-ovn-daemon --ovs-socket=/run/openvswitch/db.sock --bind-socket=/run/openvswitch/kube-ovn-daemon.sock --enable-mirror=false --mirror-iface=mirror0 --node-switch=join --encap-checksum=true --service-cluster-ip-range=********/16 --iface=eth0 --dpdk-tunnel-iface=br-phy --network-type=geneve --default-interface-name=eth0 --logtostderr=false --alsologtostderr=true --log_file=/var/log/kube-ovn/kube-ovn-cni.log --log_file_max_size=200 --enable-metrics=true --kubelet-dir=/var/lib/kubelet --enable-tproxy=false --ovs-vsctl-concurrency=100 --secure-serving=true --enable-ovn-ipsec=false --set-vxlan-tx-off=false --tls-min-version=TLS12 --tls-max-version=TLS12 --tls-cipher-suites=TLS_ECDHE_ECDSA_WITH_AES_256_GCM_SHA384 --tls-cipher-suites=TLS_ECDHE_RSA_WITH_AES_256_GCM_SHA384 --tls-cipher-suites=TLS_ECDHE_RSA_WITH_CHACHA20_POLY1305_SHA256 --tls-cipher-suites=TLS_ECDHE_ECDSA_WITH_CHACHA20_POLY1305_SHA256 
Found kubelet process: PID 2032859
Command line: /bin/bash /usr/local/bin/unified-scanner.sh #!/bin/bash
# Check kubelet client CA file configuration
# STIG requirement: clientCAFile should be set in config, not as command line option

echo &#34;Checking kubelet client CA file configuration...&#34;

CLIENT_CA_CONFIGURED=false
COMMAND_LINE_OPTION_FOUND=false
CONFIG_FOUND=false

# Method 1: Check if --client-ca-file is used as command line option (this is a violation)
if [ -d &#34;/host/proc&#34; ]; then
  for pid in $(ls /host/proc | grep &#39;^[0-9]*$&#39;); do
    if [ -f &#34;/host/proc/$pid/cmdline&#34; ] &amp;&amp; grep -q kubelet &#34;/host/proc/$pid/cmdline&#34; 2&gt;/dev/null; then
      cmdline=$(tr &#39;\0&#39; &#39; &#39; &lt; &#34;/host/proc/$pid/cmdline&#34;)
      echo &#34;✓ Found kubelet process (PID: $pid)&#34;
      
      if echo &#34;$cmdline&#34; | grep -q -- &#34;--client-ca-file&#34;; then
        ca_file=$(echo &#34;$cmdline&#34; | sed &#39;s/.*--client-ca-file[= ]\([^ ]*\).*/\1/&#39;)
        echo &#34;  ✗ VIOLATION: Found --client-ca-file as command line option: $ca_file&#34;
        COMMAND_LINE_OPTION_FOUND=true
      fi
      break
    fi
  done
fi

# Method 2: Check kubelet config files for clientCAFile setting
KUBELET_CONFIG_PATHS=(
  &#34;/host/var/lib/kubelet/config.yaml&#34;
  &#34;/host/etc/kubernetes/kubelet/kubelet-config.yaml&#34;
  &#34;/host/etc/kubernetes/kubelet.yaml&#34;
)

for config_path in &#34;${KUBELET_CONFIG_PATHS[@]}&#34;; do
  if [ -f &#34;$config_path&#34; ]; then
    CONFIG_FOUND=true
    echo &#34;✓ Checking kubelet config file: $config_path&#34;
    
    if grep -q &#34;clientCAFile:&#34; &#34;$config_path&#34;; then
      ca_file=$(grep &#34;clientCAFile:&#34; &#34;$config_path&#34; | sed &#39;s/.*clientCAFile: *&#34;\?\([^&#34;]*\)&#34;\?.*/\1/&#39;)
      if [ -n &#34;$ca_file&#34; ]; then
        echo &#34;  ✓ Found clientCAFile in config: $ca_file&#34;
        
        # Verify the CA file exists
        if [ -f &#34;/host$ca_file&#34; ]; then
          echo &#34;  ✓ CA file exists: /host$ca_file&#34;
          CLIENT_CA_CONFIGURED=true
        else
          echo &#34;  ✗ WARNING: CA file does not exist: /host$ca_file&#34;
        fi
      else
        echo &#34;  ✗ clientCAFile is set but has no value&#34;
      fi
    else
      echo &#34;  ℹ No clientCAFile setting found in config file&#34;
    fi
  fi
done

# Try to find kubelet config from running process
if [ -d &#34;/host/proc&#34; ] &amp;&amp; [ &#34;$CONFIG_FOUND&#34; = false ]; then
  for pid in $(ls /host/proc | grep &#39;^[0-9]*$&#39;); do
    if [ -f &#34;/host/proc/$pid/cmdline&#34; ] &amp;&amp; grep -q kubelet &#34;/host/proc/$pid/cmdline&#34; 2&gt;/dev/null; then
      cmdline=$(tr &#39;\0&#39; &#39; &#39; &lt; &#34;/host/proc/$pid/cmdline&#34;)
      if echo &#34;$cmdline&#34; | grep -q -- &#34;--config&#34;; then
        config_file=$(echo &#34;$cmdline&#34; | sed &#39;s/.*--config[= ]\([^ ]*\).*/\1/&#39;)
        if [ -f &#34;/host$config_file&#34; ]; then
          CONFIG_FOUND=true
          echo &#34;✓ Found kubelet config from process: /host$config_file&#34;
          
          if grep -q &#34;clientCAFile:&#34; &#34;/host$config_file&#34;; then
            ca_file=$(grep &#34;clientCAFile:&#34; &#34;/host$config_file&#34; | sed &#39;s/.*clientCAFile: *&#34;\?\([^&#34;]*\)&#34;\?.*/\1/&#39;)
            if [ -n &#34;$ca_file&#34; ]; then
              echo &#34;  ✓ Found clientCAFile in config: $ca_file&#34;
              
              if [ -f &#34;/host$ca_file&#34; ]; then
                echo &#34;  ✓ CA file exists: /host$ca_file&#34;
                CLIENT_CA_CONFIGURED=true
              else
                echo &#34;  ✗ WARNING: CA file does not exist: /host$ca_file&#34;
              fi
            fi
          fi
        fi
      fi
      break
    fi
  done
fi

# Evaluate results
echo &#34;=== EVALUATION ===&#34;
echo &#34;Command line option found: $COMMAND_LINE_OPTION_FOUND&#34;
echo &#34;Config found: $CONFIG_FOUND&#34;
echo &#34;Client CA configured: $CLIENT_CA_CONFIGURED&#34;

if [ &#34;$COMMAND_LINE_OPTION_FOUND&#34; = true ]; then
  echo &#34;FAIL: Found --client-ca-file as command line option (should be in config file)&#34;
  exit 1
fi

if [ &#34;$CONFIG_FOUND&#34; = false ]; then
  echo &#34;FAIL: No kubelet configuration found&#34;
  exit 1
fi

if [ &#34;$CLIENT_CA_CONFIGURED&#34; = false ]; then
  echo &#34;FAIL: clientCAFile is not properly configured in kubelet config&#34;
  exit 1
fi

echo &#34;PASS: clientCAFile is properly configured in kubelet config file&#34;
exit 0
 
Found kubelet process: PID 2032866
Command line: /bin/bash /usr/local/bin/unified-scanner.sh #!/bin/bash
# Check if kubelet authorization mode is set to Webhook
# STIG requirement: authorization mode should be set to Webhook

WEBHOOK_MODE_CONFIGURED=false
CONFIG_FOUND=false

echo &#34;Checking kubelet authorization mode configuration...&#34;

# Function to check kubelet config file for authorization mode setting
check_kubelet_auth_config() {
  local config_file=&#34;$1&#34;
  echo &#34;Checking config file: $config_file&#34;
  
  if [ -f &#34;$config_file&#34; ]; then
    CONFIG_FOUND=true
    echo &#34;✓ Config file found: $config_file&#34;
    
    # Show relevant config section for debugging
    echo &#34;Authorization section:&#34;
    grep -A 10 &#34;authorization:&#34; &#34;$config_file&#34; | head -15 || echo &#34;No authorization section found&#34;
    
    # Check for explicit &#34;mode: Webhook&#34; under authorization section
    if grep -A 10 &#34;authorization:&#34; &#34;$config_file&#34; | grep -q &#34;mode: Webhook&#34;; then
      WEBHOOK_MODE_CONFIGURED=true
      echo &#34;✓ Found: authorization mode explicitly set to Webhook&#34;
      return 0
    fi
    
    # Check if authorization mode is set to AlwaysAllow (fail case)
    if grep -A 10 &#34;authorization:&#34; &#34;$config_file&#34; | grep -q &#34;mode: AlwaysAllow&#34;; then
      echo &#34;FAIL: Kubelet authorization mode is set to insecure value (AlwaysAllow) in $config_file&#34;
      exit 1
    fi
    
    # Check for authorization section without explicit mode setting
    if grep -A 10 &#34;authorization:&#34; &#34;$config_file&#34; | grep -q &#34;authorization:&#34;; then
      echo &#34;Found authorization section without explicit mode setting&#34;
    fi
  else
    echo &#34;✗ Config file not found: $config_file&#34;
  fi
}

# Check kubelet config files - try both chroot and direct paths
CONFIG_PATHS=(
  &#34;/var/lib/kubelet/config.yaml&#34;
  &#34;/host/var/lib/kubelet/config.yaml&#34;
  &#34;/etc/kubernetes/kubelet/kubelet-config.yaml&#34;
  &#34;/host/etc/kubernetes/kubelet/kubelet-config.yaml&#34;
  &#34;/etc/kubernetes/kubelet.yaml&#34;
  &#34;/host/etc/kubernetes/kubelet.yaml&#34;
  &#34;/etc/kubernetes/kubelet/config.yaml&#34;
  &#34;/host/etc/kubernetes/kubelet/config.yaml&#34;
)

for config_path in &#34;${CONFIG_PATHS[@]}&#34;; do
  if [ -f &#34;$config_path&#34; ]; then
    check_kubelet_auth_config &#34;$config_path&#34;
    break
  fi
done

# Check kubelet process command line arguments
echo &#34;Checking kubelet process command line arguments...&#34;

# Try multiple ways to find kubelet process
KUBELET_PID=&#34;&#34;

# Method 1: Check /proc directly (if accessible)
if [ -d /proc ]; then
  for pid in $(ls /proc 2&gt;/dev/null | grep &#39;^[0-9]*$&#39;); do
    if [ -f &#34;/proc/$pid/cmdline&#34; ] &amp;&amp; grep -q kubelet &#34;/proc/$pid/cmdline&#34; 2&gt;/dev/null; then
      KUBELET_PID=$pid
      echo &#34;Found kubelet process: PID $pid&#34;
      CMDLINE=$(tr &#39;\0&#39; &#39; &#39; &lt; &#34;/proc/$pid/cmdline&#34; 2&gt;/dev/null || echo &#34;&#34;)
      echo &#34;Command line: $CMDLINE&#34;
      
      if echo &#34;$CMDLINE&#34; | grep -q &#34;\--authorization-mode=Webhook&#34;; then
        WEBHOOK_MODE_CONFIGURED=true
        echo &#34;✓ Found: --authorization-mode=Webhook in kubelet command line&#34;
        break
      elif echo &#34;$CMDLINE&#34; | grep -q &#34;\--authorization-mode=AlwaysAllow&#34;; then
        echo &#34;FAIL: Kubelet authorization mode is set to insecure value (AlwaysAllow) via command line&#34;
        exit 1
      fi
    fi
  done
fi

# Method 2: Check /host/proc if /proc didn&#39;t work
if [ -z &#34;$KUBELET_PID&#34; ] &amp;&amp; [ -d /host/proc ]; then
  for pid in $(ls /host/proc 2&gt;/dev/null | grep &#39;^[0-9]*$&#39;); do
    if [ -f &#34;/host/proc/$pid/cmdline&#34; ] &amp;&amp; grep -q kubelet &#34;/host/proc/$pid/cmdline&#34; 2&gt;/dev/null; then
      KUBELET_PID=$pid
      echo &#34;Found kubelet process in /host/proc: PID $pid&#34;
      CMDLINE=$(tr &#39;\0&#39; &#39; &#39; &lt; &#34;/host/proc/$pid/cmdline&#34; 2&gt;/dev/null || echo &#34;&#34;)
      echo &#34;Command line: $CMDLINE&#34;
      
      if echo &#34;$CMDLINE&#34; | grep -q &#34;\--authorization-mode=Webhook&#34;; then
        WEBHOOK_MODE_CONFIGURED=true
        echo &#34;✓ Found: --authorization-mode=Webhook in kubelet command line&#34;
        break
      elif echo &#34;$CMDLINE&#34; | grep -q &#34;\--authorization-mode=AlwaysAllow&#34;; then
        echo &#34;FAIL: Kubelet authorization mode is set to insecure value (AlwaysAllow) via command line&#34;
        exit 1
      fi
    fi
  done
fi

# Method 3: Use ps command if available
if [ -z &#34;$KUBELET_PID&#34; ] &amp;&amp; command -v ps &gt;/dev/null 2&gt;&amp;1; then
  echo &#34;Trying ps command to find kubelet...&#34;
  PS_OUTPUT=$(ps aux 2&gt;/dev/null | grep kubelet | grep -v grep || echo &#34;&#34;)
  if [ -n &#34;$PS_OUTPUT&#34; ]; then
    echo &#34;Kubelet process found via ps:&#34;
    echo &#34;$PS_OUTPUT&#34;
    
    if echo &#34;$PS_OUTPUT&#34; | grep -q &#34;\--authorization-mode=Webhook&#34;; then
      WEBHOOK_MODE_CONFIGURED=true
      echo &#34;✓ Found: --authorization-mode=Webhook in kubelet command line (via ps)&#34;
    elif echo &#34;$PS_OUTPUT&#34; | grep -q &#34;\--authorization-mode=AlwaysAllow&#34;; then
      echo &#34;FAIL: Kubelet authorization mode is set to insecure value (AlwaysAllow) via command line (via ps)&#34;
      exit 1
    fi
  fi
fi

# Evaluate results
echo &#34;&#34;
echo &#34;=== EVALUATION ===&#34;
echo &#34;Config found: $CONFIG_FOUND&#34;
echo &#34;Webhook mode configured: $WEBHOOK_MODE_CONFIGURED&#34;

if [ &#34;$WEBHOOK_MODE_CONFIGURED&#34; = &#34;true&#34; ]; then
  echo &#34;PASS: Kubelet authorization mode is set to Webhook&#34;
  exit 0
elif [ &#34;$CONFIG_FOUND&#34; = &#34;true&#34; ]; then
  # If we found config files but no explicit mode setting, check default behavior
  echo &#34;? Config found but no explicit authorization mode setting&#34;
  echo &#34;? In Kubernetes 1.6&#43;, authorization mode defaults to Webhook&#34;
  echo &#34;PASS: Kubelet authorization mode appears to be set to default (Webhook)&#34;
  exit 0
else
  echo &#34;FAIL: No kubelet configuration found to verify authorization mode&#34;
  exit 1
fi
 
✓ Found: --authorization-mode=Webhook in kubelet command line

=== EVALUATION ===
Config found: true
Webhook mode configured: true
PASS: Kubelet authorization mode is set to Webhook</td>
                                            </tr>
                                            
                                            <tr>
                                                <td class="node-name">**************</td>
                                                <td><span class="status-badge status-pass">PASS</span></td>
                                                <td>PASS: Kubelet authorization mode is set to Webhook</td>
                                                <td>Checking kubelet authorization mode configuration...
Checking config file: /var/lib/kubelet/config.yaml
✓ Config file found: /var/lib/kubelet/config.yaml
Authorization section:
authorization:
  mode: Webhook
  webhook:
    cacheAuthorizedTTL: 0s
    cacheUnauthorizedTTL: 0s
cgroupDriver: systemd
clusterDNS:
- *********
clusterDomain: cluster.local
containerRuntimeEndpoint: &#34;&#34;
cpuManagerReconcilePeriod: 0s
✓ Found: authorization mode explicitly set to Webhook
Checking kubelet process command line arguments...
Found kubelet process: PID 11187
Command line: bash /kube-ovn/start-cniserver.sh --enable-mirror=false --mirror-iface=mirror0 --node-switch=join --encap-checksum=true --service-cluster-ip-range=********/16 --iface=eth0 --dpdk-tunnel-iface=br-phy --network-type=geneve --default-interface-name=eth0 --logtostderr=false --alsologtostderr=true --log_file=/var/log/kube-ovn/kube-ovn-cni.log --log_file_max_size=200 --enable-metrics=true --kubelet-dir=/var/lib/kubelet --enable-tproxy=false --ovs-vsctl-concurrency=100 --secure-serving=true --enable-ovn-ipsec=false --set-vxlan-tx-off=false --tls-min-version=TLS12 --tls-max-version=TLS12 --tls-cipher-suites=TLS_ECDHE_ECDSA_WITH_AES_256_GCM_SHA384 --tls-cipher-suites=TLS_ECDHE_RSA_WITH_AES_256_GCM_SHA384 --tls-cipher-suites=TLS_ECDHE_RSA_WITH_CHACHA20_POLY1305_SHA256 --tls-cipher-suites=TLS_ECDHE_ECDSA_WITH_CHACHA20_POLY1305_SHA256 
Found kubelet process: PID 11236
Command line: ./kube-ovn-daemon --ovs-socket=/run/openvswitch/db.sock --bind-socket=/run/openvswitch/kube-ovn-daemon.sock --enable-mirror=false --mirror-iface=mirror0 --node-switch=join --encap-checksum=true --service-cluster-ip-range=********/16 --iface=eth0 --dpdk-tunnel-iface=br-phy --network-type=geneve --default-interface-name=eth0 --logtostderr=false --alsologtostderr=true --log_file=/var/log/kube-ovn/kube-ovn-cni.log --log_file_max_size=200 --enable-metrics=true --kubelet-dir=/var/lib/kubelet --enable-tproxy=false --ovs-vsctl-concurrency=100 --secure-serving=true --enable-ovn-ipsec=false --set-vxlan-tx-off=false --tls-min-version=TLS12 --tls-max-version=TLS12 --tls-cipher-suites=TLS_ECDHE_ECDSA_WITH_AES_256_GCM_SHA384 --tls-cipher-suites=TLS_ECDHE_RSA_WITH_AES_256_GCM_SHA384 --tls-cipher-suites=TLS_ECDHE_RSA_WITH_CHACHA20_POLY1305_SHA256 --tls-cipher-suites=TLS_ECDHE_ECDSA_WITH_CHACHA20_POLY1305_SHA256 
Found kubelet process: PID 1940729
Command line: /bin/bash /usr/local/bin/unified-scanner.sh #!/bin/bash
# Check if kubelet anonymous authentication is disabled
# STIG requirement: anonymous authentication should be disabled

ANONYMOUS_DISABLED=false
CONFIG_FOUND=false

echo &#34;Checking kubelet anonymous authentication configuration...&#34;

# Function to check kubelet config file for anonymous auth setting
check_kubelet_config() {
  local config_file=&#34;$1&#34;
  echo &#34;Checking config file: $config_file&#34;
  
  if [ -f &#34;$config_file&#34; ]; then
    CONFIG_FOUND=true
    echo &#34;✓ Config file found: $config_file&#34;
    
    # Show relevant config section for debugging
    echo &#34;Authentication section:&#34;
    grep -A 10 &#34;authentication:&#34; &#34;$config_file&#34; | head -15 || echo &#34;No authentication section found&#34;
    
    # Method 1: Check for explicit &#34;enabled: false&#34; under anonymous section
    if grep -A 10 &#34;authentication:&#34; &#34;$config_file&#34; | grep -A 5 &#34;anonymous:&#34; | grep -q &#34;enabled: false&#34;; then
      ANONYMOUS_DISABLED=true
      echo &#34;✓ Found: anonymous authentication explicitly disabled (enabled: false)&#34;
      return 0
    fi
    
    # Method 2: Check if anonymous auth is explicitly enabled (fail case)
    if grep -A 10 &#34;authentication:&#34; &#34;$config_file&#34; | grep -A 5 &#34;anonymous:&#34; | grep -q &#34;enabled: true&#34;; then
      echo &#34;FAIL: Kubelet anonymous authentication is explicitly enabled in $config_file&#34;
      exit 1
    fi
    
    # Method 3: Check for anonymous section without explicit enabled setting
    if grep -A 10 &#34;authentication:&#34; &#34;$config_file&#34; | grep -q &#34;anonymous:&#34;; then
      echo &#34;Found anonymous section without explicit enabled setting&#34;
      # If anonymous section exists but no explicit enabled setting, check if it&#39;s secure by default
      ANONYMOUS_DISABLED=true
    fi
  else
    echo &#34;✗ Config file not found: $config_file&#34;
  fi
}

# Check kubelet config files - try both chroot and direct paths
CONFIG_PATHS=(
  &#34;/var/lib/kubelet/config.yaml&#34;
  &#34;/host/var/lib/kubelet/config.yaml&#34;
  &#34;/etc/kubernetes/kubelet/kubelet-config.yaml&#34;
  &#34;/host/etc/kubernetes/kubelet/kubelet-config.yaml&#34;
  &#34;/etc/kubernetes/kubelet.yaml&#34;
  &#34;/host/etc/kubernetes/kubelet.yaml&#34;
  &#34;/etc/kubernetes/kubelet/config.yaml&#34;
  &#34;/host/etc/kubernetes/kubelet/config.yaml&#34;
)

for config_path in &#34;${CONFIG_PATHS[@]}&#34;; do
  if [ -f &#34;$config_path&#34; ]; then
    check_kubelet_config &#34;$config_path&#34;
    break
  fi
done

# Check kubelet process command line arguments
echo &#34;Checking kubelet process command line arguments...&#34;

# Try multiple ways to find kubelet process
KUBELET_PID=&#34;&#34;

# Method 1: Check /proc directly (if accessible)
if [ -d /proc ]; then
  for pid in $(ls /proc 2&gt;/dev/null | grep &#39;^[0-9]*$&#39;); do
    if [ -f &#34;/proc/$pid/cmdline&#34; ] &amp;&amp; grep -q kubelet &#34;/proc/$pid/cmdline&#34; 2&gt;/dev/null; then
      KUBELET_PID=$pid
      echo &#34;Found kubelet process: PID $pid&#34;
      CMDLINE=$(tr &#39;\0&#39; &#39; &#39; &lt; &#34;/proc/$pid/cmdline&#34; 2&gt;/dev/null || echo &#34;&#34;)
      echo &#34;Command line: $CMDLINE&#34;
      
      if echo &#34;$CMDLINE&#34; | grep -q &#34;\--anonymous-auth=false&#34;; then
        ANONYMOUS_DISABLED=true
        echo &#34;✓ Found: --anonymous-auth=false in kubelet command line&#34;
        break
      elif echo &#34;$CMDLINE&#34; | grep -q &#34;\--anonymous-auth=true&#34;; then
        echo &#34;FAIL: Kubelet anonymous authentication is explicitly enabled via command line&#34;
        exit 1
      fi
    fi
  done
fi

# Method 2: Check /host/proc if /proc didn&#39;t work
if [ -z &#34;$KUBELET_PID&#34; ] &amp;&amp; [ -d /host/proc ]; then
  for pid in $(ls /host/proc 2&gt;/dev/null | grep &#39;^[0-9]*$&#39;); do
    if [ -f &#34;/host/proc/$pid/cmdline&#34; ] &amp;&amp; grep -q kubelet &#34;/host/proc/$pid/cmdline&#34; 2&gt;/dev/null; then
      KUBELET_PID=$pid
      echo &#34;Found kubelet process in /host/proc: PID $pid&#34;
      CMDLINE=$(tr &#39;\0&#39; &#39; &#39; &lt; &#34;/host/proc/$pid/cmdline&#34; 2&gt;/dev/null || echo &#34;&#34;)
      echo &#34;Command line: $CMDLINE&#34;
      
      if echo &#34;$CMDLINE&#34; | grep -q &#34;\--anonymous-auth=false&#34;; then
        ANONYMOUS_DISABLED=true
        echo &#34;✓ Found: --anonymous-auth=false in kubelet command line&#34;
        break
      elif echo &#34;$CMDLINE&#34; | grep -q &#34;\--anonymous-auth=true&#34;; then
        echo &#34;FAIL: Kubelet anonymous authentication is explicitly enabled via command line&#34;
        exit 1
      fi
    fi
  done
fi

# Method 3: Use ps command if available
if [ -z &#34;$KUBELET_PID&#34; ] &amp;&amp; command -v ps &gt;/dev/null 2&gt;&amp;1; then
  echo &#34;Trying ps command to find kubelet...&#34;
  PS_OUTPUT=$(ps aux 2&gt;/dev/null | grep kubelet | grep -v grep || echo &#34;&#34;)
  if [ -n &#34;$PS_OUTPUT&#34; ]; then
    echo &#34;Kubelet process found via ps:&#34;
    echo &#34;$PS_OUTPUT&#34;
    
    if echo &#34;$PS_OUTPUT&#34; | grep -q &#34;\--anonymous-auth=false&#34;; then
      ANONYMOUS_DISABLED=true
      echo &#34;✓ Found: --anonymous-auth=false in kubelet command line (via ps)&#34;
    elif echo &#34;$PS_OUTPUT&#34; | grep -q &#34;\--anonymous-auth=true&#34;; then
      echo &#34;FAIL: Kubelet anonymous authentication is explicitly enabled via command line (via ps)&#34;
      exit 1
    fi
  fi
fi

# Evaluate results
echo &#34;&#34;
echo &#34;=== EVALUATION ===&#34;
echo &#34;Config found: $CONFIG_FOUND&#34;
echo &#34;Anonymous disabled: $ANONYMOUS_DISABLED&#34;

if [ &#34;$ANONYMOUS_DISABLED&#34; = &#34;true&#34; ]; then
  echo &#34;PASS: Kubelet anonymous authentication is disabled&#34;
  exit 0
elif [ &#34;$CONFIG_FOUND&#34; = &#34;true&#34; ]; then
  # If we found config files but no explicit disabled setting, this might be a newer version
  # where anonymous auth is disabled by default
  echo &#34;? Config found but no explicit anonymous auth setting&#34;
  echo &#34;? In Kubernetes 1.6&#43;, anonymous authentication is disabled by default&#34;
  echo &#34;PASS: Kubelet anonymous authentication appears to be disabled by default&#34;
  exit 0
else
  echo &#34;FAIL: No kubelet configuration found to verify anonymous authentication status&#34;
  exit 1
fi
 
Found kubelet process: PID 1940882
Command line: /bin/bash /usr/local/bin/unified-scanner.sh #!/bin/bash
# Check if kubelet authorization mode is set to Webhook
# STIG requirement: authorization mode should be set to Webhook

WEBHOOK_MODE_CONFIGURED=false
CONFIG_FOUND=false

echo &#34;Checking kubelet authorization mode configuration...&#34;

# Function to check kubelet config file for authorization mode setting
check_kubelet_auth_config() {
  local config_file=&#34;$1&#34;
  echo &#34;Checking config file: $config_file&#34;
  
  if [ -f &#34;$config_file&#34; ]; then
    CONFIG_FOUND=true
    echo &#34;✓ Config file found: $config_file&#34;
    
    # Show relevant config section for debugging
    echo &#34;Authorization section:&#34;
    grep -A 10 &#34;authorization:&#34; &#34;$config_file&#34; | head -15 || echo &#34;No authorization section found&#34;
    
    # Check for explicit &#34;mode: Webhook&#34; under authorization section
    if grep -A 10 &#34;authorization:&#34; &#34;$config_file&#34; | grep -q &#34;mode: Webhook&#34;; then
      WEBHOOK_MODE_CONFIGURED=true
      echo &#34;✓ Found: authorization mode explicitly set to Webhook&#34;
      return 0
    fi
    
    # Check if authorization mode is set to AlwaysAllow (fail case)
    if grep -A 10 &#34;authorization:&#34; &#34;$config_file&#34; | grep -q &#34;mode: AlwaysAllow&#34;; then
      echo &#34;FAIL: Kubelet authorization mode is set to insecure value (AlwaysAllow) in $config_file&#34;
      exit 1
    fi
    
    # Check for authorization section without explicit mode setting
    if grep -A 10 &#34;authorization:&#34; &#34;$config_file&#34; | grep -q &#34;authorization:&#34;; then
      echo &#34;Found authorization section without explicit mode setting&#34;
    fi
  else
    echo &#34;✗ Config file not found: $config_file&#34;
  fi
}

# Check kubelet config files - try both chroot and direct paths
CONFIG_PATHS=(
  &#34;/var/lib/kubelet/config.yaml&#34;
  &#34;/host/var/lib/kubelet/config.yaml&#34;
  &#34;/etc/kubernetes/kubelet/kubelet-config.yaml&#34;
  &#34;/host/etc/kubernetes/kubelet/kubelet-config.yaml&#34;
  &#34;/etc/kubernetes/kubelet.yaml&#34;
  &#34;/host/etc/kubernetes/kubelet.yaml&#34;
  &#34;/etc/kubernetes/kubelet/config.yaml&#34;
  &#34;/host/etc/kubernetes/kubelet/config.yaml&#34;
)

for config_path in &#34;${CONFIG_PATHS[@]}&#34;; do
  if [ -f &#34;$config_path&#34; ]; then
    check_kubelet_auth_config &#34;$config_path&#34;
    break
  fi
done

# Check kubelet process command line arguments
echo &#34;Checking kubelet process command line arguments...&#34;

# Try multiple ways to find kubelet process
KUBELET_PID=&#34;&#34;

# Method 1: Check /proc directly (if accessible)
if [ -d /proc ]; then
  for pid in $(ls /proc 2&gt;/dev/null | grep &#39;^[0-9]*$&#39;); do
    if [ -f &#34;/proc/$pid/cmdline&#34; ] &amp;&amp; grep -q kubelet &#34;/proc/$pid/cmdline&#34; 2&gt;/dev/null; then
      KUBELET_PID=$pid
      echo &#34;Found kubelet process: PID $pid&#34;
      CMDLINE=$(tr &#39;\0&#39; &#39; &#39; &lt; &#34;/proc/$pid/cmdline&#34; 2&gt;/dev/null || echo &#34;&#34;)
      echo &#34;Command line: $CMDLINE&#34;
      
      if echo &#34;$CMDLINE&#34; | grep -q &#34;\--authorization-mode=Webhook&#34;; then
        WEBHOOK_MODE_CONFIGURED=true
        echo &#34;✓ Found: --authorization-mode=Webhook in kubelet command line&#34;
        break
      elif echo &#34;$CMDLINE&#34; | grep -q &#34;\--authorization-mode=AlwaysAllow&#34;; then
        echo &#34;FAIL: Kubelet authorization mode is set to insecure value (AlwaysAllow) via command line&#34;
        exit 1
      fi
    fi
  done
fi

# Method 2: Check /host/proc if /proc didn&#39;t work
if [ -z &#34;$KUBELET_PID&#34; ] &amp;&amp; [ -d /host/proc ]; then
  for pid in $(ls /host/proc 2&gt;/dev/null | grep &#39;^[0-9]*$&#39;); do
    if [ -f &#34;/host/proc/$pid/cmdline&#34; ] &amp;&amp; grep -q kubelet &#34;/host/proc/$pid/cmdline&#34; 2&gt;/dev/null; then
      KUBELET_PID=$pid
      echo &#34;Found kubelet process in /host/proc: PID $pid&#34;
      CMDLINE=$(tr &#39;\0&#39; &#39; &#39; &lt; &#34;/host/proc/$pid/cmdline&#34; 2&gt;/dev/null || echo &#34;&#34;)
      echo &#34;Command line: $CMDLINE&#34;
      
      if echo &#34;$CMDLINE&#34; | grep -q &#34;\--authorization-mode=Webhook&#34;; then
        WEBHOOK_MODE_CONFIGURED=true
        echo &#34;✓ Found: --authorization-mode=Webhook in kubelet command line&#34;
        break
      elif echo &#34;$CMDLINE&#34; | grep -q &#34;\--authorization-mode=AlwaysAllow&#34;; then
        echo &#34;FAIL: Kubelet authorization mode is set to insecure value (AlwaysAllow) via command line&#34;
        exit 1
      fi
    fi
  done
fi

# Method 3: Use ps command if available
if [ -z &#34;$KUBELET_PID&#34; ] &amp;&amp; command -v ps &gt;/dev/null 2&gt;&amp;1; then
  echo &#34;Trying ps command to find kubelet...&#34;
  PS_OUTPUT=$(ps aux 2&gt;/dev/null | grep kubelet | grep -v grep || echo &#34;&#34;)
  if [ -n &#34;$PS_OUTPUT&#34; ]; then
    echo &#34;Kubelet process found via ps:&#34;
    echo &#34;$PS_OUTPUT&#34;
    
    if echo &#34;$PS_OUTPUT&#34; | grep -q &#34;\--authorization-mode=Webhook&#34;; then
      WEBHOOK_MODE_CONFIGURED=true
      echo &#34;✓ Found: --authorization-mode=Webhook in kubelet command line (via ps)&#34;
    elif echo &#34;$PS_OUTPUT&#34; | grep -q &#34;\--authorization-mode=AlwaysAllow&#34;; then
      echo &#34;FAIL: Kubelet authorization mode is set to insecure value (AlwaysAllow) via command line (via ps)&#34;
      exit 1
    fi
  fi
fi

# Evaluate results
echo &#34;&#34;
echo &#34;=== EVALUATION ===&#34;
echo &#34;Config found: $CONFIG_FOUND&#34;
echo &#34;Webhook mode configured: $WEBHOOK_MODE_CONFIGURED&#34;

if [ &#34;$WEBHOOK_MODE_CONFIGURED&#34; = &#34;true&#34; ]; then
  echo &#34;PASS: Kubelet authorization mode is set to Webhook&#34;
  exit 0
elif [ &#34;$CONFIG_FOUND&#34; = &#34;true&#34; ]; then
  # If we found config files but no explicit mode setting, check default behavior
  echo &#34;? Config found but no explicit authorization mode setting&#34;
  echo &#34;? In Kubernetes 1.6&#43;, authorization mode defaults to Webhook&#34;
  echo &#34;PASS: Kubelet authorization mode appears to be set to default (Webhook)&#34;
  exit 0
else
  echo &#34;FAIL: No kubelet configuration found to verify authorization mode&#34;
  exit 1
fi
 
✓ Found: --authorization-mode=Webhook in kubelet command line

=== EVALUATION ===
Config found: true
Webhook mode configured: true
PASS: Kubelet authorization mode is set to Webhook</td>
                                            </tr>
                                            
                                        </tbody>
                                    </table>
                                </div>
                                
                            
                        </div>
                    </div>
                </div>
                
                
                <div class="finding-item">
                    <div class="finding-header" onclick="toggleFinding(this)">
                        <div class="finding-title-section">
                            <div class="finding-id">V-242387</div>
                            <div class="finding-title">The Kubernetes Kubelet must have the readOnlyPort flag disabled.</div>
                            <div class="finding-meta">
                                <span class="severity-badge severity-high">high</span>
                                <span class="status-badge status-pass-pass">PASS</span>
                                <span class="info-value">node</span>
                            </div>
                        </div>
                        <div class="expand-icon">▼</div>
                    </div>
                    <div class="finding-content">
                        <div class="finding-details">
                            <div class="detail-section">
                                <h4>Description</h4>
                                <p>The kubelet process provides a read-only API in addition to the main Kubernetes API. Unauthenticated access to this read-only API could disclose information about the cluster that could assist an attacker in a further attack. The read-only API provided by the kubelet must be disabled.

By default, the kubelet serves a read-only view of most of its internal state on a port (typically 10255). This port does not require authentication and provides access to potentially sensitive information about the node and running workloads. Disabling this port reduces the attack surface.
</p>
                            </div>
                            
                            
                            <div class="detail-section">
                                <h4>Check Text</h4>
                                <pre>On the Control Plane and Worker Nodes, run the command:
ps -ef | grep kubelet

Check the config file(s) referenced by the --config argument (example: --config=/etc/kubernetes/kubelet/kubelet-config.yaml):

Change to the directory identified by --config argument and run the command:
grep -i readonlyport *

If the setting \&#34;readOnlyPort\&#34; is not set to \&#34;0\&#34;, this is a finding.

If the setting \&#34;readOnlyPort\&#34; is not configured, check if there is an entry for \&#34;--read-only-port\&#34; in the kubelet command.

On the Control Plane and Worker Nodes, run the command:
ps -ef | grep kubelet

If \&#34;--read-only-port\&#34; is not set to \&#34;0\&#34;, this is a finding.
</pre>
                            </div>
                            
                            
                            
                            <div class="detail-section">
                                <h4>Fix Text</h4>
                                <pre>On the Control Plane and Worker Nodes, run the command:
ps -ef | grep kubelet

Check the config file(s) referenced by the --config argument (example: --config=/etc/kubernetes/kubelet/kubelet-config.yaml):

Edit the Kubernetes Kubelet config file:
Set the value of \&#34;readOnlyPort\&#34; to \&#34;0\&#34;.

Reset Kubelet service using the following command:
service kubelet restart
</pre>
                            </div>
                            
                            
                            
                            <div class="detail-section">
                                <h4>Result Message</h4>
                                <p>✓ PASS: Kubelet read-only port is disabled</p>
                            </div>
                            
                            
                            
                                
                                <div class="detail-section">
                                    <h4>Node Results</h4>
                                    <table class="node-results-table">
                                        <thead>
                                            <tr>
                                                <th>Node Name</th>
                                                <th>Status</th>
                                                <th>Message</th>
                                                <th>Evidence</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            
                                            <tr>
                                                <td class="node-name">**************</td>
                                                <td><span class="status-badge status-pass">PASS</span></td>
                                                <td>✓ PASS: Kubelet read-only port is disabled</td>
                                                <td>Checking kubelet read-only port configuration...
✓ Read-only port 10255 is not listening

=== EVALUATION ===
Config found: false
Read-only port disabled: true
✓ PASS: Kubelet read-only port is disabled</td>
                                            </tr>
                                            
                                            <tr>
                                                <td class="node-name">**************</td>
                                                <td><span class="status-badge status-pass">PASS</span></td>
                                                <td>✓ PASS: Kubelet read-only port is disabled</td>
                                                <td>Checking kubelet read-only port configuration...
✓ Read-only port 10255 is not listening

=== EVALUATION ===
Config found: false
Read-only port disabled: true
✓ PASS: Kubelet read-only port is disabled</td>
                                            </tr>
                                            
                                            <tr>
                                                <td class="node-name">***************</td>
                                                <td><span class="status-badge status-pass">PASS</span></td>
                                                <td>✓ PASS: Kubelet read-only port is disabled</td>
                                                <td>Checking kubelet read-only port configuration...
✓ Read-only port 10255 is not listening

=== EVALUATION ===
Config found: false
Read-only port disabled: true
✓ PASS: Kubelet read-only port is disabled</td>
                                            </tr>
                                            
                                            <tr>
                                                <td class="node-name">***************</td>
                                                <td><span class="status-badge status-pass">PASS</span></td>
                                                <td>✓ PASS: Kubelet read-only port is disabled</td>
                                                <td>Checking kubelet read-only port configuration...
✓ Read-only port 10255 is not listening

=== EVALUATION ===
Config found: false
Read-only port disabled: true
✓ PASS: Kubelet read-only port is disabled</td>
                                            </tr>
                                            
                                        </tbody>
                                    </table>
                                </div>
                                
                            
                        </div>
                    </div>
                </div>
                
                
                <div class="finding-item">
                    <div class="finding-header" onclick="toggleFinding(this)">
                        <div class="finding-title-section">
                            <div class="finding-id">V-242416</div>
                            <div class="finding-title">The Kubernetes PKI directory and file(s) must be owned by root.</div>
                            <div class="finding-meta">
                                <span class="severity-badge severity-high">high</span>
                                <span class="status-badge status-pass-pass">PASS</span>
                                <span class="info-value">node</span>
                            </div>
                        </div>
                        <div class="expand-icon">▼</div>
                    </div>
                    <div class="finding-content">
                        <div class="finding-details">
                            <div class="detail-section">
                                <h4>Description</h4>
                                <p>The Kubernetes PKI directory contains all the public/private key pairs and certificates used by Kubernetes components. If an attacker can gain access to these files, changes can be made to open vulnerabilities and bypass user authorizations inherent within Kubernetes with RBAC implemented.
</p>
                            </div>
                            
                            
                            <div class="detail-section">
                                <h4>Check Text</h4>
                                <pre>On the Control Plane, run the command:
stat -c %U:%G /etc/kubernetes/pki/*

If any file is not owned by root:root, this is a finding.
</pre>
                            </div>
                            
                            
                            
                            <div class="detail-section">
                                <h4>Fix Text</h4>
                                <pre>On the Control Plane, run the command:
chown -R root:root /etc/kubernetes/pki/
</pre>
                            </div>
                            
                            
                            
                            <div class="detail-section">
                                <h4>Result Message</h4>
                                <p>WARNING: No PKI files found</p>
                            </div>
                            
                            
                            
                                
                                <div class="detail-section">
                                    <h4>Node Results</h4>
                                    <table class="node-results-table">
                                        <thead>
                                            <tr>
                                                <th>Node Name</th>
                                                <th>Status</th>
                                                <th>Message</th>
                                                <th>Evidence</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            
                                            <tr>
                                                <td class="node-name">***************</td>
                                                <td><span class="status-badge status-pass">PASS</span></td>
                                                <td>WARNING: No PKI files found</td>
                                                <td>Checking PKI directory and files ownership...
WARNING: No PKI files found</td>
                                            </tr>
                                            
                                            <tr>
                                                <td class="node-name">**************</td>
                                                <td><span class="status-badge status-pass">PASS</span></td>
                                                <td>WARNING: No PKI files found</td>
                                                <td>Checking PKI directory and files ownership...
WARNING: No PKI files found</td>
                                            </tr>
                                            
                                            <tr>
                                                <td class="node-name">***************</td>
                                                <td><span class="status-badge status-pass">PASS</span></td>
                                                <td>WARNING: No PKI files found</td>
                                                <td>Checking PKI directory and files ownership...
WARNING: No PKI files found</td>
                                            </tr>
                                            
                                            <tr>
                                                <td class="node-name">**************</td>
                                                <td><span class="status-badge status-pass">PASS</span></td>
                                                <td>WARNING: No PKI files found</td>
                                                <td>Checking PKI directory and files ownership...
WARNING: No PKI files found</td>
                                            </tr>
                                            
                                        </tbody>
                                    </table>
                                </div>
                                
                            
                        </div>
                    </div>
                </div>
                
                
                <div class="finding-item">
                    <div class="finding-header" onclick="toggleFinding(this)">
                        <div class="finding-title-section">
                            <div class="finding-id">V-242418</div>
                            <div class="finding-title">The Kubernetes PKI keys must have file permissions set to 600 or more restrictive.</div>
                            <div class="finding-meta">
                                <span class="severity-badge severity-high">high</span>
                                <span class="status-badge status-pass-pass">PASS</span>
                                <span class="info-value">node</span>
                            </div>
                        </div>
                        <div class="expand-icon">▼</div>
                    </div>
                    <div class="finding-content">
                        <div class="finding-details">
                            <div class="detail-section">
                                <h4>Description</h4>
                                <p>The Kubernetes PKI directory contains all the public/private key pairs and certificates used by Kubernetes components. Private key files contain sensitive cryptographic material and must be protected from unauthorized access. If an attacker can gain access to these files, they could potentially compromise the security of the entire cluster.
</p>
                            </div>
                            
                            
                            <div class="detail-section">
                                <h4>Check Text</h4>
                                <pre>On the Control Plane, run the command:
stat -c %a /etc/kubernetes/pki/*.key

If any key file has permissions more permissive than &#34;600&#34;, this is a finding.
</pre>
                            </div>
                            
                            
                            
                            <div class="detail-section">
                                <h4>Fix Text</h4>
                                <pre>On the Control Plane, run the command:
chmod 600 /etc/kubernetes/pki/*.key
</pre>
                            </div>
                            
                            
                            
                            <div class="detail-section">
                                <h4>Result Message</h4>
                                <p>WARNING: No PKI directories found to check</p>
                            </div>
                            
                            
                            
                                
                                <div class="detail-section">
                                    <h4>Node Results</h4>
                                    <table class="node-results-table">
                                        <thead>
                                            <tr>
                                                <th>Node Name</th>
                                                <th>Status</th>
                                                <th>Message</th>
                                                <th>Evidence</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            
                                            <tr>
                                                <td class="node-name">**************</td>
                                                <td><span class="status-badge status-pass">PASS</span></td>
                                                <td>WARNING: No PKI directories found to check</td>
                                                <td>Checking PKI private key file permissions...

=== EVALUATION ===
PKI directory found: false
Key files checked: 0
Violations found: false
WARNING: No PKI directories found to check</td>
                                            </tr>
                                            
                                            <tr>
                                                <td class="node-name">***************</td>
                                                <td><span class="status-badge status-pass">PASS</span></td>
                                                <td>WARNING: No PKI directories found to check</td>
                                                <td>Checking PKI private key file permissions...

=== EVALUATION ===
PKI directory found: false
Key files checked: 0
Violations found: false
WARNING: No PKI directories found to check</td>
                                            </tr>
                                            
                                            <tr>
                                                <td class="node-name">**************</td>
                                                <td><span class="status-badge status-pass">PASS</span></td>
                                                <td>WARNING: No PKI directories found to check</td>
                                                <td>Checking PKI private key file permissions...

=== EVALUATION ===
PKI directory found: false
Key files checked: 0
Violations found: false
WARNING: No PKI directories found to check</td>
                                            </tr>
                                            
                                            <tr>
                                                <td class="node-name">***************</td>
                                                <td><span class="status-badge status-pass">PASS</span></td>
                                                <td>WARNING: No PKI directories found to check</td>
                                                <td>Checking PKI private key file permissions...

=== EVALUATION ===
PKI directory found: false
Key files checked: 0
Violations found: false
WARNING: No PKI directories found to check</td>
                                            </tr>
                                            
                                        </tbody>
                                    </table>
                                </div>
                                
                            
                        </div>
                    </div>
                </div>
                
                
                <div class="finding-item">
                    <div class="finding-header" onclick="toggleFinding(this)">
                        <div class="finding-title-section">
                            <div class="finding-id">V-242410</div>
                            <div class="finding-title">The Kubernetes admin.conf must be owned by root.</div>
                            <div class="finding-meta">
                                <span class="severity-badge severity-medium">medium</span>
                                <span class="status-badge status-pass-pass">PASS</span>
                                <span class="info-value">node</span>
                            </div>
                        </div>
                        <div class="expand-icon">▼</div>
                    </div>
                    <div class="finding-content">
                        <div class="finding-details">
                            <div class="detail-section">
                                <h4>Description</h4>
                                <p>The admin.conf is the administrator kubeconfig file defining administrator user, base64-encoded certificate, and cluster server location. This configuration file contains the credentials necessary to access the cluster as an administrator. If an attacker can gain access to this file, changes can be made to open vulnerabilities and bypass user authorizations inherent within Kubernetes with RBAC implemented.
</p>
                            </div>
                            
                            
                            <div class="detail-section">
                                <h4>Check Text</h4>
                                <pre>On the Control Plane, run the command:
stat -c %U:%G /etc/kubernetes/admin.conf

If the file is not owned by root:root, this is a finding.
</pre>
                            </div>
                            
                            
                            
                            <div class="detail-section">
                                <h4>Fix Text</h4>
                                <pre>On the Control Plane, run the command:
chown root:root /etc/kubernetes/admin.conf
</pre>
                            </div>
                            
                            
                            
                            <div class="detail-section">
                                <h4>Result Message</h4>
                                <p>WARNING: No admin configuration files found</p>
                            </div>
                            
                            
                            
                                
                                <div class="detail-section">
                                    <h4>Node Results</h4>
                                    <table class="node-results-table">
                                        <thead>
                                            <tr>
                                                <th>Node Name</th>
                                                <th>Status</th>
                                                <th>Message</th>
                                                <th>Evidence</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            
                                            <tr>
                                                <td class="node-name">***************</td>
                                                <td><span class="status-badge status-pass">PASS</span></td>
                                                <td>WARNING: No admin configuration files found</td>
                                                <td>Checking admin.conf file ownership...
WARNING: No admin configuration files found</td>
                                            </tr>
                                            
                                            <tr>
                                                <td class="node-name">**************</td>
                                                <td><span class="status-badge status-pass">PASS</span></td>
                                                <td>WARNING: No admin configuration files found</td>
                                                <td>Checking admin.conf file ownership...
WARNING: No admin configuration files found</td>
                                            </tr>
                                            
                                            <tr>
                                                <td class="node-name">***************</td>
                                                <td><span class="status-badge status-pass">PASS</span></td>
                                                <td>WARNING: No admin configuration files found</td>
                                                <td>Checking admin.conf file ownership...
WARNING: No admin configuration files found</td>
                                            </tr>
                                            
                                            <tr>
                                                <td class="node-name">**************</td>
                                                <td><span class="status-badge status-pass">PASS</span></td>
                                                <td>WARNING: No admin configuration files found</td>
                                                <td>Checking admin.conf file ownership...
WARNING: No admin configuration files found</td>
                                            </tr>
                                            
                                        </tbody>
                                    </table>
                                </div>
                                
                            
                        </div>
                    </div>
                </div>
                
                
                <div class="finding-item">
                    <div class="finding-header" onclick="toggleFinding(this)">
                        <div class="finding-title-section">
                            <div class="finding-id">V-242411</div>
                            <div class="finding-title">The Kubernetes admin.conf must have file permissions set to 644 or more restrictive.</div>
                            <div class="finding-meta">
                                <span class="severity-badge severity-medium">medium</span>
                                <span class="status-badge status-pass-pass">PASS</span>
                                <span class="info-value">node</span>
                            </div>
                        </div>
                        <div class="expand-icon">▼</div>
                    </div>
                    <div class="finding-content">
                        <div class="finding-details">
                            <div class="detail-section">
                                <h4>Description</h4>
                                <p>The admin.conf is the administrator kubeconfig file defining administrator user, base64-encoded certificate, and cluster server location. This configuration file contains the credentials necessary to access the cluster as an administrator. If an attacker can gain access to this file, changes can be made to open vulnerabilities and bypass user authorizations inherent within Kubernetes with RBAC implemented.
</p>
                            </div>
                            
                            
                            <div class="detail-section">
                                <h4>Check Text</h4>
                                <pre>On the Control Plane, run the command:
stat -c %a /etc/kubernetes/admin.conf

If the file has permissions more permissive than &#34;644&#34;, this is a finding.
</pre>
                            </div>
                            
                            
                            
                            <div class="detail-section">
                                <h4>Fix Text</h4>
                                <pre>On the Control Plane, run the command:
chmod 644 /etc/kubernetes/admin.conf
</pre>
                            </div>
                            
                            
                            
                            <div class="detail-section">
                                <h4>Result Message</h4>
                                <p>WARNING: No admin configuration files found</p>
                            </div>
                            
                            
                            
                                
                                <div class="detail-section">
                                    <h4>Node Results</h4>
                                    <table class="node-results-table">
                                        <thead>
                                            <tr>
                                                <th>Node Name</th>
                                                <th>Status</th>
                                                <th>Message</th>
                                                <th>Evidence</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            
                                            <tr>
                                                <td class="node-name">**************</td>
                                                <td><span class="status-badge status-pass">PASS</span></td>
                                                <td>WARNING: No admin configuration files found</td>
                                                <td>Checking admin.conf file permissions...
WARNING: No admin configuration files found</td>
                                            </tr>
                                            
                                            <tr>
                                                <td class="node-name">***************</td>
                                                <td><span class="status-badge status-pass">PASS</span></td>
                                                <td>WARNING: No admin configuration files found</td>
                                                <td>Checking admin.conf file permissions...
WARNING: No admin configuration files found</td>
                                            </tr>
                                            
                                            <tr>
                                                <td class="node-name">**************</td>
                                                <td><span class="status-badge status-pass">PASS</span></td>
                                                <td>WARNING: No admin configuration files found</td>
                                                <td>Checking admin.conf file permissions...
WARNING: No admin configuration files found</td>
                                            </tr>
                                            
                                            <tr>
                                                <td class="node-name">***************</td>
                                                <td><span class="status-badge status-pass">PASS</span></td>
                                                <td>WARNING: No admin configuration files found</td>
                                                <td>Checking admin.conf file permissions...
WARNING: No admin configuration files found</td>
                                            </tr>
                                            
                                        </tbody>
                                    </table>
                                </div>
                                
                            
                        </div>
                    </div>
                </div>
                
                
                <div class="finding-item">
                    <div class="finding-header" onclick="toggleFinding(this)">
                        <div class="finding-title-section">
                            <div class="finding-id">V-242414</div>
                            <div class="finding-title">The Kubernetes controller-manager.conf must be owned by root.</div>
                            <div class="finding-meta">
                                <span class="severity-badge severity-medium">medium</span>
                                <span class="status-badge status-pass-pass">PASS</span>
                                <span class="info-value">node</span>
                            </div>
                        </div>
                        <div class="expand-icon">▼</div>
                    </div>
                    <div class="finding-content">
                        <div class="finding-details">
                            <div class="detail-section">
                                <h4>Description</h4>
                                <p>The controller-manager.conf is the kubeconfig file for the Controller Manager. The Controller Manager is responsible for running controller processes. If an attacker can gain access to this file, changes can be made to open vulnerabilities and bypass user authorizations inherent within Kubernetes with RBAC implemented.
</p>
                            </div>
                            
                            
                            <div class="detail-section">
                                <h4>Check Text</h4>
                                <pre>On the Control Plane, run the command:
stat -c %U:%G /etc/kubernetes/controller-manager.conf

If the file is not owned by root:root, this is a finding.
</pre>
                            </div>
                            
                            
                            
                            <div class="detail-section">
                                <h4>Fix Text</h4>
                                <pre>On the Control Plane, run the command:
chown root:root /etc/kubernetes/controller-manager.conf
</pre>
                            </div>
                            
                            
                            
                            <div class="detail-section">
                                <h4>Result Message</h4>
                                <p>WARNING: No controller-manager configuration files found</p>
                            </div>
                            
                            
                            
                                
                                <div class="detail-section">
                                    <h4>Node Results</h4>
                                    <table class="node-results-table">
                                        <thead>
                                            <tr>
                                                <th>Node Name</th>
                                                <th>Status</th>
                                                <th>Message</th>
                                                <th>Evidence</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            
                                            <tr>
                                                <td class="node-name">***************</td>
                                                <td><span class="status-badge status-pass">PASS</span></td>
                                                <td>WARNING: No controller-manager configuration files found</td>
                                                <td>Checking controller-manager.conf file ownership...
WARNING: No controller-manager configuration files found</td>
                                            </tr>
                                            
                                            <tr>
                                                <td class="node-name">**************</td>
                                                <td><span class="status-badge status-pass">PASS</span></td>
                                                <td>WARNING: No controller-manager configuration files found</td>
                                                <td>Checking controller-manager.conf file ownership...
WARNING: No controller-manager configuration files found</td>
                                            </tr>
                                            
                                            <tr>
                                                <td class="node-name">***************</td>
                                                <td><span class="status-badge status-pass">PASS</span></td>
                                                <td>WARNING: No controller-manager configuration files found</td>
                                                <td>Checking controller-manager.conf file ownership...
WARNING: No controller-manager configuration files found</td>
                                            </tr>
                                            
                                            <tr>
                                                <td class="node-name">**************</td>
                                                <td><span class="status-badge status-pass">PASS</span></td>
                                                <td>WARNING: No controller-manager configuration files found</td>
                                                <td>Checking controller-manager.conf file ownership...
WARNING: No controller-manager configuration files found</td>
                                            </tr>
                                            
                                        </tbody>
                                    </table>
                                </div>
                                
                            
                        </div>
                    </div>
                </div>
                
                
                <div class="finding-item">
                    <div class="finding-header" onclick="toggleFinding(this)">
                        <div class="finding-title-section">
                            <div class="finding-id">V-242415</div>
                            <div class="finding-title">The Kubernetes controller-manager.conf must have file permissions set to 644 or more restrictive.</div>
                            <div class="finding-meta">
                                <span class="severity-badge severity-medium">medium</span>
                                <span class="status-badge status-pass-pass">PASS</span>
                                <span class="info-value">node</span>
                            </div>
                        </div>
                        <div class="expand-icon">▼</div>
                    </div>
                    <div class="finding-content">
                        <div class="finding-details">
                            <div class="detail-section">
                                <h4>Description</h4>
                                <p>The controller-manager.conf is the kubeconfig file for the Controller Manager. The Controller Manager is responsible for running controller processes. If an attacker can gain access to this file, changes can be made to open vulnerabilities and bypass user authorizations inherent within Kubernetes with RBAC implemented.
</p>
                            </div>
                            
                            
                            <div class="detail-section">
                                <h4>Check Text</h4>
                                <pre>On the Control Plane, run the command:
stat -c %a /etc/kubernetes/controller-manager.conf

If the file has permissions more permissive than &#34;644&#34;, this is a finding.
</pre>
                            </div>
                            
                            
                            
                            <div class="detail-section">
                                <h4>Fix Text</h4>
                                <pre>On the Control Plane, run the command:
chmod 644 /etc/kubernetes/controller-manager.conf
</pre>
                            </div>
                            
                            
                            
                            <div class="detail-section">
                                <h4>Result Message</h4>
                                <p>WARNING: No controller-manager configuration files found</p>
                            </div>
                            
                            
                            
                                
                                <div class="detail-section">
                                    <h4>Node Results</h4>
                                    <table class="node-results-table">
                                        <thead>
                                            <tr>
                                                <th>Node Name</th>
                                                <th>Status</th>
                                                <th>Message</th>
                                                <th>Evidence</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            
                                            <tr>
                                                <td class="node-name">**************</td>
                                                <td><span class="status-badge status-pass">PASS</span></td>
                                                <td>WARNING: No controller-manager configuration files found</td>
                                                <td>Checking controller-manager.conf file permissions...
WARNING: No controller-manager configuration files found</td>
                                            </tr>
                                            
                                            <tr>
                                                <td class="node-name">**************</td>
                                                <td><span class="status-badge status-pass">PASS</span></td>
                                                <td>WARNING: No controller-manager configuration files found</td>
                                                <td>Checking controller-manager.conf file permissions...
WARNING: No controller-manager configuration files found</td>
                                            </tr>
                                            
                                            <tr>
                                                <td class="node-name">***************</td>
                                                <td><span class="status-badge status-pass">PASS</span></td>
                                                <td>WARNING: No controller-manager configuration files found</td>
                                                <td>Checking controller-manager.conf file permissions...
WARNING: No controller-manager configuration files found</td>
                                            </tr>
                                            
                                            <tr>
                                                <td class="node-name">***************</td>
                                                <td><span class="status-badge status-pass">PASS</span></td>
                                                <td>WARNING: No controller-manager configuration files found</td>
                                                <td>Checking controller-manager.conf file permissions...
WARNING: No controller-manager configuration files found</td>
                                            </tr>
                                            
                                        </tbody>
                                    </table>
                                </div>
                                
                            
                        </div>
                    </div>
                </div>
                
                
                <div class="finding-item">
                    <div class="finding-header" onclick="toggleFinding(this)">
                        <div class="finding-title-section">
                            <div class="finding-id">V-242424</div>
                            <div class="finding-title">The Kubernetes etcd must be owned by etcd.</div>
                            <div class="finding-meta">
                                <span class="severity-badge severity-medium">medium</span>
                                <span class="status-badge status-pass-pass">PASS</span>
                                <span class="info-value">node</span>
                            </div>
                        </div>
                        <div class="expand-icon">▼</div>
                    </div>
                    <div class="finding-content">
                        <div class="finding-details">
                            <div class="detail-section">
                                <h4>Description</h4>
                                <p>The etcd database stores the state of the cluster. If an attacker can gain access to the etcd database, changes can be made to open vulnerabilities and bypass user authorizations inherent within Kubernetes with RBAC implemented. The etcd database should be owned by etcd to ensure proper access controls.
</p>
                            </div>
                            
                            
                            <div class="detail-section">
                                <h4>Check Text</h4>
                                <pre>Determine the etcd data directory by running the following command:
ps -ef | grep etcd

Note the data directory (identified by --data-dir).

Run the command:
stat -c %U:%G &lt;etcd_data_directory&gt;

If the directory is not owned by etcd:etcd, this is a finding.
</pre>
                            </div>
                            
                            
                            
                            <div class="detail-section">
                                <h4>Fix Text</h4>
                                <pre>Run the command:
chown etcd:etcd &lt;etcd_data_directory&gt;

To verify the change took place, run the command:
stat -c %U:%G &lt;etcd_data_directory&gt;
</pre>
                            </div>
                            
                            
                            
                            <div class="detail-section">
                                <h4>Result Message</h4>
                                <p>WARNING: No etcd data directories found</p>
                            </div>
                            
                            
                            
                                
                                <div class="detail-section">
                                    <h4>Node Results</h4>
                                    <table class="node-results-table">
                                        <thead>
                                            <tr>
                                                <th>Node Name</th>
                                                <th>Status</th>
                                                <th>Message</th>
                                                <th>Evidence</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            
                                            <tr>
                                                <td class="node-name">**************</td>
                                                <td><span class="status-badge status-pass">PASS</span></td>
                                                <td>WARNING: No etcd data directories found</td>
                                                <td>Checking etcd data directory ownership...
WARNING: No etcd data directories found</td>
                                            </tr>
                                            
                                            <tr>
                                                <td class="node-name">***************</td>
                                                <td><span class="status-badge status-pass">PASS</span></td>
                                                <td>WARNING: No etcd data directories found</td>
                                                <td>Checking etcd data directory ownership...
WARNING: No etcd data directories found</td>
                                            </tr>
                                            
                                            <tr>
                                                <td class="node-name">***************</td>
                                                <td><span class="status-badge status-pass">PASS</span></td>
                                                <td>WARNING: No etcd data directories found</td>
                                                <td>Checking etcd data directory ownership...
WARNING: No etcd data directories found</td>
                                            </tr>
                                            
                                            <tr>
                                                <td class="node-name">**************</td>
                                                <td><span class="status-badge status-pass">PASS</span></td>
                                                <td>WARNING: No etcd data directories found</td>
                                                <td>Checking etcd data directory ownership...
WARNING: No etcd data directories found</td>
                                            </tr>
                                            
                                        </tbody>
                                    </table>
                                </div>
                                
                            
                        </div>
                    </div>
                </div>
                
                
                <div class="finding-item">
                    <div class="finding-header" onclick="toggleFinding(this)">
                        <div class="finding-title-section">
                            <div class="finding-id">V-242425</div>
                            <div class="finding-title">The Kubernetes etcd must have file permissions set to 644 or more restrictive.</div>
                            <div class="finding-meta">
                                <span class="severity-badge severity-medium">medium</span>
                                <span class="status-badge status-pass-pass">PASS</span>
                                <span class="info-value">node</span>
                            </div>
                        </div>
                        <div class="expand-icon">▼</div>
                    </div>
                    <div class="finding-content">
                        <div class="finding-details">
                            <div class="detail-section">
                                <h4>Description</h4>
                                <p>The etcd database stores the state of the cluster. If an attacker can gain access to the etcd database, changes can be made to open vulnerabilities and bypass user authorizations inherent within Kubernetes with RBAC implemented. The etcd data directory should have restrictive permissions to prevent unauthorized access.
</p>
                            </div>
                            
                            
                            <div class="detail-section">
                                <h4>Check Text</h4>
                                <pre>Determine the etcd data directory by running the following command:
ps -ef | grep etcd

Note the data directory (identified by --data-dir).

Run the command:
stat -c %a &lt;etcd_data_directory&gt;

If the directory has permissions more permissive than &#34;700&#34;, this is a finding.
</pre>
                            </div>
                            
                            
                            
                            <div class="detail-section">
                                <h4>Fix Text</h4>
                                <pre>Run the command:
chmod 700 &lt;etcd_data_directory&gt;

To verify the change took place, run the command:
stat -c %a &lt;etcd_data_directory&gt;
</pre>
                            </div>
                            
                            
                            
                            <div class="detail-section">
                                <h4>Result Message</h4>
                                <p>WARNING: No etcd data directories found</p>
                            </div>
                            
                            
                            
                                
                                <div class="detail-section">
                                    <h4>Node Results</h4>
                                    <table class="node-results-table">
                                        <thead>
                                            <tr>
                                                <th>Node Name</th>
                                                <th>Status</th>
                                                <th>Message</th>
                                                <th>Evidence</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            
                                            <tr>
                                                <td class="node-name">***************</td>
                                                <td><span class="status-badge status-pass">PASS</span></td>
                                                <td>WARNING: No etcd data directories found</td>
                                                <td>Checking etcd data directory permissions...
WARNING: No etcd data directories found</td>
                                            </tr>
                                            
                                            <tr>
                                                <td class="node-name">**************</td>
                                                <td><span class="status-badge status-pass">PASS</span></td>
                                                <td>WARNING: No etcd data directories found</td>
                                                <td>Checking etcd data directory permissions...
WARNING: No etcd data directories found</td>
                                            </tr>
                                            
                                            <tr>
                                                <td class="node-name">**************</td>
                                                <td><span class="status-badge status-pass">PASS</span></td>
                                                <td>WARNING: No etcd data directories found</td>
                                                <td>Checking etcd data directory permissions...
WARNING: No etcd data directories found</td>
                                            </tr>
                                            
                                            <tr>
                                                <td class="node-name">***************</td>
                                                <td><span class="status-badge status-pass">PASS</span></td>
                                                <td>WARNING: No etcd data directories found</td>
                                                <td>Checking etcd data directory permissions...
WARNING: No etcd data directories found</td>
                                            </tr>
                                            
                                        </tbody>
                                    </table>
                                </div>
                                
                            
                        </div>
                    </div>
                </div>
                
                
                <div class="finding-item">
                    <div class="finding-header" onclick="toggleFinding(this)">
                        <div class="finding-title-section">
                            <div class="finding-id">V-242420</div>
                            <div class="finding-title">Kubernetes Kubelet must have the SSL Certificate Authority set.</div>
                            <div class="finding-meta">
                                <span class="severity-badge severity-medium">medium</span>
                                <span class="status-badge status-pass-pass">PASS</span>
                                <span class="info-value">node</span>
                            </div>
                        </div>
                        <div class="expand-icon">▼</div>
                    </div>
                    <div class="finding-content">
                        <div class="finding-details">
                            <div class="detail-section">
                                <h4>Description</h4>
                                <p>Kubernetes container and pod configuration are maintained by Kubelet. Kubelet agents register nodes with the API Server, mount volume storage, and perform health checks for containers and pods. Anyone who gains access to Kubelet agents can effectively control applications within the pods and containers. Using authenticity protection, the communication can be protected against man-in-the-middle attacks/session hijacking and the insertion of false information into sessions.

The communication session is protected by utilizing transport encryption protocols such as TLS. TLS provides the Kubernetes API Server with a means to authenticate sessions and encrypt traffic.

To enable encrypted communication for Kubelet, the clientCAFile must be set. This parameter gives the location of the SSL Certificate Authority file used to secure Kubelet communication.
</p>
                            </div>
                            
                            
                            <div class="detail-section">
                                <h4>Check Text</h4>
                                <pre>On the Control Plane, run the command:
ps -ef | grep kubelet

If the &#34;--client-ca-file&#34; option exists, this is a finding.

Note the path to the config file (identified by --config).

Run the command:
grep -i clientCAFile &lt;path_to_config_file&gt;

If the setting &#34;clientCAFile&#34; is not set or contains no value, this is a finding.
</pre>
                            </div>
                            
                            
                            
                            <div class="detail-section">
                                <h4>Fix Text</h4>
                                <pre>On the Control Plane, run the command:
ps -ef | grep kubelet

Remove the &#34;--client-ca-file&#34; option if present.

Note the path to the config file (identified by --config).

Edit the Kubernetes Kubelet config file: 
Set the value of &#34;clientCAFile&#34; to a path containing an Approved Organizational Certificate. 

Restart the kubelet service using the following command:
systemctl daemon-reload &amp;&amp; systemctl restart kubelet
</pre>
                            </div>
                            
                            
                            
                            <div class="detail-section">
                                <h4>Result Message</h4>
                                <p>FAIL: No kubelet configuration found</p>
                            </div>
                            
                            
                            
                                
                                <div class="detail-section">
                                    <h4>Node Results</h4>
                                    <table class="node-results-table">
                                        <thead>
                                            <tr>
                                                <th>Node Name</th>
                                                <th>Status</th>
                                                <th>Message</th>
                                                <th>Evidence</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            
                                            <tr>
                                                <td class="node-name">***************</td>
                                                <td><span class="status-badge status-pass">PASS</span></td>
                                                <td>FAIL: No kubelet configuration found</td>
                                                <td>Checking kubelet client CA file configuration...
=== EVALUATION ===
Command line option found: false
Config found: false
Client CA configured: false
FAIL: No kubelet configuration found</td>
                                            </tr>
                                            
                                            <tr>
                                                <td class="node-name">***************</td>
                                                <td><span class="status-badge status-pass">PASS</span></td>
                                                <td>FAIL: No kubelet configuration found</td>
                                                <td>Checking kubelet client CA file configuration...
=== EVALUATION ===
Command line option found: false
Config found: false
Client CA configured: false
FAIL: No kubelet configuration found</td>
                                            </tr>
                                            
                                            <tr>
                                                <td class="node-name">**************</td>
                                                <td><span class="status-badge status-pass">PASS</span></td>
                                                <td>FAIL: No kubelet configuration found</td>
                                                <td>Checking kubelet client CA file configuration...
=== EVALUATION ===
Command line option found: false
Config found: false
Client CA configured: false
FAIL: No kubelet configuration found</td>
                                            </tr>
                                            
                                            <tr>
                                                <td class="node-name">**************</td>
                                                <td><span class="status-badge status-pass">PASS</span></td>
                                                <td>FAIL: No kubelet configuration found</td>
                                                <td>Checking kubelet client CA file configuration...
=== EVALUATION ===
Command line option found: false
Config found: false
Client CA configured: false
FAIL: No kubelet configuration found</td>
                                            </tr>
                                            
                                        </tbody>
                                    </table>
                                </div>
                                
                            
                        </div>
                    </div>
                </div>
                
                
                <div class="finding-item">
                    <div class="finding-header" onclick="toggleFinding(this)">
                        <div class="finding-title-section">
                            <div class="finding-id">V-242406</div>
                            <div class="finding-title">The Kubernetes KubeletConfiguration file must be owned by root.</div>
                            <div class="finding-meta">
                                <span class="severity-badge severity-medium">medium</span>
                                <span class="status-badge status-pass-pass">PASS</span>
                                <span class="info-value">node</span>
                            </div>
                        </div>
                        <div class="expand-icon">▼</div>
                    </div>
                    <div class="finding-content">
                        <div class="finding-details">
                            <div class="detail-section">
                                <h4>Description</h4>
                                <p>The kubelet configuration file contains the runtime configuration of the kubelet service. If an attacker can gain access to this file, changes can be made to open vulnerabilities and bypass user authorizations inherent within Kubernetes with RBAC implemented.
</p>
                            </div>
                            
                            
                            <div class="detail-section">
                                <h4>Check Text</h4>
                                <pre>On the Kubernetes Control Plane and Worker nodes, run the command:
ps -ef | grep kubelet

Check the config file (path identified by: --config):

Change to the directory identified by --config (example /etc/sysconfig/) run the command:
ls -l kubelet

Each kubelet configuration file must be owned by root:root.

If any manifest file is not owned by root:root, this is a finding.
</pre>
                            </div>
                            
                            
                            
                            <div class="detail-section">
                                <h4>Fix Text</h4>
                                <pre>On the Control Plane and Worker nodes, change to the --config directory. Run the command:
chown root:root kubelet

To verify the change took place, run the command:
ls -l kubelet

The kubelet file should now be owned by root:root.
</pre>
                            </div>
                            
                            
                            
                            <div class="detail-section">
                                <h4>Result Message</h4>
                                <p>WARNING: No kubelet configuration files found</p>
                            </div>
                            
                            
                            
                                
                                <div class="detail-section">
                                    <h4>Node Results</h4>
                                    <table class="node-results-table">
                                        <thead>
                                            <tr>
                                                <th>Node Name</th>
                                                <th>Status</th>
                                                <th>Message</th>
                                                <th>Evidence</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            
                                            <tr>
                                                <td class="node-name">***************</td>
                                                <td><span class="status-badge status-pass">PASS</span></td>
                                                <td>WARNING: No kubelet configuration files found</td>
                                                <td>Checking kubelet configuration file ownership...
WARNING: No kubelet configuration files found</td>
                                            </tr>
                                            
                                            <tr>
                                                <td class="node-name">***************</td>
                                                <td><span class="status-badge status-pass">PASS</span></td>
                                                <td>WARNING: No kubelet configuration files found</td>
                                                <td>Checking kubelet configuration file ownership...
WARNING: No kubelet configuration files found</td>
                                            </tr>
                                            
                                            <tr>
                                                <td class="node-name">**************</td>
                                                <td><span class="status-badge status-pass">PASS</span></td>
                                                <td>WARNING: No kubelet configuration files found</td>
                                                <td>Checking kubelet configuration file ownership...
WARNING: No kubelet configuration files found</td>
                                            </tr>
                                            
                                            <tr>
                                                <td class="node-name">**************</td>
                                                <td><span class="status-badge status-pass">PASS</span></td>
                                                <td>WARNING: No kubelet configuration files found</td>
                                                <td>Checking kubelet configuration file ownership...
WARNING: No kubelet configuration files found</td>
                                            </tr>
                                            
                                        </tbody>
                                    </table>
                                </div>
                                
                            
                        </div>
                    </div>
                </div>
                
                
                <div class="finding-item">
                    <div class="finding-header" onclick="toggleFinding(this)">
                        <div class="finding-title-section">
                            <div class="finding-id">V-242407</div>
                            <div class="finding-title">The Kubernetes KubeletConfiguration files must have file permissions set to 644 or more restrictive.</div>
                            <div class="finding-meta">
                                <span class="severity-badge severity-medium">medium</span>
                                <span class="status-badge status-pass-pass">PASS</span>
                                <span class="info-value">node</span>
                            </div>
                        </div>
                        <div class="expand-icon">▼</div>
                    </div>
                    <div class="finding-content">
                        <div class="finding-details">
                            <div class="detail-section">
                                <h4>Description</h4>
                                <p>The kubelet configuration file contains the runtime configuration of the kubelet service. If an attacker can gain access to this file, changes can be made to open vulnerabilities and bypass user authorizations inherit within Kubernetes with RBAC implemented.
</p>
                            </div>
                            
                            
                            <div class="detail-section">
                                <h4>Check Text</h4>
                                <pre>On the Kubernetes Control Plane and Worker nodes, run the command:
ps -ef | grep kubelet

Check the config file (path identified by: --config):

Change to the directory identified by --config (example /etc/sysconfig/) and run the command:
ls -l kubelet

Each KubeletConfiguration file must have permissions of &#34;644&#34; or more restrictive.

If any KubeletConfiguration file is less restrictive than &#34;644&#34;, this is a finding.
</pre>
                            </div>
                            
                            
                            
                            <div class="detail-section">
                                <h4>Fix Text</h4>
                                <pre>On the Kubernetes Control Plane and Worker nodes, run the command:
ps -ef | grep kubelet

Check the config file (path identified by: --config):

Change to the directory identified by --config (example /etc/sysconfig/) and run the command:
chmod 644 kubelet

To verify the change took place, run the command:
ls -l kubelet

The kubelet file should now have the permissions of &#34;644&#34;.
</pre>
                            </div>
                            
                            
                            
                            <div class="detail-section">
                                <h4>Result Message</h4>
                                <p>WARNING: No kubelet configuration files found</p>
                            </div>
                            
                            
                            
                                
                                <div class="detail-section">
                                    <h4>Node Results</h4>
                                    <table class="node-results-table">
                                        <thead>
                                            <tr>
                                                <th>Node Name</th>
                                                <th>Status</th>
                                                <th>Message</th>
                                                <th>Evidence</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            
                                            <tr>
                                                <td class="node-name">***************</td>
                                                <td><span class="status-badge status-pass">PASS</span></td>
                                                <td>WARNING: No kubelet configuration files found</td>
                                                <td>Checking kubelet configuration file permissions...
WARNING: No kubelet configuration files found</td>
                                            </tr>
                                            
                                            <tr>
                                                <td class="node-name">**************</td>
                                                <td><span class="status-badge status-pass">PASS</span></td>
                                                <td>WARNING: No kubelet configuration files found</td>
                                                <td>Checking kubelet configuration file permissions...
WARNING: No kubelet configuration files found</td>
                                            </tr>
                                            
                                            <tr>
                                                <td class="node-name">***************</td>
                                                <td><span class="status-badge status-pass">PASS</span></td>
                                                <td>WARNING: No kubelet configuration files found</td>
                                                <td>Checking kubelet configuration file permissions...
WARNING: No kubelet configuration files found</td>
                                            </tr>
                                            
                                            <tr>
                                                <td class="node-name">**************</td>
                                                <td><span class="status-badge status-pass">PASS</span></td>
                                                <td>WARNING: No kubelet configuration files found</td>
                                                <td>Checking kubelet configuration file permissions...
WARNING: No kubelet configuration files found</td>
                                            </tr>
                                            
                                        </tbody>
                                    </table>
                                </div>
                                
                            
                        </div>
                    </div>
                </div>
                
                
                <div class="finding-item">
                    <div class="finding-header" onclick="toggleFinding(this)">
                        <div class="finding-title-section">
                            <div class="finding-id">V-242404</div>
                            <div class="finding-title">Kubernetes Kubelet must deny hostname override.</div>
                            <div class="finding-meta">
                                <span class="severity-badge severity-medium">medium</span>
                                <span class="status-badge status-pass-pass">PASS</span>
                                <span class="info-value">node</span>
                            </div>
                        </div>
                        <div class="expand-icon">▼</div>
                    </div>
                    <div class="finding-content">
                        <div class="finding-details">
                            <div class="detail-section">
                                <h4>Description</h4>
                                <p>Kubernetes allows for the overriding of hostnames. Allowing this feature to be implemented within the kubelets may break the TLS setup between the kubelet service and the API server. This setting also can make it difficult to associate logs with nodes if security analytics needs to take place. The better practice is to setup nodes with resolvable FQDNs and avoid overriding the hostnames.
</p>
                            </div>
                            
                            
                            <div class="detail-section">
                                <h4>Check Text</h4>
                                <pre>On the Control Plane and Worker nodes, run the command:
ps -ef | grep kubelet

If the option &#34;--hostname-override&#34; is present, this is a finding.
</pre>
                            </div>
                            
                            
                            
                            <div class="detail-section">
                                <h4>Fix Text</h4>
                                <pre>Run the command:  
systemctl status kubelet.  
Note the path to the drop-in file.

Determine the path to the environment file(s) with the command: 
grep -i EnvironmentFile &lt;path_to_drop_in_file&gt;.

Remove the &#34;--hostname-override&#34; option from any environment file where it is present.  

Restart the kubelet service using the following command:
systemctl daemon-reload &amp;&amp; systemctl restart kubelet
</pre>
                            </div>
                            
                            
                            
                            <div class="detail-section">
                                <h4>Result Message</h4>
                                <p>PASS: No kubelet hostname override found</p>
                            </div>
                            
                            
                            
                                
                                <div class="detail-section">
                                    <h4>Node Results</h4>
                                    <table class="node-results-table">
                                        <thead>
                                            <tr>
                                                <th>Node Name</th>
                                                <th>Status</th>
                                                <th>Message</th>
                                                <th>Evidence</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            
                                            <tr>
                                                <td class="node-name">***************</td>
                                                <td><span class="status-badge status-pass">PASS</span></td>
                                                <td>PASS: No kubelet hostname override found</td>
                                                <td>Checking kubelet hostname override configuration...
PASS: No kubelet hostname override found</td>
                                            </tr>
                                            
                                            <tr>
                                                <td class="node-name">***************</td>
                                                <td><span class="status-badge status-pass">PASS</span></td>
                                                <td>PASS: No kubelet hostname override found</td>
                                                <td>Checking kubelet hostname override configuration...
PASS: No kubelet hostname override found</td>
                                            </tr>
                                            
                                            <tr>
                                                <td class="node-name">**************</td>
                                                <td><span class="status-badge status-pass">PASS</span></td>
                                                <td>PASS: No kubelet hostname override found</td>
                                                <td>Checking kubelet hostname override configuration...
PASS: No kubelet hostname override found</td>
                                            </tr>
                                            
                                            <tr>
                                                <td class="node-name">**************</td>
                                                <td><span class="status-badge status-pass">PASS</span></td>
                                                <td>PASS: No kubelet hostname override found</td>
                                                <td>Checking kubelet hostname override configuration...
PASS: No kubelet hostname override found</td>
                                            </tr>
                                            
                                        </tbody>
                                    </table>
                                </div>
                                
                            
                        </div>
                    </div>
                </div>
                
                
                <div class="finding-item">
                    <div class="finding-header" onclick="toggleFinding(this)">
                        <div class="finding-title-section">
                            <div class="finding-id">V-242435</div>
                            <div class="finding-title">The Kubernetes kubelet must enable streaming connection timeout.</div>
                            <div class="finding-meta">
                                <span class="severity-badge severity-medium">medium</span>
                                <span class="status-badge status-pass-pass">PASS</span>
                                <span class="info-value">node</span>
                            </div>
                        </div>
                        <div class="expand-icon">▼</div>
                    </div>
                    <div class="finding-content">
                        <div class="finding-details">
                            <div class="detail-section">
                                <h4>Description</h4>
                                <p>Streaming connections that are not properly configured can consume system resources and can be used to cause a denial of service attack. Streaming connections must be terminated after a period of inactivity.

The kubelet&#39;s streaming connection idle timeout ensures that streaming connections (such as kubectl exec, kubectl logs, kubectl port-forward) are automatically closed after a period of inactivity. This prevents resource exhaustion and potential denial of service attacks.

Setting streamingConnectionIdleTimeout to 0 disables the timeout, which is not recommended for production environments.
</p>
                            </div>
                            
                            
                            <div class="detail-section">
                                <h4>Check Text</h4>
                                <pre>On the Control Plane and Worker Nodes, run the command:
ps -ef | grep kubelet

Check the config file(s) referenced by the --config argument (example: --config=/etc/kubernetes/kubelet/kubelet-config.yaml):

Change to the directory identified by --config argument and run the command:
grep -i streamingconnectionidletimeout *

If the setting streamingConnectionIdleTimeout is not configured or is set to \&#34;0\&#34;, this is a finding.

If the setting streamingConnectionIdleTimeout is not configured, check if there is an entry for \&#34;--streaming-connection-idle-timeout\&#34; in the kubelet command.

On the Control Plane and Worker Nodes, run the command:
ps -ef | grep kubelet

If \&#34;--streaming-connection-idle-timeout\&#34; is not configured or is set to \&#34;0\&#34;, this is a finding.
</pre>
                            </div>
                            
                            
                            
                            <div class="detail-section">
                                <h4>Fix Text</h4>
                                <pre>On the Control Plane and Worker Nodes, run the command:
ps -ef | grep kubelet

Check the config file(s) referenced by the --config argument (example: --config=/etc/kubernetes/kubelet/kubelet-config.yaml):

Edit the Kubernetes Kubelet config file:
Set the value of streamingConnectionIdleTimeout to an appropriate timeout value (e.g., \&#34;5m\&#34; for 5 minutes).

Reset Kubelet service using the following command:
service kubelet restart
</pre>
                            </div>
                            
                            
                            
                            <div class="detail-section">
                                <h4>Result Message</h4>
                                <p>FAIL: No kubelet configuration found to verify streaming connection timeout</p>
                            </div>
                            
                            
                            
                                
                                <div class="detail-section">
                                    <h4>Node Results</h4>
                                    <table class="node-results-table">
                                        <thead>
                                            <tr>
                                                <th>Node Name</th>
                                                <th>Status</th>
                                                <th>Message</th>
                                                <th>Evidence</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            
                                            <tr>
                                                <td class="node-name">**************</td>
                                                <td><span class="status-badge status-pass">PASS</span></td>
                                                <td>FAIL: No kubelet configuration found to verify streaming connection timeout</td>
                                                <td>Checking kubelet streaming connection timeout configuration...

=== EVALUATION ===
Config found: false
Timeout configured: false
Timeout disabled: false
FAIL: No kubelet configuration found to verify streaming connection timeout</td>
                                            </tr>
                                            
                                            <tr>
                                                <td class="node-name">***************</td>
                                                <td><span class="status-badge status-pass">PASS</span></td>
                                                <td>FAIL: No kubelet configuration found to verify streaming connection timeout</td>
                                                <td>Checking kubelet streaming connection timeout configuration...

=== EVALUATION ===
Config found: false
Timeout configured: false
Timeout disabled: false
FAIL: No kubelet configuration found to verify streaming connection timeout</td>
                                            </tr>
                                            
                                            <tr>
                                                <td class="node-name">***************</td>
                                                <td><span class="status-badge status-pass">PASS</span></td>
                                                <td>FAIL: No kubelet configuration found to verify streaming connection timeout</td>
                                                <td>Checking kubelet streaming connection timeout configuration...

=== EVALUATION ===
Config found: false
Timeout configured: false
Timeout disabled: false
FAIL: No kubelet configuration found to verify streaming connection timeout</td>
                                            </tr>
                                            
                                            <tr>
                                                <td class="node-name">**************</td>
                                                <td><span class="status-badge status-pass">PASS</span></td>
                                                <td>FAIL: No kubelet configuration found to verify streaming connection timeout</td>
                                                <td>Checking kubelet streaming connection timeout configuration...

=== EVALUATION ===
Config found: false
Timeout configured: false
Timeout disabled: false
FAIL: No kubelet configuration found to verify streaming connection timeout</td>
                                            </tr>
                                            
                                        </tbody>
                                    </table>
                                </div>
                                
                            
                        </div>
                    </div>
                </div>
                
                
                <div class="finding-item">
                    <div class="finding-header" onclick="toggleFinding(this)">
                        <div class="finding-title-section">
                            <div class="finding-id">V-242430</div>
                            <div class="finding-title">Kubernetes Kubelet must enable tlsCertFile for client authentication to secure service.</div>
                            <div class="finding-meta">
                                <span class="severity-badge severity-medium">medium</span>
                                <span class="status-badge status-pass-pass">PASS</span>
                                <span class="info-value">node</span>
                            </div>
                        </div>
                        <div class="expand-icon">▼</div>
                    </div>
                    <div class="finding-content">
                        <div class="finding-details">
                            <div class="detail-section">
                                <h4>Description</h4>
                                <p>Kubernetes container and pod configuration are maintained by Kubelet. Kubelet agents register nodes with the API Server, mount volume storage, and perform health checks for containers and pods. Anyone who gains access to Kubelet agents can effectively control applications within the pods and containers. Using authenticity protection, the communication can be protected against man-in-the-middle attacks/session hijacking and the insertion of false information into sessions.

The communication session is protected by utilizing transport encryption protocols, such as TLS. TLS provides the Kubernetes API Server with a means to be able to authenticate sessions and encrypt traffic.

To enable encrypted communication for Kubelet, the parameter tlsCertFile must be set. This parameter gives the location of the SSL Certificate Authority file used to secure Kubelet communication.
</p>
                            </div>
                            
                            
                            <div class="detail-section">
                                <h4>Check Text</h4>
                                <pre>On the Control Plane and Worker Nodes, run the command:
ps -ef | grep kubelet

Check the config file(s) referenced by the --config argument (example: --config=/etc/kubernetes/kubelet/kubelet-config.yaml):

Change to the directory identified by --config argument and run the command:
grep -i tlscertfile *

If the setting tlsCertFile is not configured, this is a finding.
</pre>
                            </div>
                            
                            
                            
                            <div class="detail-section">
                                <h4>Fix Text</h4>
                                <pre>On the Control Plane and Worker Nodes, run the command:
ps -ef | grep kubelet

Check the config file(s) referenced by the --config argument (example: --config=/etc/kubernetes/kubelet/kubelet-config.yaml):

Edit the Kubernetes Kubelet config file:
Set the value of tlsCertFile to the Approved Organizational Certificate.

Reset Kubelet service using the following command:
service kubelet restart
</pre>
                            </div>
                            
                            
                            
                            <div class="detail-section">
                                <h4>Result Message</h4>
                                <p>FAIL: Kubelet TLS certificate file is not properly configured</p>
                            </div>
                            
                            
                            
                                
                                <div class="detail-section">
                                    <h4>Node Results</h4>
                                    <table class="node-results-table">
                                        <thead>
                                            <tr>
                                                <th>Node Name</th>
                                                <th>Status</th>
                                                <th>Message</th>
                                                <th>Evidence</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            
                                            <tr>
                                                <td class="node-name">***************</td>
                                                <td><span class="status-badge status-pass">PASS</span></td>
                                                <td>FAIL: Kubelet TLS certificate file is not properly configured</td>
                                                <td>Checking kubelet TLS certificate file configuration...

=== EVALUATION ===
Config found: false
Certificate file configured: false
FAIL: Kubelet TLS certificate file is not properly configured</td>
                                            </tr>
                                            
                                            <tr>
                                                <td class="node-name">**************</td>
                                                <td><span class="status-badge status-pass">PASS</span></td>
                                                <td>FAIL: Kubelet TLS certificate file is not properly configured</td>
                                                <td>Checking kubelet TLS certificate file configuration...

=== EVALUATION ===
Config found: false
Certificate file configured: false
FAIL: Kubelet TLS certificate file is not properly configured</td>
                                            </tr>
                                            
                                            <tr>
                                                <td class="node-name">**************</td>
                                                <td><span class="status-badge status-pass">PASS</span></td>
                                                <td>FAIL: Kubelet TLS certificate file is not properly configured</td>
                                                <td>Checking kubelet TLS certificate file configuration...

=== EVALUATION ===
Config found: false
Certificate file configured: false
FAIL: Kubelet TLS certificate file is not properly configured</td>
                                            </tr>
                                            
                                            <tr>
                                                <td class="node-name">***************</td>
                                                <td><span class="status-badge status-pass">PASS</span></td>
                                                <td>FAIL: Kubelet TLS certificate file is not properly configured</td>
                                                <td>Checking kubelet TLS certificate file configuration...

=== EVALUATION ===
Config found: false
Certificate file configured: false
FAIL: Kubelet TLS certificate file is not properly configured</td>
                                            </tr>
                                            
                                        </tbody>
                                    </table>
                                </div>
                                
                            
                        </div>
                    </div>
                </div>
                
                
                <div class="finding-item">
                    <div class="finding-header" onclick="toggleFinding(this)">
                        <div class="finding-title-section">
                            <div class="finding-id">V-242431</div>
                            <div class="finding-title">Kubernetes Kubelet must enable tlsPrivateKeyFile for client authentication to secure service.</div>
                            <div class="finding-meta">
                                <span class="severity-badge severity-medium">medium</span>
                                <span class="status-badge status-pass-pass">PASS</span>
                                <span class="info-value">node</span>
                            </div>
                        </div>
                        <div class="expand-icon">▼</div>
                    </div>
                    <div class="finding-content">
                        <div class="finding-details">
                            <div class="detail-section">
                                <h4>Description</h4>
                                <p>Kubernetes container and pod configuration are maintained by Kubelet. Kubelet agents register nodes with the API Server, mount volume storage, and perform health checks for containers and pods. Anyone who gains access to Kubelet agents can effectively control applications within the pods and containers. Using authenticity protection, the communication can be protected against man-in-the-middle attacks/session hijacking and the insertion of false information into sessions.

The communication session is protected by utilizing transport encryption protocols, such as TLS. TLS provides the Kubernetes API Server with a means to be able to authenticate sessions and encrypt traffic.

To enable encrypted communication for Kubelet, the parameter tlsPrivateKeyFile must be set. This parameter gives the location of the SSL Private Key file used to secure Kubelet communication.
</p>
                            </div>
                            
                            
                            <div class="detail-section">
                                <h4>Check Text</h4>
                                <pre>On the Control Plane and Worker Nodes, run the command:
ps -ef | grep kubelet

Check the config file(s) referenced by the --config argument (example: --config=/etc/kubernetes/kubelet/kubelet-config.yaml):

Change to the directory identified by --config argument and run the command:
grep -i tlsprivatekeyfile *

If the setting tlsPrivateKeyFile is not configured, this is a finding.
</pre>
                            </div>
                            
                            
                            
                            <div class="detail-section">
                                <h4>Fix Text</h4>
                                <pre>On the Control Plane and Worker Nodes, run the command:
ps -ef | grep kubelet

Check the config file(s) referenced by the --config argument (example: --config=/etc/kubernetes/kubelet/kubelet-config.yaml):

Edit the Kubernetes Kubelet config file:
Set the value of tlsPrivateKeyFile to the Approved Organizational Private Key.

Reset Kubelet service using the following command:
service kubelet restart
</pre>
                            </div>
                            
                            
                            
                            <div class="detail-section">
                                <h4>Result Message</h4>
                                <p>FAIL: Kubelet TLS private key file not configured (tlsPrivateKeyFile or --tls-private-key-file not found)</p>
                            </div>
                            
                            
                            
                                
                                <div class="detail-section">
                                    <h4>Node Results</h4>
                                    <table class="node-results-table">
                                        <thead>
                                            <tr>
                                                <th>Node Name</th>
                                                <th>Status</th>
                                                <th>Message</th>
                                                <th>Evidence</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            
                                            <tr>
                                                <td class="node-name">**************</td>
                                                <td><span class="status-badge status-pass">PASS</span></td>
                                                <td>FAIL: Kubelet TLS private key file not configured (tlsPrivateKeyFile or --tls-private-key-file not found)</td>
                                                <td>FAIL: Kubelet TLS private key file not configured (tlsPrivateKeyFile or --tls-private-key-file not found)</td>
                                            </tr>
                                            
                                            <tr>
                                                <td class="node-name">***************</td>
                                                <td><span class="status-badge status-pass">PASS</span></td>
                                                <td>FAIL: Kubelet TLS private key file not configured (tlsPrivateKeyFile or --tls-private-key-file not found)</td>
                                                <td>FAIL: Kubelet TLS private key file not configured (tlsPrivateKeyFile or --tls-private-key-file not found)</td>
                                            </tr>
                                            
                                            <tr>
                                                <td class="node-name">**************</td>
                                                <td><span class="status-badge status-pass">PASS</span></td>
                                                <td>FAIL: Kubelet TLS private key file not configured (tlsPrivateKeyFile or --tls-private-key-file not found)</td>
                                                <td>FAIL: Kubelet TLS private key file not configured (tlsPrivateKeyFile or --tls-private-key-file not found)</td>
                                            </tr>
                                            
                                            <tr>
                                                <td class="node-name">***************</td>
                                                <td><span class="status-badge status-pass">PASS</span></td>
                                                <td>FAIL: Kubelet TLS private key file not configured (tlsPrivateKeyFile or --tls-private-key-file not found)</td>
                                                <td>FAIL: Kubelet TLS private key file not configured (tlsPrivateKeyFile or --tls-private-key-file not found)</td>
                                            </tr>
                                            
                                        </tbody>
                                    </table>
                                </div>
                                
                            
                        </div>
                    </div>
                </div>
                
                
                <div class="finding-item">
                    <div class="finding-header" onclick="toggleFinding(this)">
                        <div class="finding-title-section">
                            <div class="finding-id">V-242408</div>
                            <div class="finding-title">The Kubernetes manifest files must have least privileges.</div>
                            <div class="finding-meta">
                                <span class="severity-badge severity-medium">medium</span>
                                <span class="status-badge status-pass-pass">PASS</span>
                                <span class="info-value">node</span>
                            </div>
                        </div>
                        <div class="expand-icon">▼</div>
                    </div>
                    <div class="finding-content">
                        <div class="finding-details">
                            <div class="detail-section">
                                <h4>Description</h4>
                                <p>The manifest files contain the runtime configuration of the API server, scheduler, controller, and etcd. If an attacker can gain access to these files, changes can be made to open vulnerabilities and bypass user authorizations inherent within Kubernetes with RBAC implemented.

Satisfies: SRG-APP-000133-CTR-000310, SRG-APP-000133-CTR-000295, SRG-APP-000516-CTR-001335
</p>
                            </div>
                            
                            
                            <div class="detail-section">
                                <h4>Check Text</h4>
                                <pre>On both Control Plane and Worker Nodes, change to the /etc/kubernetes/manifest directory. Run the command:
ls -l *

Each manifest file must have permissions &#34;644&#34; or more restrictive.

If any manifest file is less restrictive than &#34;644&#34;, this is a finding.
</pre>
                            </div>
                            
                            
                            
                            <div class="detail-section">
                                <h4>Fix Text</h4>
                                <pre>On both Control Plane and Worker Nodes, change to the /etc/kubernetes/manifest directory. Run the command:
chmod 644 *

To verify the change took place, run the command:
ls -l *

All the manifest files should now have privileges of &#34;644&#34;.
</pre>
                            </div>
                            
                            
                            
                            <div class="detail-section">
                                <h4>Result Message</h4>
                                <p>PASS: All manifest files have appropriate permissions (644 or more restrictive)</p>
                            </div>
                            
                            
                            
                                
                                <div class="detail-section">
                                    <h4>Node Results</h4>
                                    <table class="node-results-table">
                                        <thead>
                                            <tr>
                                                <th>Node Name</th>
                                                <th>Status</th>
                                                <th>Message</th>
                                                <th>Evidence</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            
                                            <tr>
                                                <td class="node-name">**************</td>
                                                <td><span class="status-badge status-pass">PASS</span></td>
                                                <td>PASS: All manifest files have appropriate permissions (644 or more restrictive)</td>
                                                <td>Checking Kubernetes manifest file permissions...
ℹ Manifest directory not found: /host/etc/kubernetes/manifests
ℹ Manifest directory not found: /host/etc/kubernetes/manifest
PASS: All manifest files have appropriate permissions (644 or more restrictive)</td>
                                            </tr>
                                            
                                            <tr>
                                                <td class="node-name">**************</td>
                                                <td><span class="status-badge status-pass">PASS</span></td>
                                                <td>PASS: All manifest files have appropriate permissions (644 or more restrictive)</td>
                                                <td>Checking Kubernetes manifest file permissions...
ℹ Manifest directory not found: /host/etc/kubernetes/manifests
ℹ Manifest directory not found: /host/etc/kubernetes/manifest
PASS: All manifest files have appropriate permissions (644 or more restrictive)</td>
                                            </tr>
                                            
                                            <tr>
                                                <td class="node-name">***************</td>
                                                <td><span class="status-badge status-pass">PASS</span></td>
                                                <td>PASS: All manifest files have appropriate permissions (644 or more restrictive)</td>
                                                <td>Checking Kubernetes manifest file permissions...
ℹ Manifest directory not found: /host/etc/kubernetes/manifests
ℹ Manifest directory not found: /host/etc/kubernetes/manifest
PASS: All manifest files have appropriate permissions (644 or more restrictive)</td>
                                            </tr>
                                            
                                            <tr>
                                                <td class="node-name">***************</td>
                                                <td><span class="status-badge status-pass">PASS</span></td>
                                                <td>PASS: All manifest files have appropriate permissions (644 or more restrictive)</td>
                                                <td>Checking Kubernetes manifest file permissions...
ℹ Manifest directory not found: /host/etc/kubernetes/manifests
ℹ Manifest directory not found: /host/etc/kubernetes/manifest
PASS: All manifest files have appropriate permissions (644 or more restrictive)</td>
                                            </tr>
                                            
                                        </tbody>
                                    </table>
                                </div>
                                
                            
                        </div>
                    </div>
                </div>
                
                
                <div class="finding-item">
                    <div class="finding-header" onclick="toggleFinding(this)">
                        <div class="finding-title-section">
                            <div class="finding-id">V-242405</div>
                            <div class="finding-title">The Kubernetes manifests must be owned by root.</div>
                            <div class="finding-meta">
                                <span class="severity-badge severity-medium">medium</span>
                                <span class="status-badge status-pass-pass">PASS</span>
                                <span class="info-value">node</span>
                            </div>
                        </div>
                        <div class="expand-icon">▼</div>
                    </div>
                    <div class="finding-content">
                        <div class="finding-details">
                            <div class="detail-section">
                                <h4>Description</h4>
                                <p>The manifest files contain the runtime configuration of the API server, proxy, scheduler, controller, and etcd. If an attacker can gain access to these files, changes can be made to open vulnerabilities and bypass user authorizations inherit within Kubernetes with RBAC implemented.
</p>
                            </div>
                            
                            
                            <div class="detail-section">
                                <h4>Check Text</h4>
                                <pre>On the Control Plane, change to the /etc/kubernetes/manifest directory. Run the command:
ls -l *

Each manifest file must be owned by root:root.

If any manifest file is not owned by root:root, this is a finding.
</pre>
                            </div>
                            
                            
                            
                            <div class="detail-section">
                                <h4>Fix Text</h4>
                                <pre>On the Control Plane, change to the /etc/kubernetes/manifest directory. Run the command:
chown root:root *

To verify the change took place, run the command:
ls -l *

All the manifest files should be owned by root:root.
</pre>
                            </div>
                            
                            
                            
                            <div class="detail-section">
                                <h4>Result Message</h4>
                                <p>PASS: All manifest files are properly owned by root:root</p>
                            </div>
                            
                            
                            
                                
                                <div class="detail-section">
                                    <h4>Node Results</h4>
                                    <table class="node-results-table">
                                        <thead>
                                            <tr>
                                                <th>Node Name</th>
                                                <th>Status</th>
                                                <th>Message</th>
                                                <th>Evidence</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            
                                            <tr>
                                                <td class="node-name">**************</td>
                                                <td><span class="status-badge status-pass">PASS</span></td>
                                                <td>PASS: All manifest files are properly owned by root:root</td>
                                                <td>Checking Kubernetes manifest file ownership...
ℹ Manifest directory not found: /host/etc/kubernetes/manifests
ℹ Manifest directory not found: /host/etc/kubernetes/manifest
PASS: All manifest files are properly owned by root:root</td>
                                            </tr>
                                            
                                            <tr>
                                                <td class="node-name">***************</td>
                                                <td><span class="status-badge status-pass">PASS</span></td>
                                                <td>PASS: All manifest files are properly owned by root:root</td>
                                                <td>Checking Kubernetes manifest file ownership...
ℹ Manifest directory not found: /host/etc/kubernetes/manifests
ℹ Manifest directory not found: /host/etc/kubernetes/manifest
PASS: All manifest files are properly owned by root:root</td>
                                            </tr>
                                            
                                            <tr>
                                                <td class="node-name">**************</td>
                                                <td><span class="status-badge status-pass">PASS</span></td>
                                                <td>PASS: All manifest files are properly owned by root:root</td>
                                                <td>Checking Kubernetes manifest file ownership...
ℹ Manifest directory not found: /host/etc/kubernetes/manifests
ℹ Manifest directory not found: /host/etc/kubernetes/manifest
PASS: All manifest files are properly owned by root:root</td>
                                            </tr>
                                            
                                            <tr>
                                                <td class="node-name">***************</td>
                                                <td><span class="status-badge status-pass">PASS</span></td>
                                                <td>PASS: All manifest files are properly owned by root:root</td>
                                                <td>Checking Kubernetes manifest file ownership...
ℹ Manifest directory not found: /host/etc/kubernetes/manifests
ℹ Manifest directory not found: /host/etc/kubernetes/manifest
PASS: All manifest files are properly owned by root:root</td>
                                            </tr>
                                            
                                        </tbody>
                                    </table>
                                </div>
                                
                            
                        </div>
                    </div>
                </div>
                
                
                <div class="finding-item">
                    <div class="finding-header" onclick="toggleFinding(this)">
                        <div class="finding-title-section">
                            <div class="finding-id">V-242433</div>
                            <div class="finding-title">Kubernetes must remove old components after updated versions have been installed.</div>
                            <div class="finding-meta">
                                <span class="severity-badge severity-medium">medium</span>
                                <span class="status-badge status-pass-pass">PASS</span>
                                <span class="info-value">node</span>
                            </div>
                        </div>
                        <div class="expand-icon">▼</div>
                    </div>
                    <div class="finding-content">
                        <div class="finding-details">
                            <div class="detail-section">
                                <h4>Description</h4>
                                <p>Previous versions of Kubernetes components that are not removed after updates may be exploited by adversaries. Some malicious software may execute procedures to install older versions of Kubernetes components to reintroduce vulnerabilities that have been mitigated in newer versions.

When Kubernetes components are updated, previous versions should be completely removed from the system to prevent potential security issues and ensure that only the current, patched versions are available for execution.
</p>
                            </div>
                            
                            
                            <div class="detail-section">
                                <h4>Check Text</h4>
                                <pre>Verify that old versions of Kubernetes components have been removed from the system.

Check for multiple versions of Kubernetes binaries by running:
find /usr/bin/ -name &#39;kube*&#39; -type f
find /usr/local/bin/ -name &#39;kube*&#39; -type f

If multiple versions of the same Kubernetes component are found (e.g., kubelet, kubectl, kubeadm), this is a finding.
</pre>
                            </div>
                            
                            
                            
                            <div class="detail-section">
                                <h4>Fix Text</h4>
                                <pre>Remove old versions of Kubernetes components:

1. Identify all versions of Kubernetes components
2. Remove outdated versions while keeping only the current version
3. Verify that only the current version remains

Example:
rm /usr/bin/kubelet-old
rm /usr/bin/kubectl-1.24.0
</pre>
                            </div>
                            
                            
                            
                            <div class="detail-section">
                                <h4>Result Message</h4>
                                <p>Check passed</p>
                            </div>
                            
                            
                            
                                
                                <div class="detail-section">
                                    <h4>Node Results</h4>
                                    <table class="node-results-table">
                                        <thead>
                                            <tr>
                                                <th>Node Name</th>
                                                <th>Status</th>
                                                <th>Message</th>
                                                <th>Evidence</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            
                                            <tr>
                                                <td class="node-name">**************</td>
                                                <td><span class="status-badge status-pass">PASS</span></td>
                                                <td>Check passed</td>
                                                <td>N/A</td>
                                            </tr>
                                            
                                            <tr>
                                                <td class="node-name">***************</td>
                                                <td><span class="status-badge status-pass">PASS</span></td>
                                                <td>Check passed</td>
                                                <td>N/A</td>
                                            </tr>
                                            
                                            <tr>
                                                <td class="node-name">**************</td>
                                                <td><span class="status-badge status-pass">PASS</span></td>
                                                <td>Check passed</td>
                                                <td>N/A</td>
                                            </tr>
                                            
                                            <tr>
                                                <td class="node-name">***************</td>
                                                <td><span class="status-badge status-pass">PASS</span></td>
                                                <td>Check passed</td>
                                                <td>N/A</td>
                                            </tr>
                                            
                                        </tbody>
                                    </table>
                                </div>
                                
                            
                        </div>
                    </div>
                </div>
                
                
                <div class="finding-item">
                    <div class="finding-header" onclick="toggleFinding(this)">
                        <div class="finding-title-section">
                            <div class="finding-id">V-242417</div>
                            <div class="finding-title">The Kubernetes PKI CRT must have file permissions set to 644 or more restrictive.</div>
                            <div class="finding-meta">
                                <span class="severity-badge severity-medium">medium</span>
                                <span class="status-badge status-pass-pass">PASS</span>
                                <span class="info-value">node</span>
                            </div>
                        </div>
                        <div class="expand-icon">▼</div>
                    </div>
                    <div class="finding-content">
                        <div class="finding-details">
                            <div class="detail-section">
                                <h4>Description</h4>
                                <p>The Kubernetes PKI directory contains all the public/private key pairs and certificates used by Kubernetes components. Certificate files contain public keys and should be readable but not writable by non-privileged users. If an attacker can modify these files, they could potentially compromise the security of the cluster.
</p>
                            </div>
                            
                            
                            <div class="detail-section">
                                <h4>Check Text</h4>
                                <pre>On the Control Plane, run the command:
stat -c %a /etc/kubernetes/pki/*.crt

If any certificate file has permissions more permissive than &#34;644&#34;, this is a finding.
</pre>
                            </div>
                            
                            
                            
                            <div class="detail-section">
                                <h4>Fix Text</h4>
                                <pre>On the Control Plane, run the command:
chmod 644 /etc/kubernetes/pki/*.crt
</pre>
                            </div>
                            
                            
                            
                            <div class="detail-section">
                                <h4>Result Message</h4>
                                <p>WARNING: No certificate files found</p>
                            </div>
                            
                            
                            
                                
                                <div class="detail-section">
                                    <h4>Node Results</h4>
                                    <table class="node-results-table">
                                        <thead>
                                            <tr>
                                                <th>Node Name</th>
                                                <th>Status</th>
                                                <th>Message</th>
                                                <th>Evidence</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            
                                            <tr>
                                                <td class="node-name">***************</td>
                                                <td><span class="status-badge status-pass">PASS</span></td>
                                                <td>WARNING: No certificate files found</td>
                                                <td>Checking PKI certificate file permissions...
WARNING: No certificate files found</td>
                                            </tr>
                                            
                                            <tr>
                                                <td class="node-name">***************</td>
                                                <td><span class="status-badge status-pass">PASS</span></td>
                                                <td>WARNING: No certificate files found</td>
                                                <td>Checking PKI certificate file permissions...
WARNING: No certificate files found</td>
                                            </tr>
                                            
                                            <tr>
                                                <td class="node-name">**************</td>
                                                <td><span class="status-badge status-pass">PASS</span></td>
                                                <td>WARNING: No certificate files found</td>
                                                <td>Checking PKI certificate file permissions...
WARNING: No certificate files found</td>
                                            </tr>
                                            
                                            <tr>
                                                <td class="node-name">**************</td>
                                                <td><span class="status-badge status-pass">PASS</span></td>
                                                <td>WARNING: No certificate files found</td>
                                                <td>Checking PKI certificate file permissions...
WARNING: No certificate files found</td>
                                            </tr>
                                            
                                        </tbody>
                                    </table>
                                </div>
                                
                            
                        </div>
                    </div>
                </div>
                
                
                <div class="finding-item">
                    <div class="finding-header" onclick="toggleFinding(this)">
                        <div class="finding-title-section">
                            <div class="finding-id">V-242409</div>
                            <div class="finding-title">The Kubernetes PKI keys must have file permissions set to 600 or more restrictive.</div>
                            <div class="finding-meta">
                                <span class="severity-badge severity-medium">medium</span>
                                <span class="status-badge status-pass-pass">PASS</span>
                                <span class="info-value">node</span>
                            </div>
                        </div>
                        <div class="expand-icon">▼</div>
                    </div>
                    <div class="finding-content">
                        <div class="finding-details">
                            <div class="detail-section">
                                <h4>Description</h4>
                                <p>The Kubernetes PKI directory contains all certificate key files supporting secure network communications in the Kubernetes Control Plane. If these files can be modified, data traversing within the cluster communications could be compromised. Many of the security settings within the document are implemented through these files.

The PKI certificate key files should be protected with the most restrictive file permissions possible. Setting the permissions to 600 ensures that only the owner (root) can read and write to these sensitive files.
</p>
                            </div>
                            
                            
                            <div class="detail-section">
                                <h4>Check Text</h4>
                                <pre>Review the Kubernetes PKI key files by using the command:

find /etc/kubernetes/pki/ -name &#39;*.key&#39; -exec stat -c permissions=%a %n {} \;

If any key file permissions are not set to 600 or more restrictive, this is a finding.
</pre>
                            </div>
                            
                            
                            
                            <div class="detail-section">
                                <h4>Fix Text</h4>
                                <pre>Change the permissions of the PKI key files by executing the command:

find /etc/kubernetes/pki/ -name &#39;*.key&#39; -exec chmod 600 {} \;
</pre>
                            </div>
                            
                            
                            
                            <div class="detail-section">
                                <h4>Result Message</h4>
                                <p>FAIL: PKI directory /host/etc/kubernetes/pki not found</p>
                            </div>
                            
                            
                            
                                
                                <div class="detail-section">
                                    <h4>Node Results</h4>
                                    <table class="node-results-table">
                                        <thead>
                                            <tr>
                                                <th>Node Name</th>
                                                <th>Status</th>
                                                <th>Message</th>
                                                <th>Evidence</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            
                                            <tr>
                                                <td class="node-name">***************</td>
                                                <td><span class="status-badge status-pass">PASS</span></td>
                                                <td>FAIL: PKI directory /host/etc/kubernetes/pki not found</td>
                                                <td>FAIL: PKI directory /host/etc/kubernetes/pki not found</td>
                                            </tr>
                                            
                                            <tr>
                                                <td class="node-name">**************</td>
                                                <td><span class="status-badge status-pass">PASS</span></td>
                                                <td>FAIL: PKI directory /host/etc/kubernetes/pki not found</td>
                                                <td>FAIL: PKI directory /host/etc/kubernetes/pki not found</td>
                                            </tr>
                                            
                                            <tr>
                                                <td class="node-name">**************</td>
                                                <td><span class="status-badge status-pass">PASS</span></td>
                                                <td>FAIL: PKI directory /host/etc/kubernetes/pki not found</td>
                                                <td>FAIL: PKI directory /host/etc/kubernetes/pki not found</td>
                                            </tr>
                                            
                                            <tr>
                                                <td class="node-name">***************</td>
                                                <td><span class="status-badge status-pass">PASS</span></td>
                                                <td>FAIL: PKI directory /host/etc/kubernetes/pki not found</td>
                                                <td>FAIL: PKI directory /host/etc/kubernetes/pki not found</td>
                                            </tr>
                                            
                                        </tbody>
                                    </table>
                                </div>
                                
                            
                        </div>
                    </div>
                </div>
                
                
                <div class="finding-item">
                    <div class="finding-header" onclick="toggleFinding(this)">
                        <div class="finding-title-section">
                            <div class="finding-id">V-242412</div>
                            <div class="finding-title">The Kubernetes scheduler.conf must be owned by root.</div>
                            <div class="finding-meta">
                                <span class="severity-badge severity-medium">medium</span>
                                <span class="status-badge status-pass-pass">PASS</span>
                                <span class="info-value">node</span>
                            </div>
                        </div>
                        <div class="expand-icon">▼</div>
                    </div>
                    <div class="finding-content">
                        <div class="finding-details">
                            <div class="detail-section">
                                <h4>Description</h4>
                                <p>The scheduler.conf is the kubeconfig file for the Kubernetes Scheduler. The Kubernetes Scheduler is responsible for scheduling pods on available worker nodes. If an attacker can gain access to this file, changes can be made to open vulnerabilities and bypass user authorizations inherent within Kubernetes with RBAC implemented.
</p>
                            </div>
                            
                            
                            <div class="detail-section">
                                <h4>Check Text</h4>
                                <pre>On the Control Plane, run the command:
stat -c %U:%G /etc/kubernetes/scheduler.conf

If the file is not owned by root:root, this is a finding.
</pre>
                            </div>
                            
                            
                            
                            <div class="detail-section">
                                <h4>Fix Text</h4>
                                <pre>On the Control Plane, run the command:
chown root:root /etc/kubernetes/scheduler.conf
</pre>
                            </div>
                            
                            
                            
                            <div class="detail-section">
                                <h4>Result Message</h4>
                                <p>WARNING: No scheduler configuration files found</p>
                            </div>
                            
                            
                            
                                
                                <div class="detail-section">
                                    <h4>Node Results</h4>
                                    <table class="node-results-table">
                                        <thead>
                                            <tr>
                                                <th>Node Name</th>
                                                <th>Status</th>
                                                <th>Message</th>
                                                <th>Evidence</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            
                                            <tr>
                                                <td class="node-name">**************</td>
                                                <td><span class="status-badge status-pass">PASS</span></td>
                                                <td>WARNING: No scheduler configuration files found</td>
                                                <td>Checking scheduler.conf file ownership...
WARNING: No scheduler configuration files found</td>
                                            </tr>
                                            
                                            <tr>
                                                <td class="node-name">***************</td>
                                                <td><span class="status-badge status-pass">PASS</span></td>
                                                <td>WARNING: No scheduler configuration files found</td>
                                                <td>Checking scheduler.conf file ownership...
WARNING: No scheduler configuration files found</td>
                                            </tr>
                                            
                                            <tr>
                                                <td class="node-name">**************</td>
                                                <td><span class="status-badge status-pass">PASS</span></td>
                                                <td>WARNING: No scheduler configuration files found</td>
                                                <td>Checking scheduler.conf file ownership...
WARNING: No scheduler configuration files found</td>
                                            </tr>
                                            
                                            <tr>
                                                <td class="node-name">***************</td>
                                                <td><span class="status-badge status-pass">PASS</span></td>
                                                <td>WARNING: No scheduler configuration files found</td>
                                                <td>Checking scheduler.conf file ownership...
WARNING: No scheduler configuration files found</td>
                                            </tr>
                                            
                                        </tbody>
                                    </table>
                                </div>
                                
                            
                        </div>
                    </div>
                </div>
                
                
                <div class="finding-item">
                    <div class="finding-header" onclick="toggleFinding(this)">
                        <div class="finding-title-section">
                            <div class="finding-id">V-242413</div>
                            <div class="finding-title">The Kubernetes scheduler.conf must have file permissions set to 644 or more restrictive.</div>
                            <div class="finding-meta">
                                <span class="severity-badge severity-medium">medium</span>
                                <span class="status-badge status-pass-pass">PASS</span>
                                <span class="info-value">node</span>
                            </div>
                        </div>
                        <div class="expand-icon">▼</div>
                    </div>
                    <div class="finding-content">
                        <div class="finding-details">
                            <div class="detail-section">
                                <h4>Description</h4>
                                <p>The scheduler.conf is the kubeconfig file for the Kubernetes Scheduler. The Kubernetes Scheduler is responsible for scheduling pods on available worker nodes. If an attacker can gain access to this file, changes can be made to open vulnerabilities and bypass user authorizations inherent within Kubernetes with RBAC implemented.
</p>
                            </div>
                            
                            
                            <div class="detail-section">
                                <h4>Check Text</h4>
                                <pre>On the Control Plane, run the command:
stat -c %a /etc/kubernetes/scheduler.conf

If the file has permissions more permissive than &#34;644&#34;, this is a finding.
</pre>
                            </div>
                            
                            
                            
                            <div class="detail-section">
                                <h4>Fix Text</h4>
                                <pre>On the Control Plane, run the command:
chmod 644 /etc/kubernetes/scheduler.conf
</pre>
                            </div>
                            
                            
                            
                            <div class="detail-section">
                                <h4>Result Message</h4>
                                <p>WARNING: No scheduler configuration files found</p>
                            </div>
                            
                            
                            
                                
                                <div class="detail-section">
                                    <h4>Node Results</h4>
                                    <table class="node-results-table">
                                        <thead>
                                            <tr>
                                                <th>Node Name</th>
                                                <th>Status</th>
                                                <th>Message</th>
                                                <th>Evidence</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            
                                            <tr>
                                                <td class="node-name">**************</td>
                                                <td><span class="status-badge status-pass">PASS</span></td>
                                                <td>WARNING: No scheduler configuration files found</td>
                                                <td>Checking scheduler.conf file permissions...
WARNING: No scheduler configuration files found</td>
                                            </tr>
                                            
                                            <tr>
                                                <td class="node-name">***************</td>
                                                <td><span class="status-badge status-pass">PASS</span></td>
                                                <td>WARNING: No scheduler configuration files found</td>
                                                <td>Checking scheduler.conf file permissions...
WARNING: No scheduler configuration files found</td>
                                            </tr>
                                            
                                            <tr>
                                                <td class="node-name">***************</td>
                                                <td><span class="status-badge status-pass">PASS</span></td>
                                                <td>WARNING: No scheduler configuration files found</td>
                                                <td>Checking scheduler.conf file permissions...
WARNING: No scheduler configuration files found</td>
                                            </tr>
                                            
                                            <tr>
                                                <td class="node-name">**************</td>
                                                <td><span class="status-badge status-pass">PASS</span></td>
                                                <td>WARNING: No scheduler configuration files found</td>
                                                <td>Checking scheduler.conf file permissions...
WARNING: No scheduler configuration files found</td>
                                            </tr>
                                            
                                        </tbody>
                                    </table>
                                </div>
                                
                            
                        </div>
                    </div>
                </div>
                
            
        </div>

        <div class="footer">
            <p>Generated by Compliance Operator on June 26, 2025 at 19:26:59 CST</p>
            <p>Security Technical Implementation Guide (STIG) Compliance Assessment</p>
        </div>
    </div>
</body>
</html>
