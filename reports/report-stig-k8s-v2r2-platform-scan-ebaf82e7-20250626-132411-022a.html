
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Compliance Scan Report - stig-k8s-v2r2-platform-scan</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Helvetica Neue', Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            background-color: #f8f9fa;
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
            min-height: 100vh;
        }
        
         
        .header {
            background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 50%, #ec4899 100%);
            color: white;
            padding: 40px 30px;
            text-align: center;
            position: relative;
            overflow: hidden;
        }
        
        .header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
            opacity: 0.3;
        }
        
        .header-content {
            position: relative;
            z-index: 1;
        }
        
        .header h1 {
            font-size: 3em;
            font-weight: 300;
            margin-bottom: 10px;
            text-shadow: 0 2px 4px rgba(0,0,0,0.3);
        }
        
        .header .subtitle {
            font-size: 1.2em;
            opacity: 0.9;
            font-weight: 300;
        }
        
         
        .overview {
            padding: 40px 30px;
            background: linear-gradient(to bottom, #fff 0%, #f8f9fa 100%);
        }
        
        .section-title {
            font-size: 2em;
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 30px;
            border-bottom: 3px solid #4f46e5;
            padding-bottom: 10px;
        }
        
        .overview-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 25px;
            margin-bottom: 30px;
        }
        
        .overview-card {
            background: white;
            border: 1px solid #e5e7eb;
            border-radius: 12px;
            padding: 25px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.05);
            transition: transform 0.2s, box-shadow 0.2s;
        }
        
        .overview-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 15px rgba(0,0,0,0.1);
        }
        
        .overview-card h3 {
            color: #4f46e5;
            font-size: 1.3em;
            margin-bottom: 15px;
            font-weight: 600;
        }
        
        .overview-card .info-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 0;
            border-bottom: 1px solid #f3f4f6;
        }
        
        .overview-card .info-item:last-child {
            border-bottom: none;
        }
        
        .info-label {
            font-weight: 500;
            color: #6b7280;
        }
        
        .info-value {
            font-weight: 600;
            color: #1f2937;
        }
        
         
        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 20px;
            margin-top: 30px;
        }
        
        .stat-card {
            background: white;
            border-radius: 12px;
            padding: 25px 20px;
            text-align: center;
            border: 2px solid transparent;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }
        
        .stat-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
        }
        
        .stat-card.total::before { background: #6b7280; }
        .stat-card.pass::before { background: #10b981; }
        .stat-card.fail::before { background: #ef4444; }
        .stat-card.error::before { background: #f59e0b; }
        .stat-card.manual::before { background: #8b5cf6; }
        .stat-card.na::before { background: #6b7280; }
        
        .stat-card h4 {
            font-size: 2.5em;
            font-weight: 700;
            margin-bottom: 5px;
        }
        
        .stat-card.total h4 { color: #6b7280; }
        .stat-card.pass h4 { color: #10b981; }
        .stat-card.fail h4 { color: #ef4444; }
        .stat-card.error h4 { color: #f59e0b; }
        .stat-card.manual h4 { color: #8b5cf6; }
        .stat-card.na h4 { color: #6b7280; }
        
        .stat-card p {
            color: #6b7280;
            font-weight: 500;
            text-transform: uppercase;
            font-size: 0.9em;
            letter-spacing: 0.5px;
        }
        
         
        .findings {
            padding: 40px 30px;
            background: #f8f9fa;
        }
        
        .finding-item {
            background: white;
            border-radius: 12px;
            margin-bottom: 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.08);
            overflow: hidden;
            transition: box-shadow 0.3s ease;
        }
        
        .finding-item:hover {
            box-shadow: 0 4px 20px rgba(0,0,0,0.12);
        }
        
        .finding-header {
            padding: 20px 25px;
            background: linear-gradient(135deg, #f8f9fa 0%, #e5e7eb 100%);
            border-bottom: 1px solid #e5e7eb;
            cursor: pointer;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .finding-header:hover {
            background: linear-gradient(135deg, #f3f4f6 0%, #d1d5db 100%);
        }
        
        .finding-title-section {
            flex: 1;
        }
        
        .finding-id {
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
            background: #4f46e5;
            color: white;
            padding: 6px 12px;
            border-radius: 6px;
            font-weight: 600;
            font-size: 0.9em;
            margin-bottom: 8px;
            display: inline-block;
        }
        
        .finding-title {
            font-size: 1.2em;
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 5px;
        }
        
        .finding-meta {
            display: flex;
            gap: 15px;
            align-items: center;
        }
        
        .severity-badge, .status-badge {
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 0.8em;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
        
        .severity-high { background: #fecaca; color: #991b1b; }
        .severity-medium { background: #fed7aa; color: #9a3412; }
        .severity-low { background: #bbf7d0; color: #166534; }
        
        .status-pass { background: #dcfce7; color: #166534; }
        .status-fail { background: #fecaca; color: #991b1b; }
        .status-error { background: #fed7aa; color: #9a3412; }
        .status-manual { background: #e0e7ff; color: #3730a3; }
        .status-not-applicable { background: #f3f4f6; color: #374151; }
        
        .expand-icon {
            font-size: 1.2em;
            transition: transform 0.3s ease;
            color: #6b7280;
        }
        
        .finding-header.expanded .expand-icon {
            transform: rotate(180deg);
        }
        
        .finding-content {
            display: none;
            padding: 25px;
            background: white;
        }
        
        .finding-content.expanded {
            display: block;
        }
        
        .finding-details {
            display: grid;
            gap: 20px;
        }
        
        .detail-section {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 20px;
            border-left: 4px solid #4f46e5;
        }
        
        .detail-section h4 {
            color: #4f46e5;
            font-weight: 600;
            margin-bottom: 10px;
            font-size: 1.1em;
        }
        
        .detail-section p {
            line-height: 1.6;
            color: #374151;
        }
        
        .detail-section pre {
            background: white;
            padding: 15px;
            border-radius: 6px;
            border: 1px solid #e5e7eb;
            overflow-x: auto;
            font-size: 0.9em;
            line-height: 1.4;
        }
        
         
        .node-results {
            margin-top: 20px;
        }
        
        .node-results-table {
            width: 100%;
            border-collapse: collapse;
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }
        
        .node-results-table th {
            background: #f8f9fa;
            padding: 15px;
            text-align: left;
            font-weight: 600;
            color: #374151;
            border-bottom: 2px solid #e5e7eb;
        }
        
        .node-results-table td {
            padding: 12px 15px;
            border-bottom: 1px solid #f3f4f6;
            vertical-align: top;
        }
        
        .node-results-table tr:hover {
            background: #f9fafb;
        }
        
        .node-name {
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
            font-weight: 600;
            color: #4f46e5;
        }
        
         
        .footer {
            background: #1f2937;
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .footer p {
            opacity: 0.8;
        }
        
         
        @media (max-width: 768px) {
            .container { margin: 0; }
            .header, .overview, .findings { padding: 20px; }
            .header h1 { font-size: 2em; }
            .overview-grid { grid-template-columns: 1fr; }
            .stats { grid-template-columns: repeat(2, 1fr); }
            .finding-meta { flex-direction: column; align-items: flex-start; gap: 10px; }
        }
        
         
        @media print {
            .finding-content { display: block !important; }
            .expand-icon { display: none; }
            .finding-header { cursor: default; }
        }
    </style>
    <script>
        function toggleFinding(element) {
            const content = element.nextElementSibling;
            const icon = element.querySelector('.expand-icon');
            
            if (content.classList.contains('expanded')) {
                content.classList.remove('expanded');
                element.classList.remove('expanded');
            } else {
                content.classList.add('expanded');
                element.classList.add('expanded');
            }
        }
        
        
        document.addEventListener('DOMContentLoaded', function() {
            const failedFindings = document.querySelectorAll('.finding-item');
            failedFindings.forEach(function(finding) {
                const statusBadge = finding.querySelector('.status-fail');
                if (statusBadge) {
                    const header = finding.querySelector('.finding-header');
                    const content = finding.querySelector('.finding-content');
                    content.classList.add('expanded');
                    header.classList.add('expanded');
                }
            });
        });
    </script>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="header-content">
                <h1>Compliance Scan Report</h1>
                <div class="subtitle">Security Technical Implementation Guide (STIG) Assessment</div>
            </div>
        </div>

        <div class="overview">
            <h2 class="section-title">Scan Overview</h2>
            <div class="overview-grid">
                <div class="overview-card">
                    <h3>Scan Information</h3>
                    <div class="info-item">
                        <span class="info-label">Scan Name</span>
                        <span class="info-value">stig-k8s-v2r2-platform-scan</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">Profile</span>
                        <span class="info-value">stig-k8s-v2r2-platform</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">Scan ID</span>
                        <span class="info-value">stig-k8s-v2r2-platform-scan-ebaf82e7-20250626-132411-022a</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">Scan Time</span>
                        <span class="info-value">2025-06-26 21:24:32 UTC</span>
                    </div>
                </div>
                
                <div class="overview-card">
                    <h3>Cluster Information</h3>
                    <div class="info-item">
                        <span class="info-label">Kubernetes Version</span>
                        <span class="info-value">v1.31.6</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">Node Count</span>
                        <span class="info-value">4</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">Container Runtime</span>
                        <span class="info-value">containerd://1.7.23-4</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">Master Node</span>
                        <span class="info-value">192.168.139.60</span>
                    </div>
                </div>
                
                <div class="overview-card">
                    <h3>Node Details</h3>
                    
                    <div class="info-item">
                        <span class="info-label">192.168.142.195 (worker)</span>
                        <span class="info-value">192.168.142.195</span>
                    </div>
                    
                    <div class="info-item">
                        <span class="info-label">192.168.129.58 (worker)</span>
                        <span class="info-value">192.168.129.58</span>
                    </div>
                    
                    <div class="info-item">
                        <span class="info-label">192.168.139.60 (control-plane)</span>
                        <span class="info-value">192.168.139.60</span>
                    </div>
                    
                    <div class="info-item">
                        <span class="info-label">192.168.140.231 (worker)</span>
                        <span class="info-value">192.168.140.231</span>
                    </div>
                    
                </div>
            </div>

            <div class="stats">
                <div class="stat-card total">
                    <h4>39</h4>
                    <p>Total</p>
                </div>
                <div class="stat-card pass">
                    <h4>30</h4>
                    <p>Pass</p>
                </div>
                <div class="stat-card fail">
                    <h4>8</h4>
                    <p>Fail</p>
                </div>
                <div class="stat-card error">
                    <h4>0</h4>
                    <p>Error</p>
                </div>
                <div class="stat-card manual">
                    <h4>1</h4>
                    <p>Manual</p>
                </div>
                <div class="stat-card na">
                    <h4>0</h4>
                    <p>N/A</p>
                </div>
            </div>
        </div>

        <div class="findings">
            <h2 class="section-title">Findings - All (39)</h2>
            
                
                
                <div class="finding-item">
                    <div class="finding-header" onclick="toggleFinding(this)">
                        <div class="finding-title-section">
                            <div class="finding-id">V-242390</div>
                            <div class="finding-title">The Kubernetes API server must have anonymous authentication disabled.</div>
                            <div class="finding-meta">
                                <span class="severity-badge severity-high">high</span>
                                <span class="status-badge status-fail-fail">FAIL</span>
                                <span class="info-value">platform</span>
                            </div>
                        </div>
                        <div class="expand-icon">▼</div>
                    </div>
                    <div class="finding-content">
                        <div class="finding-details">
                            <div class="detail-section">
                                <h4>Description</h4>
                                <p>The Kubernetes API Server controls Kubernetes via an API interface. A user who has access to the API essentially has root access to the entire Kubernetes cluster. To control access, users must be authenticated and authorized. By allowing anonymous connections, the controls put in place to secure the API can be bypassed.

Setting &#34;--anonymous-auth&#34; to &#34;false&#34; also disables unauthenticated requests from kubelets.

While there are instances where anonymous connections may be needed (e.g., health checks) and Role-Based Access Controls (RBACs) are in place to limit the anonymous access, this access should be disabled, and only enabled when necessary.
</p>
                            </div>
                            
                            
                            <div class="detail-section">
                                <h4>Check Text</h4>
                                <pre>Change to the /etc/kubernetes/manifests directory on the Kubernetes Control Plane. Run the command:
grep -i anonymous-auth *

If the setting &#34;--anonymous-auth&#34; is set to &#34;true&#34; in the Kubernetes API Server manifest file, this is a finding.
</pre>
                            </div>
                            
                            
                            
                            <div class="detail-section">
                                <h4>Fix Text</h4>
                                <pre>Edit the Kubernetes API Server manifest file in the /etc/kubernetes/manifests directory on the Kubernetes Control Plane.

Set the value of &#34;--anonymous-auth&#34; to &#34;false&#34;.
</pre>
                            </div>
                            
                            
                            
                            <div class="detail-section">
                                <h4>Result Message</h4>
                                <p>FAIL: Anonymous authentication is explicitly enabled (process)</p>
                            </div>
                            
                            
                            
                        </div>
                    </div>
                </div>
                
                
                <div class="finding-item">
                    <div class="finding-header" onclick="toggleFinding(this)">
                        <div class="finding-title-section">
                            <div class="finding-id">V-242388</div>
                            <div class="finding-title">The Kubernetes API server must have the insecure bind address not set.</div>
                            <div class="finding-meta">
                                <span class="severity-badge severity-high">high</span>
                                <span class="status-badge status-fail-fail">FAIL</span>
                                <span class="info-value">platform</span>
                            </div>
                        </div>
                        <div class="expand-icon">▼</div>
                    </div>
                    <div class="finding-content">
                        <div class="finding-details">
                            <div class="detail-section">
                                <h4>Description</h4>
                                <p>By default, the API server will listen on two ports and addresses. One address is the secure address and the other address is called the &#34;insecure bind&#34; address and is set by default to localhost. Any requests to this address bypass authentication and authorization checks. If this insecure bind address is set to localhost, anyone who gains access to the host on which the Control Plane is running can bypass all authorization and authentication mechanisms put in place and have full control over the entire cluster.

Close or set the insecure bind address by setting the API server&#39;s &#34;--insecure-bind-address&#34; flag to an IP or leave it unset and ensure that the &#34;--insecure-bind-port&#34; is not set.
</p>
                            </div>
                            
                            
                            <div class="detail-section">
                                <h4>Check Text</h4>
                                <pre>Change to the /etc/kubernetes/manifests directory on the Kubernetes Control Plane. Run the command:
grep -i insecure-bind-address *

If the setting &#34;--insecure-bind-address&#34; is found and set to &#34;localhost&#34; in the Kubernetes API manifest file, this is a finding.
</pre>
                            </div>
                            
                            
                            
                            <div class="detail-section">
                                <h4>Fix Text</h4>
                                <pre>Edit the Kubernetes API Server manifest file in the /etc/kubernetes/manifests directory on the Kubernetes Control Plane.

Remove the value of &#34;--insecure-bind-address&#34; setting.
</pre>
                            </div>
                            
                            
                            
                            <div class="detail-section">
                                <h4>Result Message</h4>
                                <p>FAIL: Insecure bind address is configured</p>
                            </div>
                            
                            
                            
                        </div>
                    </div>
                </div>
                
                
                <div class="finding-item">
                    <div class="finding-header" onclick="toggleFinding(this)">
                        <div class="finding-title-section">
                            <div class="finding-id">V-242386</div>
                            <div class="finding-title">The Kubernetes API server must have the insecure port flag disabled.</div>
                            <div class="finding-meta">
                                <span class="severity-badge severity-high">high</span>
                                <span class="status-badge status-pass-pass">PASS</span>
                                <span class="info-value">platform</span>
                            </div>
                        </div>
                        <div class="expand-icon">▼</div>
                    </div>
                    <div class="finding-content">
                        <div class="finding-details">
                            <div class="detail-section">
                                <h4>Description</h4>
                                <p>By default, the API server will listen on two ports. One port is the secure port and the other port is called the &#34;localhost port&#34;. This port is also called the &#34;insecure port&#34;, port 8080. Any requests to this port bypass authentication and authorization checks. If this port is left open, anyone who gains access to the host on which the Control Plane is running can bypass all authorization and authentication mechanisms put in place, and have full control over the entire cluster.

Close the insecure port by setting the API server&#39;s &#34;--insecure-port&#34; flag to &#34;0&#34;, ensuring that the &#34;--insecure-bind-address&#34; is not set.
</p>
                            </div>
                            
                            
                            <div class="detail-section">
                                <h4>Check Text</h4>
                                <pre>Change to the /etc/kubernetes/manifests directory on the Kubernetes Control Plane. Run the command:
grep -i insecure-port *

If the setting &#34;--insecure-port&#34; is not set to &#34;0&#34; or is not configured in the Kubernetes API server manifest file, this is a finding.

Note: &#34;--insecure-port&#34; flag has been deprecated and can only be set to &#34;0&#34;. **This flag will be removed in v1.24.*
</pre>
                            </div>
                            
                            
                            
                            <div class="detail-section">
                                <h4>Fix Text</h4>
                                <pre>Edit the Kubernetes API Server manifest file in the /etc/kubernetes/manifests directory on the Kubernetes Control Plane.

Set the value of &#34;--insecure-port&#34; to &#34;0&#34;.
</pre>
                            </div>
                            
                            
                            
                            <div class="detail-section">
                                <h4>Result Message</h4>
                                <p>PASS: Insecure port explicitly disabled (--insecure-port=0)</p>
                            </div>
                            
                            
                            
                        </div>
                    </div>
                </div>
                
                
                <div class="finding-item">
                    <div class="finding-header" onclick="toggleFinding(this)">
                        <div class="finding-title-section">
                            <div class="finding-id">V-245544</div>
                            <div class="finding-title">Kubernetes endpoints must use approved organizational certificate and key pair to protect information in transit.</div>
                            <div class="finding-meta">
                                <span class="severity-badge severity-high">high</span>
                                <span class="status-badge status-pass-pass">PASS</span>
                                <span class="info-value">platform</span>
                            </div>
                        </div>
                        <div class="expand-icon">▼</div>
                    </div>
                    <div class="finding-content">
                        <div class="finding-details">
                            <div class="detail-section">
                                <h4>Description</h4>
                                <p>Kubernetes control plane and external communication is managed by API Server. The main implementation of the API Server is to manage hardware resources for pods and container using horizontal or vertical scaling. Anyone who can gain access to the API Server can effectively control your Kubernetes architecture. Using authenticity protection, the communication can be protected against man-in-the-middle attacks/session hijacking and the insertion of false information into sessions.

The communication session is protected by utilizing transport encryption protocols, such as TLS. TLS provides the Kubernetes API Server with a means to be able to authenticate sessions and encrypt traffic.

By default, the API Server does not authenticate to the kubelet HTTPs endpoint. To enable secure communication for API Server, the parameter -kubelet-client-certificate and kubelet-client-key must be set. This parameter gives the location of the certificate and key pair used to secure API Server communication.
</p>
                            </div>
                            
                            
                            <div class="detail-section">
                                <h4>Check Text</h4>
                                <pre>Change to the /etc/kubernetes/manifests/ directory on the Kubernetes Control Plane. Run the command:
grep -i kubelet-client-certificate *
grep -I kubelet-client-key * 

If the setting &#34;--kubelet-client-certificate&#34; is not configured in the Kubernetes API server manifest file or contains no value, this is a finding.

If the setting &#34;--kubelet-client-key&#34; is not configured in the Kubernetes API server manifest file or contains no value, this is a finding.
</pre>
                            </div>
                            
                            
                            
                            <div class="detail-section">
                                <h4>Fix Text</h4>
                                <pre>Edit the Kubernetes API Server manifest file in the /etc/kubernetes/manifests directory on the Kubernetes Control Plane. Set the value of &#34;--kubelet-client-certificate&#34; and &#34;--kubelet-client-key&#34; to an Approved Organizational Certificate and key pair.
</pre>
                            </div>
                            
                            
                            
                            <div class="detail-section">
                                <h4>Result Message</h4>
                                <p>PASS: API Server kubelet-client-certificate is configured: --kubelet-client-certificate=/etc/kubernetes/pki/apiserver-kubelet-client.crt (kubectl)</p>
                            </div>
                            
                            
                            
                        </div>
                    </div>
                </div>
                
                
                <div class="finding-item">
                    <div class="finding-header" onclick="toggleFinding(this)">
                        <div class="finding-title-section">
                            <div class="finding-id">V-242381</div>
                            <div class="finding-title">The Kubernetes Controller Manager must create unique service accounts for each work payload.</div>
                            <div class="finding-meta">
                                <span class="severity-badge severity-high">high</span>
                                <span class="status-badge status-pass-pass">PASS</span>
                                <span class="info-value">platform</span>
                            </div>
                        </div>
                        <div class="expand-icon">▼</div>
                    </div>
                    <div class="finding-content">
                        <div class="finding-details">
                            <div class="detail-section">
                                <h4>Description</h4>
                                <p>The Kubernetes Controller Manager is a background process that embeds core control loops regulating cluster system state through the API Server. Every process executed in a pod has an associated service account. By default, service accounts use the same credentials for authentication. Implementing the default settings poses a High risk to the Kubernetes Controller Manager. Setting the &#34;--use-service-account-credential&#34; value lowers the attack surface by generating unique service accounts settings for each controller instance.
</p>
                            </div>
                            
                            
                            <div class="detail-section">
                                <h4>Check Text</h4>
                                <pre>Change to the /etc/kubernetes/manifests directory on the Kubernetes Control Plane. Run the command:
grep -i use-service-account-credentials *

If the setting &#34;--use-service-account-credentials&#34; is not configured in the Kubernetes Controller Manager manifest file or it is set to &#34;false&#34;, this is a finding.
</pre>
                            </div>
                            
                            
                            
                            <div class="detail-section">
                                <h4>Fix Text</h4>
                                <pre>Edit the Kubernetes Controller Manager manifest file in the /etc/kubernetes/manifests directory on the Kubernetes Control Plane.

Set the value of &#34;--use-service-account-credentials&#34; to &#34;true&#34;.
</pre>
                            </div>
                            
                            
                            
                            <div class="detail-section">
                                <h4>Result Message</h4>
                                <p>Check passed</p>
                            </div>
                            
                            
                            
                        </div>
                    </div>
                </div>
                
                
                <div class="finding-item">
                    <div class="finding-header" onclick="toggleFinding(this)">
                        <div class="finding-title-section">
                            <div class="finding-id">V-242381</div>
                            <div class="finding-title">The Kubernetes Controller Manager must create unique service accounts for each work payload.</div>
                            <div class="finding-meta">
                                <span class="severity-badge severity-high">high</span>
                                <span class="status-badge status-pass-pass">PASS</span>
                                <span class="info-value">platform</span>
                            </div>
                        </div>
                        <div class="expand-icon">▼</div>
                    </div>
                    <div class="finding-content">
                        <div class="finding-details">
                            <div class="detail-section">
                                <h4>Description</h4>
                                <p>The Kubernetes Controller Manager is a background process that embeds core control loops regulating cluster system state through the API Server. Every process executed in a pod has an associated service account. By default, service accounts use the same credentials for authentication. Implementing the default settings poses a High risk to the Kubernetes Controller Manager. Setting the &#34;--use-service-account-credential&#34; value lowers the attack surface by generating unique service accounts settings for each controller instance.
</p>
                            </div>
                            
                            
                            <div class="detail-section">
                                <h4>Check Text</h4>
                                <pre>Change to the /etc/kubernetes/manifests directory on the Kubernetes Control Plane. Run the command:

grep -i use-service-account-credentials * 

If the setting &#34;--use-service-account-credentials&#34; is not configured in the Kubernetes Controller Manager manifest file or it is set to &#34;false&#34;, this is a finding.
</pre>
                            </div>
                            
                            
                            
                            <div class="detail-section">
                                <h4>Fix Text</h4>
                                <pre>Edit the Kubernetes Controller Manager manifest file in the /etc/kubernetes/manifests directory on the Kubernetes Control Plane.

Set the value of &#34;--use-service-account-credentials&#34; to &#34;true&#34;.
</pre>
                            </div>
                            
                            
                            
                            <div class="detail-section">
                                <h4>Result Message</h4>
                                <p>PASS: Controller Manager use-service-account-credentials is enabled (kubectl)</p>
                            </div>
                            
                            
                            
                        </div>
                    </div>
                </div>
                
                
                <div class="finding-item">
                    <div class="finding-header" onclick="toggleFinding(this)">
                        <div class="finding-title-section">
                            <div class="finding-id">V-242405</div>
                            <div class="finding-title">Kubernetes must have a Pod Security Admission control file configured.</div>
                            <div class="finding-meta">
                                <span class="severity-badge severity-high">high</span>
                                <span class="status-badge status-pass-pass">PASS</span>
                                <span class="info-value">platform</span>
                            </div>
                        </div>
                        <div class="expand-icon">▼</div>
                    </div>
                    <div class="finding-content">
                        <div class="finding-details">
                            <div class="detail-section">
                                <h4>Description</h4>
                                <p>Pod security restrictions are applied at the namespace level when pods are created. Pod Security Admission is an admission controller that validates pods against the Pod Security Standards and either allows, denies, or warns about the pod creation based on the configured policy.

The admission control configuration file defines the policies and settings for various admission controllers, including Pod Security Admission. This file ensures that consistent security policies are applied across the cluster and that pods meet the defined security requirements before they are created.

Without proper admission control configuration, pods may be created with insecure settings that could compromise the cluster&#39;s security posture.
</p>
                            </div>
                            
                            
                            <div class="detail-section">
                                <h4>Check Text</h4>
                                <pre>Change to the /etc/kubernetes/manifests directory on the Kubernetes Control Plane. Run the command:
grep -i admission-control-config-file *

Review the \&#34;--admission-control-config-file\&#34; setting in the Kubernetes API Server manifest file.

If the \&#34;--admission-control-config-file\&#34; setting is not configured, this is a finding.

Verify that the referenced admission control configuration file exists and contains appropriate Pod Security Admission settings.
</pre>
                            </div>
                            
                            
                            
                            <div class="detail-section">
                                <h4>Fix Text</h4>
                                <pre>Create a Pod Security Admission control configuration file and configure the API Server to use it:

1. Create an admission control configuration file (e.g., /etc/kubernetes/admission-control-config.yaml)
2. Configure Pod Security Admission policies in the file
3. Edit the Kubernetes API Server manifest file to include:
   --admission-control-config-file=/etc/kubernetes/admission-control-config.yaml

Example admission control configuration:
apiVersion: apiserver.config.k8s.io/v1
kind: AdmissionConfiguration
plugins:
- name: PodSecurity
  configuration:
    apiVersion: pod-security.admission.config.k8s.io/v1beta1
    kind: PodSecurityConfiguration
    defaults:
      enforce: \&#34;baseline\&#34;
      audit: \&#34;baseline\&#34;
      warn: \&#34;baseline\&#34;
</pre>
                            </div>
                            
                            
                            
                            <div class="detail-section">
                                <h4>Result Message</h4>
                                <p>Check passed</p>
                            </div>
                            
                            
                            
                        </div>
                    </div>
                </div>
                
                
                <div class="finding-item">
                    <div class="finding-header" onclick="toggleFinding(this)">
                        <div class="finding-title-section">
                            <div class="finding-id">V-242406</div>
                            <div class="finding-title">Kubernetes must enable PodSecurity admission controller.</div>
                            <div class="finding-meta">
                                <span class="severity-badge severity-high">high</span>
                                <span class="status-badge status-fail-fail">FAIL</span>
                                <span class="info-value">platform</span>
                            </div>
                        </div>
                        <div class="expand-icon">▼</div>
                    </div>
                    <div class="finding-content">
                        <div class="finding-details">
                            <div class="detail-section">
                                <h4>Description</h4>
                                <p>PodSecurity is an admission controller that validates and enforces security policies for pods running within a Kubernetes cluster. It replaces the deprecated Pod Security Policies (PSPs) and provides a more streamlined approach to pod security.

The PodSecurity admission controller enforces Pod Security Standards at the namespace level. These standards define three different policies to broadly cover the security spectrum:
- Privileged: Unrestricted policy, providing the widest possible level of permissions
- Baseline: Minimally restrictive policy which prevents known privilege escalations
- Restricted: Heavily restricted policy, following current Pod hardening best practices

Without proper pod security controls, containers may run with excessive privileges, potentially compromising the security of the entire cluster.
</p>
                            </div>
                            
                            
                            <div class="detail-section">
                                <h4>Check Text</h4>
                                <pre>On the Control Plane, run the command:
ps -ef | grep kube-apiserver

Check the output for the \&#34;--enable-admission-plugins\&#34; setting. Verify that PodSecurity is included in the list of enabled admission plugins.

If PodSecurity is not listed in the \&#34;--enable-admission-plugins\&#34; setting, check if it&#39;s listed in the \&#34;--disable-admission-plugins\&#34; setting.

If PodSecurity is listed in the \&#34;--disable-admission-plugins\&#34; setting, this is a finding.

If PodSecurity is not explicitly enabled or disabled, check the Kubernetes version:
kubectl version

For Kubernetes 1.25 and later, PodSecurity is enabled by default. For earlier versions, it must be explicitly enabled.
</pre>
                            </div>
                            
                            
                            
                            <div class="detail-section">
                                <h4>Fix Text</h4>
                                <pre>Edit the Kubernetes API Server manifest file (usually located at /etc/kubernetes/manifests/kube-apiserver.yaml):

Add or modify the \&#34;--enable-admission-plugins\&#34; setting to include PodSecurity:
--enable-admission-plugins=NodeRestriction,PodSecurity

If PodSecurity is listed in \&#34;--disable-admission-plugins\&#34;, remove it from that list.

Restart the API Server to apply the changes.
</pre>
                            </div>
                            
                            
                            
                            <div class="detail-section">
                                <h4>Result Message</h4>
                                <p>FAIL: PodSecurity admission controller is explicitly disabled (process)</p>
                            </div>
                            
                            
                            
                        </div>
                    </div>
                </div>
                
                
                <div class="finding-item">
                    <div class="finding-header" onclick="toggleFinding(this)">
                        <div class="finding-title-section">
                            <div class="finding-id">V-242415</div>
                            <div class="finding-title">Secrets in Kubernetes must not be stored as environment variables.</div>
                            <div class="finding-meta">
                                <span class="severity-badge severity-high">high</span>
                                <span class="status-badge status-fail-fail">FAIL</span>
                                <span class="info-value">platform</span>
                            </div>
                        </div>
                        <div class="expand-icon">▼</div>
                    </div>
                    <div class="finding-content">
                        <div class="finding-details">
                            <div class="detail-section">
                                <h4>Description</h4>
                                <p>Secrets are stored in the etcd datastore as plaintext. Additionally, anyone who is authorized to create a pod in a namespace can use that access to read any secret in that namespace; this includes indirect access such as the ability to create a deployment. Secrets should be mounted as data volumes instead of environment variables.

When secrets are stored as environment variables, they are visible in the process list and can be accessed through various means including the Kubernetes API, container inspection tools, and process dumps. This increases the risk of credential exposure.

Instead of using environment variables, secrets should be mounted as volumes or accessed through the Kubernetes API with proper authentication and authorization.
</p>
                            </div>
                            
                            
                            <div class="detail-section">
                                <h4>Check Text</h4>
                                <pre>To check for secrets stored as environment variables, run the command:
kubectl get all -o jsonpath=&#39;{range .items[*]}{\&#34;\n\&#34;}{.kind}{\&#34;\t\&#34;}{.metadata.name}{\&#34;\t\&#34;}{range .spec.containers[*]}{.env[*].valueFrom.secretKeyRef}{end}{end}&#39; --all-namespaces

Review the output for any entries that show secrets being used as environment variables through secretKeyRef.

If any secrets are found to be stored as environment variables, this is a finding.
</pre>
                            </div>
                            
                            
                            
                            <div class="detail-section">
                                <h4>Fix Text</h4>
                                <pre>Remove the environment variables that are using secretKeyRef and instead mount the secrets as volumes or use other secure methods to access the secrets.

Example of mounting a secret as a volume:
volumes:
- name: secret-volume
  secret:
    secretName: mysecret

Then mount it in the container:
volumeMounts:
- name: secret-volume
  mountPath: \&#34;/etc/secret-volume\&#34;
  readOnly: true
</pre>
                            </div>
                            
                            
                            
                            <div class="detail-section">
                                <h4>Result Message</h4>
                                <p>FAIL: Found pods using secrets as environment variables</p>
                            </div>
                            
                            
                            
                        </div>
                    </div>
                </div>
                
                
                <div class="finding-item">
                    <div class="finding-header" onclick="toggleFinding(this)">
                        <div class="finding-title-section">
                            <div class="finding-id">V-242383</div>
                            <div class="finding-title">User-managed resources must be created in dedicated namespaces.</div>
                            <div class="finding-meta">
                                <span class="severity-badge severity-high">high</span>
                                <span class="status-badge status-pass-pass">PASS</span>
                                <span class="info-value">platform</span>
                            </div>
                        </div>
                        <div class="expand-icon">▼</div>
                    </div>
                    <div class="finding-content">
                        <div class="finding-details">
                            <div class="detail-section">
                                <h4>Description</h4>
                                <p>Creating namespaces for user-managed resources is important when implementing Role-Based Access Controls (RBAC). RBAC allows for the authorization of users and helps support proper API server permissions separation and network micro segmentation. If user-managed resources are placed within the default namespaces, it becomes impossible to implement policies for RBAC permission, service account usage, network policies, and more.
</p>
                            </div>
                            
                            
                            <div class="detail-section">
                                <h4>Check Text</h4>
                                <pre>To view the available namespaces, run the command:
kubectl get namespaces

The default namespaces to be validated are default, kube-public, and kube-node-lease if it is created.

For the default namespace, execute the commands:
kubectl config set-context --current --namespace=default
kubectl get all

For the kube-public namespace, execute the commands:
kubectl config set-context --current --namespace=kube-public
kubectl get all

For the kube-node-lease namespace, execute the commands:
kubectl config set-context --current --namespace=kube-node-lease
kubectl get all

The only valid return values are the kubernetes service (i.e., service/kubernetes) and nothing at all.

If a return value is returned from the &#34;kubectl get all&#34; command and it is not the kubernetes service (i.e., service/kubernetes), this is a finding.
</pre>
                            </div>
                            
                            
                            
                            <div class="detail-section">
                                <h4>Fix Text</h4>
                                <pre>Move any user-managed resources from the default, kube-public, and kube-node-lease namespaces to user namespaces.
</pre>
                            </div>
                            
                            
                            
                            <div class="detail-section">
                                <h4>Result Message</h4>
                                <p>PASS: No user-managed resources found in system namespaces</p>
                            </div>
                            
                            
                            
                        </div>
                    </div>
                </div>
                
                
                <div class="finding-item">
                    <div class="finding-header" onclick="toggleFinding(this)">
                        <div class="finding-title-section">
                            <div class="finding-id">V-242436</div>
                            <div class="finding-title">The Kubernetes API server must have the ValidatingAdmissionWebhook enabled.</div>
                            <div class="finding-meta">
                                <span class="severity-badge severity-high">high</span>
                                <span class="status-badge status-fail-fail">FAIL</span>
                                <span class="info-value">platform</span>
                            </div>
                        </div>
                        <div class="expand-icon">▼</div>
                    </div>
                    <div class="finding-content">
                        <div class="finding-details">
                            <div class="detail-section">
                                <h4>Description</h4>
                                <p>Enabling the admissions webhook allows for Kubernetes to apply policies against objects that are to be created, read, updated, or deleted. The validating admission webhook is called during the validation phase of the admission control process. This webhook validates the incoming request and can reject requests that do not meet the defined policies.

The ValidatingAdmissionWebhook is a critical security control that allows external systems to validate and potentially reject API requests before they are persisted in etcd. This provides an additional layer of security beyond the built-in admission controllers.
</p>
                            </div>
                            
                            
                            <div class="detail-section">
                                <h4>Check Text</h4>
                                <pre>Change to the /etc/kubernetes/manifests directory on the Kubernetes Control Plane. Run the command:
grep -i enable-admission-plugins *

Review the \&#34;--enable-admission-plugins\&#34; setting in the Kubernetes API Server manifest file.

If the \&#34;--enable-admission-plugins\&#34; setting does not contain \&#34;ValidatingAdmissionWebhook\&#34;, this is a finding.

Also verify that ValidatingAdmissionWebhook is not listed in the \&#34;--disable-admission-plugins\&#34; setting.
</pre>
                            </div>
                            
                            
                            
                            <div class="detail-section">
                                <h4>Fix Text</h4>
                                <pre>Edit the Kubernetes API Server manifest file in the /etc/kubernetes/manifests directory on the Kubernetes Control Plane.

Add \&#34;ValidatingAdmissionWebhook\&#34; to the \&#34;--enable-admission-plugins\&#34; setting.

If ValidatingAdmissionWebhook is listed in \&#34;--disable-admission-plugins\&#34;, remove it from that list.

Example: --enable-admission-plugins=NodeRestriction,ValidatingAdmissionWebhook
</pre>
                            </div>
                            
                            
                            
                            <div class="detail-section">
                                <h4>Result Message</h4>
                                <p>FAIL: ValidatingAdmissionWebhook is explicitly disabled (process)</p>
                            </div>
                            
                            
                            
                        </div>
                    </div>
                </div>
                
                
                <div class="finding-item">
                    <div class="finding-header" onclick="toggleFinding(this)">
                        <div class="finding-title-section">
                            <div class="finding-id">V-242400</div>
                            <div class="finding-title">The Kubernetes API server must have Alpha APIs disabled.</div>
                            <div class="finding-meta">
                                <span class="severity-badge severity-medium">medium</span>
                                <span class="status-badge status-fail-fail">FAIL</span>
                                <span class="info-value">platform</span>
                            </div>
                        </div>
                        <div class="expand-icon">▼</div>
                    </div>
                    <div class="finding-content">
                        <div class="finding-details">
                            <div class="detail-section">
                                <h4>Description</h4>
                                <p>Kubernetes allows alpha API calls within the API server. The alpha features are disabled by default since they are not ready for production and likely to change without notice. These features may also contain security issues that are rectified as the feature matures. To keep the Kubernetes cluster secure and stable, these alpha features must not be used.
</p>
                            </div>
                            
                            
                            <div class="detail-section">
                                <h4>Check Text</h4>
                                <pre>On the Control Plane, change to the manifests&#39; directory at /etc/kubernetes/manifests and run the command:
grep -i feature-gates *

Review the &#34;--feature-gates&#34; setting, if one is returned.

If the &#34;--feature-gate&#34;s setting is available and contains the &#34;AllAlpha&#34; flag set to &#34;true&#34;, this is a finding.
</pre>
                            </div>
                            
                            
                            
                            <div class="detail-section">
                                <h4>Fix Text</h4>
                                <pre>Edit any manifest file that contains the &#34;--feature-gates&#34; setting with &#34;AllAlpha&#34; set to &#34;true&#34;.

Set the value of &#34;AllAlpha&#34; to &#34;false&#34; or remove the setting completely. (AllAlpha - default=false)
</pre>
                            </div>
                            
                            
                            
                            <div class="detail-section">
                                <h4>Result Message</h4>
                                <p>FAIL: AllAlpha feature gate is enabled (process)</p>
                            </div>
                            
                            
                            
                        </div>
                    </div>
                </div>
                
                
                <div class="finding-item">
                    <div class="finding-header" onclick="toggleFinding(this)">
                        <div class="finding-title-section">
                            <div class="finding-id">V-242461</div>
                            <div class="finding-title">Kubernetes API Server audit logs must be enabled.</div>
                            <div class="finding-meta">
                                <span class="severity-badge severity-medium">medium</span>
                                <span class="status-badge status-pass-pass">PASS</span>
                                <span class="info-value">platform</span>
                            </div>
                        </div>
                        <div class="expand-icon">▼</div>
                    </div>
                    <div class="finding-content">
                        <div class="finding-details">
                            <div class="detail-section">
                                <h4>Description</h4>
                                <p>Kubernetes API Server validates and configures pods and services for the API object. The REST operation provides frontend functionality to the cluster share state. Enabling audit logs provides a way to monitor and identify security risk events or misuse of information. Audit logs are necessary to provide evidence in the case the Kubernetes API Server is compromised requiring a Cyber Security Investigation.
</p>
                            </div>
                            
                            
                            <div class="detail-section">
                                <h4>Check Text</h4>
                                <pre>Change to the /etc/kubernetes/manifests directory on the Kubernetes Control Plane. Run the command:
grep -i audit-policy-file * 

If the setting &#34;audit-policy-file&#34; is not set or is found in the Kubernetes API manifest file without valid content, this is a finding.
</pre>
                            </div>
                            
                            
                            
                            <div class="detail-section">
                                <h4>Fix Text</h4>
                                <pre>Edit the Kubernetes API Server manifest file in the /etc/kubernetes/manifests directory on the Kubernetes Control Plane. Set the argument &#34;--audit-policy-file&#34; to &#34;log file directory&#34;.
</pre>
                            </div>
                            
                            
                            
                            <div class="detail-section">
                                <h4>Result Message</h4>
                                <p>PASS: API Server audit logging is properly configured</p>
                            </div>
                            
                            
                            
                        </div>
                    </div>
                </div>
                
                
                <div class="finding-item">
                    <div class="finding-header" onclick="toggleFinding(this)">
                        <div class="finding-title-section">
                            <div class="finding-id">V-242462</div>
                            <div class="finding-title">Kubernetes API Server audit log retention must be set.</div>
                            <div class="finding-meta">
                                <span class="severity-badge severity-medium">medium</span>
                                <span class="status-badge status-pass-pass">PASS</span>
                                <span class="info-value">platform</span>
                            </div>
                        </div>
                        <div class="expand-icon">▼</div>
                    </div>
                    <div class="finding-content">
                        <div class="finding-details">
                            <div class="detail-section">
                                <h4>Description</h4>
                                <p>Kubernetes API Server validates and configures pods and services for the API object. The REST operation provides frontend functionality to the cluster share state. Audit logs capture individual user, administrator, and system accesses to the API Server. The retention of the audit logs is critical to forensic investigations and the ability to troubleshoot system issues. The audit log retention must be configured to prevent the loss of audit data.
</p>
                            </div>
                            
                            
                            <div class="detail-section">
                                <h4>Check Text</h4>
                                <pre>Change to the /etc/kubernetes/manifests directory on the Kubernetes Control Plane. Run the command:
grep -i audit-log-maxage *

If the setting audit-log-maxage is not set or is set to less than 30, this is a finding.
</pre>
                            </div>
                            
                            
                            
                            <div class="detail-section">
                                <h4>Fix Text</h4>
                                <pre>Edit the Kubernetes API Server manifest file in the /etc/kubernetes/manifests directory on the Kubernetes Control Plane. Set the argument &#34;--audit-log-maxage&#34; to &#34;30&#34; or more.
</pre>
                            </div>
                            
                            
                            
                            <div class="detail-section">
                                <h4>Result Message</h4>
                                <p>PASS: API Server audit-log-maxage is properly configured: 30 days</p>
                            </div>
                            
                            
                            
                        </div>
                    </div>
                </div>
                
                
                <div class="finding-item">
                    <div class="finding-header" onclick="toggleFinding(this)">
                        <div class="finding-title-section">
                            <div class="finding-id">V-242463</div>
                            <div class="finding-title">Kubernetes API Server audit log maximum backup must be set.</div>
                            <div class="finding-meta">
                                <span class="severity-badge severity-medium">medium</span>
                                <span class="status-badge status-pass-pass">PASS</span>
                                <span class="info-value">platform</span>
                            </div>
                        </div>
                        <div class="expand-icon">▼</div>
                    </div>
                    <div class="finding-content">
                        <div class="finding-details">
                            <div class="detail-section">
                                <h4>Description</h4>
                                <p>Kubernetes API Server validates and configures pods and services for the API object. The REST operation provides frontend functionality to the cluster share state. Audit logs capture individual user, administrator, and system accesses to the API Server. The audit log maximum backup setting determines the maximum number of audit log files to retain. The backup retention must be configured to prevent the loss of audit data.
</p>
                            </div>
                            
                            
                            <div class="detail-section">
                                <h4>Check Text</h4>
                                <pre>Change to the /etc/kubernetes/manifests directory on the Kubernetes Control Plane. Run the command:
grep -i audit-log-maxbackup *

If the setting audit-log-maxbackup is not set or is set to less than 10, this is a finding.
</pre>
                            </div>
                            
                            
                            
                            <div class="detail-section">
                                <h4>Fix Text</h4>
                                <pre>Edit the Kubernetes API Server manifest file in the /etc/kubernetes/manifests directory on the Kubernetes Control Plane. Set the argument &#34;--audit-log-maxbackup&#34; to &#34;10&#34; or more.
</pre>
                            </div>
                            
                            
                            
                            <div class="detail-section">
                                <h4>Result Message</h4>
                                <p>PASS: API Server audit-log-maxbackup is properly configured: 10 backups</p>
                            </div>
                            
                            
                            
                        </div>
                    </div>
                </div>
                
                
                <div class="finding-item">
                    <div class="finding-header" onclick="toggleFinding(this)">
                        <div class="finding-title-section">
                            <div class="finding-id">V-242464</div>
                            <div class="finding-title">Kubernetes API Server audit log maximum size must be set.</div>
                            <div class="finding-meta">
                                <span class="severity-badge severity-medium">medium</span>
                                <span class="status-badge status-pass-pass">PASS</span>
                                <span class="info-value">platform</span>
                            </div>
                        </div>
                        <div class="expand-icon">▼</div>
                    </div>
                    <div class="finding-content">
                        <div class="finding-details">
                            <div class="detail-section">
                                <h4>Description</h4>
                                <p>Kubernetes API Server validates and configures pods and services for the API object. The REST operation provides frontend functionality to the cluster share state. Audit logs capture individual user, administrator, and system accesses to the API Server. The audit log maximum size setting determines the maximum size of individual audit log files before they are rotated. The maximum size must be configured to prevent the loss of audit data.
</p>
                            </div>
                            
                            
                            <div class="detail-section">
                                <h4>Check Text</h4>
                                <pre>Change to the /etc/kubernetes/manifests directory on the Kubernetes Control Plane. Run the command:
grep -i audit-log-maxsize *

If the setting audit-log-maxsize is not set or is set to less than 100, this is a finding.
</pre>
                            </div>
                            
                            
                            
                            <div class="detail-section">
                                <h4>Fix Text</h4>
                                <pre>Edit the Kubernetes API Server manifest file in the /etc/kubernetes/manifests directory on the Kubernetes Control Plane. Set the argument &#34;--audit-log-maxsize&#34; to &#34;100&#34; or more.
</pre>
                            </div>
                            
                            
                            
                            <div class="detail-section">
                                <h4>Result Message</h4>
                                <p>PASS: API Server audit-log-maxsize is properly configured: 200 MB</p>
                            </div>
                            
                            
                            
                        </div>
                    </div>
                </div>
                
                
                <div class="finding-item">
                    <div class="finding-header" onclick="toggleFinding(this)">
                        <div class="finding-title-section">
                            <div class="finding-id">V-242403</div>
                            <div class="finding-title">Kubernetes API Server must generate audit records that identify what type of event has occurred, identify the source of the event, contain the event results, identify any users, and identify any containers associated with the event.</div>
                            <div class="finding-meta">
                                <span class="severity-badge severity-medium">medium</span>
                                <span class="status-badge status-pass-pass">PASS</span>
                                <span class="info-value">platform</span>
                            </div>
                        </div>
                        <div class="expand-icon">▼</div>
                    </div>
                    <div class="finding-content">
                        <div class="finding-details">
                            <div class="detail-section">
                                <h4>Description</h4>
                                <p>Within Kubernetes, audit data for all components is generated by the API server. This audit data is important when there are issues, to include security incidents that must be investigated. To make the audit data worthwhile for the investigation of events, it is necessary to have the appropriate and required data logged. To fully understand the event, it is important to identify any users associated with the event.

The API server policy file allows for the following levels of auditing:
      None - Do not log events that match the rule.
      Metadata - Log request metadata (requesting user, timestamp, resource, verb, etc.) but not request or response body.
      Request - Log event metadata and request body but not response body.
      RequestResponse - Log event metadata, request, and response bodies.

Satisfies: SRGID:SRG-APP-000092-CTR-000165, SRG-APP-000026-CTR-000070, SRG-APP-000027-CTR-000075, SRG-APP-000028-CTR-000080, SRG-APP-000101-CTR-000205, SRG-APP-000100-CTR-000200, SRG-APP-000100-CTR-000195, SRG-APP-000099-CTR-000190, SRG-APP-000098-CTR-000185, SRG-APP-000095-CTR-000170, SRG-APP-000096-CTR-000175, SRG-APP-000097-CTR-000180, SRG-APP-000507-CTR-001295, SRG-APP-000504-CTR-001280, SRG-APP-000503-CTR-001275, SRG-APP-000501-CTR-001265, SRG-APP-000500-CTR-001260, SRG-APP-000497-CTR-001245, SRG-APP-000496-CTR-001240, SRG-APP-000493-CTR-001225, SRG-APP-000492-CTR-001220, SRG-APP-000343-CTR-000780, SRG-APP-000381-CTR-000905
</p>
                            </div>
                            
                            
                            <div class="detail-section">
                                <h4>Check Text</h4>
                                <pre>Change to the /etc/kubernetes/manifests/ directory on the Kubernetes Control Plane. Run the command:
grep -i audit-policy-file 

If the audit-policy-file is not set, this is a finding.

The file given is the policy file and defines what is audited and what information is included with each event.

The policy file must look like this:

# Log all requests at the RequestResponse level.
apiVersion: audit.k8s.io/vX (Where X is the latest apiVersion)
kind: Policy
rules:
- level: RequestResponse

If the audit policy file does not look like above, this is a finding.
</pre>
                            </div>
                            
                            
                            
                            <div class="detail-section">
                                <h4>Fix Text</h4>
                                <pre>Edit the Kubernetes API Server manifest file in the /etc/kubernetes/manifests directory on the Kubernetes Control Plane.

Set the value of &#34;--audit-policy-file&#34; to the path of a file with the following content:
    
    # Log all requests at the RequestResponse level.
    apiVersion: audit.k8s.io/vX (Where X is the latest apiVersion)
    kind: Policy
    rules:
    - level: RequestResponse

Note: If the API server is running as a Pod, then the manifest will also need to be updated to mount the host system filesystem where the audit policy file resides.
</pre>
                            </div>
                            
                            
                            
                            <div class="detail-section">
                                <h4>Result Message</h4>
                                <p>PASS: Audit policy file parameter is configured</p>
                            </div>
                            
                            
                            
                        </div>
                    </div>
                </div>
                
                
                <div class="finding-item">
                    <div class="finding-header" onclick="toggleFinding(this)">
                        <div class="finding-title-section">
                            <div class="finding-id">V-242382</div>
                            <div class="finding-title">The Kubernetes API Server must enable Node,RBAC as the authorization mode.</div>
                            <div class="finding-meta">
                                <span class="severity-badge severity-medium">medium</span>
                                <span class="status-badge status-pass-pass">PASS</span>
                                <span class="info-value">platform</span>
                            </div>
                        </div>
                        <div class="expand-icon">▼</div>
                    </div>
                    <div class="finding-content">
                        <div class="finding-details">
                            <div class="detail-section">
                                <h4>Description</h4>
                                <p>To mitigate the risk of unauthorized access to sensitive information by entities that have been issued certificates by DOD-approved PKIs, all DOD systems (e.g., networks, web servers, and web portals) must be properly configured to incorporate access control methods that do not rely solely on the possession of a certificate for access. Successful authentication must not automatically give an entity access to an asset or security boundary. Authorization procedures and controls must be implemented to ensure each authenticated entity also has a validated and current authorization. Authorization is the process of determining whether an entity, once authenticated, is permitted to access a specific asset.

Node,RBAC is the method within Kubernetes to control access of users and applications. Kubernetes uses roles to grant authorization API requests made by kubelets.

Satisfies: SRG-APP-000340-CTR-000770, SRG-APP-000033-CTR-000095, SRG-APP-000378-CTR-000880, SRG-APP-000033-CTR-000090
</p>
                            </div>
                            
                            
                            <div class="detail-section">
                                <h4>Check Text</h4>
                                <pre>Change to the /etc/kubernetes/manifests/ directory on the Kubernetes Control Plane. Run the command:
grep -i authorization-mode *

If the setting authorization-mode is set to &#34;AlwaysAllow&#34; in the Kubernetes API Server manifest file or is not configured, this is a finding.
</pre>
                            </div>
                            
                            
                            
                            <div class="detail-section">
                                <h4>Fix Text</h4>
                                <pre>Edit the Kubernetes API Server manifest file in the /etc/kubernetes/manifests directory on the Kubernetes Control Plane.

Set the value of &#34;--authorization-mode&#34; to &#34;Node,RBAC&#34;.
</pre>
                            </div>
                            
                            
                            
                            <div class="detail-section">
                                <h4>Result Message</h4>
                                <p>Check passed</p>
                            </div>
                            
                            
                            
                        </div>
                    </div>
                </div>
                
                
                <div class="finding-item">
                    <div class="finding-header" onclick="toggleFinding(this)">
                        <div class="finding-title-section">
                            <div class="finding-id">V-242418</div>
                            <div class="finding-title">The Kubernetes API server must use approved cipher suites.</div>
                            <div class="finding-meta">
                                <span class="severity-badge severity-medium">medium</span>
                                <span class="status-badge status-pass-pass">PASS</span>
                                <span class="info-value">platform</span>
                            </div>
                        </div>
                        <div class="expand-icon">▼</div>
                    </div>
                    <div class="finding-content">
                        <div class="finding-details">
                            <div class="detail-section">
                                <h4>Description</h4>
                                <p>The Kubernetes API server communicates to the kubelet service on the nodes to deploy, update, and delete resources. If an attacker were able to get between this communication and modify the request, the Kubernetes cluster could be compromised. Using approved cypher suites for the communication ensures the protection of the transmitted information, confidentiality, and integrity so that the attacker cannot read or alter this communication.
</p>
                            </div>
                            
                            
                            <div class="detail-section">
                                <h4>Check Text</h4>
                                <pre>Change to the /etc/kubernetes/manifests/ directory on the Kubernetes Control Plane. Run the command:
grep -i tls-cipher-suites *

If the setting feature tls-cipher-suites is not set in the Kubernetes API server manifest file or contains no value or does not contain TLS_ECDHE_ECDSA_WITH_AES_128_GCM_SHA256,TLS_ECDHE_RSA_WITH_AES_128_GCM_SHA256,TLS_ECDHE_RSA_WITH_AES_256_GCM_SHA384,TLS_ECDHE_ECDSA_WITH_AES_256_GCM_SHA384, this is a finding.
</pre>
                            </div>
                            
                            
                            
                            <div class="detail-section">
                                <h4>Fix Text</h4>
                                <pre>Edit the Kubernetes API Server manifest file in the /etc/kubernetes/manifests directory on the Kubernetes Control Plane.

Set the value of &#34;--tls-cipher-suites&#34; to:
&#34;TLS_ECDHE_ECDSA_WITH_AES_128_GCM_SHA256,TLS_ECDHE_RSA_WITH_AES_128_GCM_SHA256,TLS_ECDHE_RSA_WITH_AES_256_GCM_SHA384,TLS_ECDHE_ECDSA_WITH_AES_256_GCM_SHA384&#34;
</pre>
                            </div>
                            
                            
                            
                            <div class="detail-section">
                                <h4>Result Message</h4>
                                <p>Check passed</p>
                            </div>
                            
                            
                            
                        </div>
                    </div>
                </div>
                
                
                <div class="finding-item">
                    <div class="finding-header" onclick="toggleFinding(this)">
                        <div class="finding-title-section">
                            <div class="finding-id">V-242419</div>
                            <div class="finding-title">Kubernetes API Server must have the SSL Certificate Authority set.</div>
                            <div class="finding-meta">
                                <span class="severity-badge severity-medium">medium</span>
                                <span class="status-badge status-pass-pass">PASS</span>
                                <span class="info-value">platform</span>
                            </div>
                        </div>
                        <div class="expand-icon">▼</div>
                    </div>
                    <div class="finding-content">
                        <div class="finding-details">
                            <div class="detail-section">
                                <h4>Description</h4>
                                <p>Kubernetes control plane and external communication are managed by API Server. The main implementation of the API Server is to manage hardware resources for pods and containers using horizontal or vertical scaling. Anyone who can access the API Server can effectively control the Kubernetes architecture. Using authenticity protection, the communication can be protected against man-in-the-middle attacks/session hijacking and the insertion of false information into sessions.

The communication session is protected by utilizing transport encryption protocols such as TLS. TLS provides the Kubernetes API Server with a means to authenticate sessions and encrypt traffic. 

To enable encrypted communication for API Server, the parameter client-ca-file must be set. This parameter gives the location of the SSL Certificate Authority file used to secure API Server communication.
</p>
                            </div>
                            
                            
                            <div class="detail-section">
                                <h4>Check Text</h4>
                                <pre>Change to the /etc/kubernetes/manifests/ directory on the Kubernetes Control Plane. Run the command:
grep -i client-ca-file *

If the setting feature client-ca-file is not set in the Kubernetes API server manifest file or contains no value, this is a finding.
</pre>
                            </div>
                            
                            
                            
                            <div class="detail-section">
                                <h4>Fix Text</h4>
                                <pre>Edit the Kubernetes API Server manifest file in the /etc/kubernetes/manifests directory on the Kubernetes Control Plane. 

Set the value of &#34;--client-ca-file&#34; to path containing Approved Organizational Certificate.
</pre>
                            </div>
                            
                            
                            
                            <div class="detail-section">
                                <h4>Result Message</h4>
                                <p>PASS: API Server client-ca-file is configured: --client-ca-file=/etc/kubernetes/pki/ca.crt (kubectl)</p>
                            </div>
                            
                            
                            
                        </div>
                    </div>
                </div>
                
                
                <div class="finding-item">
                    <div class="finding-header" onclick="toggleFinding(this)">
                        <div class="finding-title-section">
                            <div class="finding-id">V-242429</div>
                            <div class="finding-title">Kubernetes etcd must have the SSL Certificate Authority set.</div>
                            <div class="finding-meta">
                                <span class="severity-badge severity-medium">medium</span>
                                <span class="status-badge status-pass-pass">PASS</span>
                                <span class="info-value">platform</span>
                            </div>
                        </div>
                        <div class="expand-icon">▼</div>
                    </div>
                    <div class="finding-content">
                        <div class="finding-details">
                            <div class="detail-section">
                                <h4>Description</h4>
                                <p>Kubernetes stores configuration and state information in a distributed key-value store called etcd. Anyone who can write to etcd can effectively control a Kubernetes cluster. Even just reading the contents of etcd could easily provide helpful hints to a would-be attacker. Using authenticity protection, the communication can be protected against man-in-the-middle attacks/session hijacking and the insertion of false information into sessions.

The communication session is protected by utilizing transport encryption protocols, such as TLS. TLS provides the Kubernetes API Server and etcd with a means to be able to authenticate sessions and encrypt traffic. 

To enable encrypted communication for etcd, the parameter &#34;--etcd-cafile&#34; must be set. This parameter gives the location of the SSL Certificate Authority file used to secure etcd communication.
</p>
                            </div>
                            
                            
                            <div class="detail-section">
                                <h4>Check Text</h4>
                                <pre>Change to the /etc/kubernetes/manifests directory on the Kubernetes Control Plane. Run the command:
grep -i etcd-cafile * 

If the setting &#34;--etcd-cafile&#34; is not configured in the Kubernetes API Server manifest file, this is a finding.
</pre>
                            </div>
                            
                            
                            
                            <div class="detail-section">
                                <h4>Fix Text</h4>
                                <pre>Edit the Kubernetes API Server manifest file in the /etc/kubernetes/manifests directory on the Kubernetes Control Plane. 

Set the value of &#34;--etcd-cafile&#34; to the Certificate Authority for etcd.
</pre>
                            </div>
                            
                            
                            
                            <div class="detail-section">
                                <h4>Result Message</h4>
                                <p>PASS: API Server etcd-cafile is configured: --etcd-cafile=/etc/kubernetes/pki/etcd/ca.crt (kubectl)</p>
                            </div>
                            
                            
                            
                        </div>
                    </div>
                </div>
                
                
                <div class="finding-item">
                    <div class="finding-header" onclick="toggleFinding(this)">
                        <div class="finding-title-section">
                            <div class="finding-id">V-242430</div>
                            <div class="finding-title">Kubernetes etcd must have a certificate for communication.</div>
                            <div class="finding-meta">
                                <span class="severity-badge severity-medium">medium</span>
                                <span class="status-badge status-pass-pass">PASS</span>
                                <span class="info-value">platform</span>
                            </div>
                        </div>
                        <div class="expand-icon">▼</div>
                    </div>
                    <div class="finding-content">
                        <div class="finding-details">
                            <div class="detail-section">
                                <h4>Description</h4>
                                <p>Kubernetes stores configuration and state information in a distributed key-value store called etcd. Anyone who can write to etcd can effectively control the Kubernetes cluster. Even just reading the contents of etcd could easily provide helpful hints to a would-be attacker. Using authenticity protection, the communication can be protected against man-in-the-middle attacks/session hijacking and the insertion of false information into sessions.

The communication session is protected by utilizing transport encryption protocols, such as TLS. TLS provides the Kubernetes API Server and etcd with a means to be able to authenticate sessions and encrypt traffic. 

To enable encrypted communication for etcd, the parameter &#34;--etcd-certfile&#34; must be set. This parameter gives the location of the SSL certification file used to secure etcd communication.
</p>
                            </div>
                            
                            
                            <div class="detail-section">
                                <h4>Check Text</h4>
                                <pre>Change to the /etc/kubernetes/manifests directory on the Kubernetes Control Plane. Run the command:
grep -i etcd-certfile * 

If the setting &#34;--etcd-certfile&#34; is not set in the Kubernetes API Server manifest file, this is a finding.
</pre>
                            </div>
                            
                            
                            
                            <div class="detail-section">
                                <h4>Fix Text</h4>
                                <pre>Edit the Kubernetes API Server manifest file in the /etc/kubernetes/manifests directory on the Kubernetes Control Plane. 

Set the value of &#34;--etcd-certfile&#34; to the certificate to be used for communication with etcd.
</pre>
                            </div>
                            
                            
                            
                            <div class="detail-section">
                                <h4>Result Message</h4>
                                <p>PASS: API Server etcd-certfile is configured: --etcd-certfile=/etc/kubernetes/pki/apiserver-etcd-client.crt (kubectl)</p>
                            </div>
                            
                            
                            
                        </div>
                    </div>
                </div>
                
                
                <div class="finding-item">
                    <div class="finding-header" onclick="toggleFinding(this)">
                        <div class="finding-title-section">
                            <div class="finding-id">V-242431</div>
                            <div class="finding-title">Kubernetes etcd must have a key file for secure communication.</div>
                            <div class="finding-meta">
                                <span class="severity-badge severity-medium">medium</span>
                                <span class="status-badge status-pass-pass">PASS</span>
                                <span class="info-value">platform</span>
                            </div>
                        </div>
                        <div class="expand-icon">▼</div>
                    </div>
                    <div class="finding-content">
                        <div class="finding-details">
                            <div class="detail-section">
                                <h4>Description</h4>
                                <p>Kubernetes stores configuration and state information in a distributed key-value store called etcd. Anyone who can write to etcd can effectively control a Kubernetes cluster. Even just reading the contents of etcd could easily provide helpful hints to a would-be attacker. Using authenticity protection, the communication can be protected against man-in-the-middle attacks/session hijacking and the insertion of false information into sessions.

The communication session is protected by utilizing transport encryption protocols, such as TLS. TLS provides the Kubernetes API Server and etcd with a means to be able to authenticate sessions and encrypt traffic. 

To enable encrypted communication for etcd, the parameter &#34;--etcd-keyfile&#34; must be set. This parameter gives the location of the key file used to secure etcd communication.
</p>
                            </div>
                            
                            
                            <div class="detail-section">
                                <h4>Check Text</h4>
                                <pre>Change to the /etc/kubernetes/manifests directory on the Kubernetes Control Plane. Run the command:
grep -i etcd-keyfile * 

If the setting &#34;--etcd-keyfile&#34; is not configured in the Kubernetes API Server manifest file, this is a finding.
</pre>
                            </div>
                            
                            
                            
                            <div class="detail-section">
                                <h4>Fix Text</h4>
                                <pre>Edit the Kubernetes API Server manifest file in the /etc/kubernetes/manifests directory on the Kubernetes Control Plane. 

Set the value of &#34;--etcd-keyfile&#34; to the certificate to be used for communication with etcd.
</pre>
                            </div>
                            
                            
                            
                            <div class="detail-section">
                                <h4>Result Message</h4>
                                <p>PASS: API Server etcd-keyfile is configured: --etcd-keyfile=/etc/kubernetes/pki/apiserver-etcd-client.key (kubectl)</p>
                            </div>
                            
                            
                            
                        </div>
                    </div>
                </div>
                
                
                <div class="finding-item">
                    <div class="finding-header" onclick="toggleFinding(this)">
                        <div class="finding-title-section">
                            <div class="finding-id">V-242389</div>
                            <div class="finding-title">The Kubernetes API server must have the secure port set.</div>
                            <div class="finding-meta">
                                <span class="severity-badge severity-medium">medium</span>
                                <span class="status-badge status-manual-manual">MANUAL</span>
                                <span class="info-value">platform</span>
                            </div>
                        </div>
                        <div class="expand-icon">▼</div>
                    </div>
                    <div class="finding-content">
                        <div class="finding-details">
                            <div class="detail-section">
                                <h4>Description</h4>
                                <p>By default, the API server will listen on what is rightfully called the secure port, port 6443. Any requests to this port will perform authentication and authorization checks. If this port is disabled, anyone who gains access to the host on which the Control Plane is running has full control of the entire cluster over encrypted traffic.

Open the secure port by setting the API server&#39;s &#34;--secure-port&#34; flag to a value other than &#34;0&#34;.
</p>
                            </div>
                            
                            
                            <div class="detail-section">
                                <h4>Check Text</h4>
                                <pre>Change to the /etc/kubernetes/manifests directory on the Kubernetes Control Plane. Run the command:
grep -i secure-port *

If the setting &#34;--secure-port&#34; is set to &#34;0&#34; or is not configured in the Kubernetes API manifest file, this is a finding.
</pre>
                            </div>
                            
                            
                            
                            <div class="detail-section">
                                <h4>Fix Text</h4>
                                <pre>Edit the Kubernetes API Server manifest file in the /etc/kubernetes/manifests directory on the Kubernetes Control Plane.

Set the value of &#34;--secure-port&#34; to a value greater than &#34;0&#34;.
</pre>
                            </div>
                            
                            
                            
                            <div class="detail-section">
                                <h4>Result Message</h4>
                                <p>WARNING: Grep command produced warnings: grep: warning: stray \ before -</p>
                            </div>
                            
                            
                            
                        </div>
                    </div>
                </div>
                
                
                <div class="finding-item">
                    <div class="finding-header" onclick="toggleFinding(this)">
                        <div class="finding-title-section">
                            <div class="finding-id">V-242422</div>
                            <div class="finding-title">Kubernetes API Server must have a certificate for communication.</div>
                            <div class="finding-meta">
                                <span class="severity-badge severity-medium">medium</span>
                                <span class="status-badge status-pass-pass">PASS</span>
                                <span class="info-value">platform</span>
                            </div>
                        </div>
                        <div class="expand-icon">▼</div>
                    </div>
                    <div class="finding-content">
                        <div class="finding-details">
                            <div class="detail-section">
                                <h4>Description</h4>
                                <p>Kubernetes control plane and external communication is managed by API Server. The main implementation of the API Server is to manage hardware resources for pods and container using horizontal or vertical scaling. Anyone who can access the API Server can effectively control the Kubernetes architecture. Using authenticity protection, the communication can be protected against man-in-the-middle attacks/session hijacking and the insertion of false information into sessions.

The communication session is protected by utilizing transport encryption protocols, such as TLS. TLS provides the Kubernetes API Server with a means to be able to authenticate sessions and encrypt traffic. 

To enable encrypted communication for API Server, the parameter etcd-cafile must be set. This parameter gives the location of the SSL Certificate Authority file used to secure API Server communication.
</p>
                            </div>
                            
                            
                            <div class="detail-section">
                                <h4>Check Text</h4>
                                <pre>Change to the /etc/kubernetes/manifests/ directory on the Kubernetes Control Plane. Run the command:
grep -i tls-cert-file *
grep -i tls-private-key-file *

If the setting tls-cert-file and private-key-file is not set in the Kubernetes API server manifest file or contains no value, this is a finding.
</pre>
                            </div>
                            
                            
                            
                            <div class="detail-section">
                                <h4>Fix Text</h4>
                                <pre>Edit the Kubernetes API Server manifest file in the /etc/kubernetes/manifests directory on the Kubernetes Control Plane. Set the value of tls-cert-file and tls-private-key-file to path containing Approved Organizational Certificate.
</pre>
                            </div>
                            
                            
                            
                            <div class="detail-section">
                                <h4>Result Message</h4>
                                <p>PASS: API Server tls-cert-file is configured: --tls-cert-file=/etc/kubernetes/pki/apiserver.crt (kubectl)</p>
                            </div>
                            
                            
                            
                        </div>
                    </div>
                </div>
                
                
                <div class="finding-item">
                    <div class="finding-header" onclick="toggleFinding(this)">
                        <div class="finding-title-section">
                            <div class="finding-id">V-242378</div>
                            <div class="finding-title">The Kubernetes API Server must use TLS 1.2, at a minimum, to protect the confidentiality of sensitive data during electronic dissemination.</div>
                            <div class="finding-meta">
                                <span class="severity-badge severity-medium">medium</span>
                                <span class="status-badge status-pass-pass">PASS</span>
                                <span class="info-value">platform</span>
                            </div>
                        </div>
                        <div class="expand-icon">▼</div>
                    </div>
                    <div class="finding-content">
                        <div class="finding-details">
                            <div class="detail-section">
                                <h4>Description</h4>
                                <p>The Kubernetes API Server will prohibit the use of SSL and unauthorized versions of TLS protocols to properly secure communication.

The use of unsupported protocol exposes vulnerabilities to the Kubernetes by rogue traffic interceptions, man-in-the-middle attacks, and impersonation of users or services from the container platform runtime, registry, and keystore. To enable the minimum version of TLS to be used by the Kubernetes API Server, the setting &#34;tls-min-version&#34; must be set.
</p>
                            </div>
                            
                            
                            <div class="detail-section">
                                <h4>Check Text</h4>
                                <pre>Change to the /etc/kubernetes/manifests directory on the Kubernetes Control Plane. Run the command:
grep -i tls-min-version *

If the setting &#34;tls-min-version&#34; is not configured in the Kubernetes API Server manifest file or it is set to &#34;VersionTLS10&#34; or &#34;VersionTLS11&#34;, this is a finding.
</pre>
                            </div>
                            
                            
                            
                            <div class="detail-section">
                                <h4>Fix Text</h4>
                                <pre>Edit the Kubernetes API Server manifest file in the /etc/kubernetes/manifests directory on the Kubernetes Control Plane. Set the value of &#34;--tls-min-version&#34; to &#34;VersionTLS12&#34; or higher.
</pre>
                            </div>
                            
                            
                            
                            <div class="detail-section">
                                <h4>Result Message</h4>
                                <p>Check passed</p>
                            </div>
                            
                            
                            
                        </div>
                    </div>
                </div>
                
                
                <div class="finding-item">
                    <div class="finding-header" onclick="toggleFinding(this)">
                        <div class="finding-title-section">
                            <div class="finding-id">V-242409</div>
                            <div class="finding-title">Kubernetes Controller Manager must disable profiling.</div>
                            <div class="finding-meta">
                                <span class="severity-badge severity-medium">medium</span>
                                <span class="status-badge status-pass-pass">PASS</span>
                                <span class="info-value">platform</span>
                            </div>
                        </div>
                        <div class="expand-icon">▼</div>
                    </div>
                    <div class="finding-content">
                        <div class="finding-details">
                            <div class="detail-section">
                                <h4>Description</h4>
                                <p>Kubernetes profiling provides the ability to analyze and troubleshoot Controller Manager events over a web interface on a host port. Enabling this service can expose details about the Kubernetes architecture. This service must not be enabled unless deemed necessary.
</p>
                            </div>
                            
                            
                            <div class="detail-section">
                                <h4>Check Text</h4>
                                <pre>Change to the /etc/kubernetes/manifests/ directory on the Kubernetes Control Plane. Run the command:
grep -i profiling * 

If the setting &#34;profiling&#34; is not configured in the Kubernetes Controller Manager manifest file or it is set to &#34;True&#34;, this is a finding.
</pre>
                            </div>
                            
                            
                            
                            <div class="detail-section">
                                <h4>Fix Text</h4>
                                <pre>Edit the Kubernetes Controller Manager manifest file in the /etc/kubernetes/manifests directory on the Kubernetes Control Plane. Set the argument &#34;--profiling value&#34; to &#34;false&#34;.
</pre>
                            </div>
                            
                            
                            
                            <div class="detail-section">
                                <h4>Result Message</h4>
                                <p>PASS: Controller Manager profiling is properly configured (disabled or not present)</p>
                            </div>
                            
                            
                            
                        </div>
                    </div>
                </div>
                
                
                <div class="finding-item">
                    <div class="finding-header" onclick="toggleFinding(this)">
                        <div class="finding-title-section">
                            <div class="finding-id">V-242421</div>
                            <div class="finding-title">Kubernetes Controller Manager must have the SSL Certificate Authority set.</div>
                            <div class="finding-meta">
                                <span class="severity-badge severity-medium">medium</span>
                                <span class="status-badge status-pass-pass">PASS</span>
                                <span class="info-value">platform</span>
                            </div>
                        </div>
                        <div class="expand-icon">▼</div>
                    </div>
                    <div class="finding-content">
                        <div class="finding-details">
                            <div class="detail-section">
                                <h4>Description</h4>
                                <p>The Kubernetes Controller Manager is responsible for creating service accounts and tokens for the API Server, maintaining the correct number of pods for every replication controller and provides notifications when nodes are offline.  

Anyone who gains access to the Controller Manager can generate backdoor accounts, take possession of, or diminish system performance without detection by disabling system notification. Using authenticity protection, the communication can be protected against man-in-the-middle attacks/session hijacking and the insertion of false information into sessions.

The communication session is protected by utilizing transport encryption protocols, such as TLS. TLS provides the Kubernetes Controller Manager with a means to be able to authenticate sessions and encrypt traffic.
</p>
                            </div>
                            
                            
                            <div class="detail-section">
                                <h4>Check Text</h4>
                                <pre>Change to the /etc/kubernetes/manifests/ directory on the Kubernetes Control Plane. Run the command:
grep -i root-ca-file *

If the setting &#34;--root-ca-file&#34; is not set in the Kubernetes Controller Manager manifest file or contains no value, this is a finding.
</pre>
                            </div>
                            
                            
                            
                            <div class="detail-section">
                                <h4>Fix Text</h4>
                                <pre>Edit the Kubernetes Controller Manager manifest file in the /etc/kubernetes/manifests directory on the Kubernetes Control Plane. 

Set the value of &#34;--root-ca-file&#34; to path containing Approved Organizational Certificate.
</pre>
                            </div>
                            
                            
                            
                            <div class="detail-section">
                                <h4>Result Message</h4>
                                <p>PASS: Controller Manager root-ca-file is configured: --root-ca-file=/etc/kubernetes/pki/ca.crt (kubectl)</p>
                            </div>
                            
                            
                            
                        </div>
                    </div>
                </div>
                
                
                <div class="finding-item">
                    <div class="finding-header" onclick="toggleFinding(this)">
                        <div class="finding-title-section">
                            <div class="finding-id">V-242385</div>
                            <div class="finding-title">The Kubernetes Controller Manager must have secure binding.</div>
                            <div class="finding-meta">
                                <span class="severity-badge severity-medium">medium</span>
                                <span class="status-badge status-pass-pass">PASS</span>
                                <span class="info-value">platform</span>
                            </div>
                        </div>
                        <div class="expand-icon">▼</div>
                    </div>
                    <div class="finding-content">
                        <div class="finding-details">
                            <div class="detail-section">
                                <h4>Description</h4>
                                <p>Limiting the number of attack vectors and implementing authentication and encryption on the endpoints available to external sources is paramount when securing the overall Kubernetes cluster. The Controller Manager API service exposes port 10252/TCP by default for health and metrics information use. This port does not encrypt or authenticate connections. If this port is exposed externally, an attacker can use this port to attack the entire Kubernetes cluster. By setting the bind address to only localhost (i.e., 127.0.0.1), only those internal services that require health and metrics information can access the Control Manager API.
</p>
                            </div>
                            
                            
                            <div class="detail-section">
                                <h4>Check Text</h4>
                                <pre>Change to the /etc/kubernetes/manifests directory on the Kubernetes Control Plane. Run the command:
grep -i bind-address *

If the setting bind-address is not set to &#34;127.0.0.1&#34; or is not found in the Kubernetes Controller Manager manifest file, this is a finding.
</pre>
                            </div>
                            
                            
                            
                            <div class="detail-section">
                                <h4>Fix Text</h4>
                                <pre>Edit the Kubernetes Controller Manager manifest file in the /etc/kubernetes/manifests directory on the Kubernetes Control Plane. Set the argument &#34;--bind-address&#34; to &#34;127.0.0.1&#34;.
</pre>
                            </div>
                            
                            
                            
                            <div class="detail-section">
                                <h4>Result Message</h4>
                                <p>PASS: Controller Manager is securely bound to [^[:space:]]*&#34;</p>
                            </div>
                            
                            
                            
                        </div>
                    </div>
                </div>
                
                
                <div class="finding-item">
                    <div class="finding-header" onclick="toggleFinding(this)">
                        <div class="finding-title-section">
                            <div class="finding-id">V-242376</div>
                            <div class="finding-title">The Kubernetes Controller Manager must use TLS 1.2, at a minimum, to protect the confidentiality of sensitive data during electronic dissemination.</div>
                            <div class="finding-meta">
                                <span class="severity-badge severity-medium">medium</span>
                                <span class="status-badge status-pass-pass">PASS</span>
                                <span class="info-value">platform</span>
                            </div>
                        </div>
                        <div class="expand-icon">▼</div>
                    </div>
                    <div class="finding-content">
                        <div class="finding-details">
                            <div class="detail-section">
                                <h4>Description</h4>
                                <p>The Kubernetes Controller Manager will prohibit the use of SSL and unauthorized versions of TLS protocols to properly secure communication.

The use of unsupported protocol exposes vulnerabilities to the Kubernetes by rogue traffic interceptions, man-in-the-middle attacks, and impersonation of users or services from the container platform runtime, registry, and key store. To enable the minimum version of TLS to be used by the Kubernetes Controller Manager, the setting &#34;tls-min-version&#34; must be set.
</p>
                            </div>
                            
                            
                            <div class="detail-section">
                                <h4>Check Text</h4>
                                <pre>Change to the /etc/kubernetes/manifests/ directory on the Kubernetes Control Plane. Run the command:
grep -i tls-min-version *

If the setting &#34;tls-min-version&#34; is not configured in the Kubernetes Controller Manager manifest file or it is set to &#34;VersionTLS10&#34; or &#34;VersionTLS11&#34;, this is a finding.
</pre>
                            </div>
                            
                            
                            
                            <div class="detail-section">
                                <h4>Fix Text</h4>
                                <pre>Edit the Kubernetes Controller Manager manifest file in the /etc/kubernetes/manifests directory on the Kubernetes Control Plane. Set the value of &#34;--tls-min-version&#34; to &#34;VersionTLS12&#34; or higher.
</pre>
                            </div>
                            
                            
                            
                            <div class="detail-section">
                                <h4>Result Message</h4>
                                <p>Check passed</p>
                            </div>
                            
                            
                            
                        </div>
                    </div>
                </div>
                
                
                <div class="finding-item">
                    <div class="finding-header" onclick="toggleFinding(this)">
                        <div class="finding-title-section">
                            <div class="finding-id">V-242379</div>
                            <div class="finding-title">The Kubernetes etcd must use TLS to protect the confidentiality of sensitive data during electronic dissemination.</div>
                            <div class="finding-meta">
                                <span class="severity-badge severity-medium">medium</span>
                                <span class="status-badge status-pass-pass">PASS</span>
                                <span class="info-value">platform</span>
                            </div>
                        </div>
                        <div class="expand-icon">▼</div>
                    </div>
                    <div class="finding-content">
                        <div class="finding-details">
                            <div class="detail-section">
                                <h4>Description</h4>
                                <p>Kubernetes etcd will prohibit the use of SSL and unauthorized versions of TLS protocols to properly secure communication.

The use of unsupported protocol exposes vulnerabilities to the Kubernetes by rogue traffic interceptions, man-in-the-middle attacks, and impersonation of users or services from the container platform runtime, registry, and keystore. To enable the minimum version of TLS to be used by the Kubernetes API Server, the setting &#34;--auto-tls&#34; must be set.
</p>
                            </div>
                            
                            
                            <div class="detail-section">
                                <h4>Check Text</h4>
                                <pre>Change to the /etc/kubernetes/manifests directory on the Kubernetes Control Plane. Run the command:

grep -i  auto-tls * 

If the setting &#34;--auto-tls&#34; is not configured in the Kubernetes etcd manifest file or it is set to true, this is a finding.
</pre>
                            </div>
                            
                            
                            
                            <div class="detail-section">
                                <h4>Fix Text</h4>
                                <pre>Edit the Kubernetes etcd manifest file in the /etc/kubernetes/manifests directory on the Kubernetes Control Plane. 

Set the value of &#34;--auto-tls&#34; to &#34;false&#34;.
</pre>
                            </div>
                            
                            
                            
                            <div class="detail-section">
                                <h4>Result Message</h4>
                                <p>PASS: etcd auto-tls is properly configured (disabled or not present)</p>
                            </div>
                            
                            
                            
                        </div>
                    </div>
                </div>
                
                
                <div class="finding-item">
                    <div class="finding-header" onclick="toggleFinding(this)">
                        <div class="finding-title-section">
                            <div class="finding-id">V-242428</div>
                            <div class="finding-title">Kubernetes etcd must have a certificate for communication.</div>
                            <div class="finding-meta">
                                <span class="severity-badge severity-medium">medium</span>
                                <span class="status-badge status-pass-pass">PASS</span>
                                <span class="info-value">platform</span>
                            </div>
                        </div>
                        <div class="expand-icon">▼</div>
                    </div>
                    <div class="finding-content">
                        <div class="finding-details">
                            <div class="detail-section">
                                <h4>Description</h4>
                                <p>Kubernetes stores configuration and state information in a distributed key-value store called etcd. Anyone who can write to etcd can effectively control a Kubernetes cluster. Even just reading the contents of etcd could easily provide helpful hints to a would-be attacker. Using authenticity protection, the communication can be protected against man-in-the-middle attacks/session hijacking and the insertion of false information into sessions.

The communication session is protected by utilizing transport encryption protocols, such as TLS. TLS provides the Kubernetes API Server and etcd with a means to be able to authenticate sessions and encrypt traffic. 

To enable encrypted communication for etcd, the parameter cert-file must be set. This parameter gives the location of the SSL certification file used to secure etcd communication.
</p>
                            </div>
                            
                            
                            <div class="detail-section">
                                <h4>Check Text</h4>
                                <pre>Change to the /etc/kubernetes/manifests directory on the Kubernetes Control Plane. Run the command:
grep -i cert-file * 

If the setting &#34;cert-file&#34; is not configured in the Kubernetes etcd manifest file, this is a finding.
</pre>
                            </div>
                            
                            
                            
                            <div class="detail-section">
                                <h4>Fix Text</h4>
                                <pre>Edit the Kubernetes etcd manifest file in the /etc/kubernetes/manifests directory on the Kubernetes Control Plane. 

Set the value of &#34;--cert-file&#34; to the Approved Organizational Certificate.
</pre>
                            </div>
                            
                            
                            
                            <div class="detail-section">
                                <h4>Result Message</h4>
                                <p>PASS: etcd cert-file is configured: --cert-file=/etc/kubernetes/pki/etcd/server.crt (kubectl)</p>
                            </div>
                            
                            
                            
                        </div>
                    </div>
                </div>
                
                
                <div class="finding-item">
                    <div class="finding-header" onclick="toggleFinding(this)">
                        <div class="finding-title-section">
                            <div class="finding-id">V-242423</div>
                            <div class="finding-title">Kubernetes etcd must enable client authentication to secure service.</div>
                            <div class="finding-meta">
                                <span class="severity-badge severity-medium">medium</span>
                                <span class="status-badge status-pass-pass">PASS</span>
                                <span class="info-value">platform</span>
                            </div>
                        </div>
                        <div class="expand-icon">▼</div>
                    </div>
                    <div class="finding-content">
                        <div class="finding-details">
                            <div class="detail-section">
                                <h4>Description</h4>
                                <p>Kubernetes container and pod configuration are maintained by Kubelet. Kubelet agents register nodes with the API Server, mount volume storage, and perform health checks for containers and pods. Anyone who gains access to Kubelet agents can effectively control applications within the pods and containers. Using authenticity protection, the communication can be protected against man-in-the-middle attacks/session hijacking and the insertion of false information into sessions.

The communication session is protected by utilizing transport encryption protocols, such as TLS. TLS provides the Kubernetes API Server with a means to be able to authenticate sessions and encrypt traffic.

To enable encrypted communication for Kubelet, the parameter client-cert-auth must be set. This parameter gives the location of the SSL Certificate Authority file used to secure Kubelet communication.
</p>
                            </div>
                            
                            
                            <div class="detail-section">
                                <h4>Check Text</h4>
                                <pre>Change to the /etc/kubernetes/manifests directory on the Kubernetes Control Plane. Run the command:

grep -i client-cert-auth * 

If the setting client-cert-auth is not configured in the Kubernetes etcd manifest file or set to &#34;false&#34;, this is a finding.
</pre>
                            </div>
                            
                            
                            
                            <div class="detail-section">
                                <h4>Fix Text</h4>
                                <pre>Edit the Kubernetes etcd manifest file in the /etc/kubernetes/manifests directory on the Kubernetes Control Plane. 

Set the value of &#34;--client-cert-auth&#34; to &#34;true&#34; for the etcd.
</pre>
                            </div>
                            
                            
                            
                            <div class="detail-section">
                                <h4>Result Message</h4>
                                <p>PASS: etcd client-cert-auth is enabled (kubectl)</p>
                            </div>
                            
                            
                            
                        </div>
                    </div>
                </div>
                
                
                <div class="finding-item">
                    <div class="finding-header" onclick="toggleFinding(this)">
                        <div class="finding-title-section">
                            <div class="finding-id">V-242427</div>
                            <div class="finding-title">Kubernetes etcd must have a key file for secure communication.</div>
                            <div class="finding-meta">
                                <span class="severity-badge severity-medium">medium</span>
                                <span class="status-badge status-pass-pass">PASS</span>
                                <span class="info-value">platform</span>
                            </div>
                        </div>
                        <div class="expand-icon">▼</div>
                    </div>
                    <div class="finding-content">
                        <div class="finding-details">
                            <div class="detail-section">
                                <h4>Description</h4>
                                <p>Kubernetes stores configuration and state information in a distributed key-value store called etcd. Anyone who can write to etcd can effectively control the Kubernetes cluster. Even just reading the contents of etcd could easily provide helpful hints to a would-be attacker. Using authenticity protection, the communication can be protected against man-in-the-middle attacks/session hijacking and the insertion of false information into sessions.

The communication session is protected by utilizing transport encryption protocols, such as TLS. TLS provides the Kubernetes API Server and etcd with a means to be able to authenticate sessions and encrypt traffic. 

To enable encrypted communication for etcd, the parameter key-file must be set. This parameter gives the location of the key file used to secure etcd communication.
</p>
                            </div>
                            
                            
                            <div class="detail-section">
                                <h4>Check Text</h4>
                                <pre>Change to the /etc/kubernetes/manifests directory on the Kubernetes Control Plane. 

Run the command:
    grep -i key-file *

If the setting &#34;key-file&#34; is not configured in the etcd manifest  file, this is a finding.
</pre>
                            </div>
                            
                            
                            
                            <div class="detail-section">
                                <h4>Fix Text</h4>
                                <pre>Edit the Kubernetes etcd manifest file in the /etc/kubernetes/manifests directory on the Kubernetes Control Plane. 

Set the value of &#34;--key-file&#34; to the Approved Organizational Certificate.
</pre>
                            </div>
                            
                            
                            
                            <div class="detail-section">
                                <h4>Result Message</h4>
                                <p>PASS: etcd key-file is configured: --key-file=/etc/kubernetes/pki/etcd/server.key (kubectl)</p>
                            </div>
                            
                            
                            
                        </div>
                    </div>
                </div>
                
                
                <div class="finding-item">
                    <div class="finding-header" onclick="toggleFinding(this)">
                        <div class="finding-title-section">
                            <div class="finding-id">V-242380</div>
                            <div class="finding-title">The Kubernetes etcd must use TLS to protect the confidentiality of sensitive data during electronic dissemination.</div>
                            <div class="finding-meta">
                                <span class="severity-badge severity-medium">medium</span>
                                <span class="status-badge status-pass-pass">PASS</span>
                                <span class="info-value">platform</span>
                            </div>
                        </div>
                        <div class="expand-icon">▼</div>
                    </div>
                    <div class="finding-content">
                        <div class="finding-details">
                            <div class="detail-section">
                                <h4>Description</h4>
                                <p>The Kubernetes API Server will prohibit the use of SSL and unauthorized versions of TLS protocols to properly secure communication.

The use of unsupported protocol exposes vulnerabilities to the Kubernetes by rogue traffic interceptions, man-in-the-middle attacks, and impersonation of users or services from the container platform runtime, registry, and keystore. To enable the minimum version of TLS to be used by the Kubernetes API Server, the setting &#34;--peer-auto-tls&#34; must be set.
</p>
                            </div>
                            
                            
                            <div class="detail-section">
                                <h4>Check Text</h4>
                                <pre>Change to the /etc/kubernetes/manifests directory on the Kubernetes Control Plane. Run the command:

grep -I  peer-auto-tls * 

If the setting &#34;--peer-auto-tls&#34; is not configured in the Kubernetes etcd manifest file or it is set to &#34;true&#34;, this is a finding.
</pre>
                            </div>
                            
                            
                            
                            <div class="detail-section">
                                <h4>Fix Text</h4>
                                <pre>Edit the Kubernetes etcd manifest file in the /etc/kubernetes/manifests directory on the Kubernetes Control Plane. 

Set the value of &#34;--peer-auto-tls&#34; to &#34;false&#34;.
</pre>
                            </div>
                            
                            
                            
                            <div class="detail-section">
                                <h4>Result Message</h4>
                                <p>PASS: etcd peer-auto-tls is properly configured (disabled or not present)</p>
                            </div>
                            
                            
                            
                        </div>
                    </div>
                </div>
                
                
                <div class="finding-item">
                    <div class="finding-header" onclick="toggleFinding(this)">
                        <div class="finding-title-section">
                            <div class="finding-id">V-242426</div>
                            <div class="finding-title">Kubernetes etcd must enable client authentication to secure service.</div>
                            <div class="finding-meta">
                                <span class="severity-badge severity-medium">medium</span>
                                <span class="status-badge status-pass-pass">PASS</span>
                                <span class="info-value">platform</span>
                            </div>
                        </div>
                        <div class="expand-icon">▼</div>
                    </div>
                    <div class="finding-content">
                        <div class="finding-details">
                            <div class="detail-section">
                                <h4>Description</h4>
                                <p>Kubernetes container and pod configuration are maintained by Kubelet. Kubelet agents register nodes with the API Server, mount volume storage, and perform health checks for containers and pods. Anyone who gains access to Kubelet agents can effectively control applications within the pods and containers. Using authenticity protection, the communication can be protected against man-in-the-middle attacks/session hijacking and the insertion of false information into sessions.

The communication session is protected by utilizing transport encryption protocols, such as TLS. TLS provides the Kubernetes API Server with a means to be able to authenticate sessions and encrypt traffic.

Etcd is a highly-available key value store used by Kubernetes deployments for persistent storage of all of its REST API objects. These objects are sensitive and should be accessible only by authenticated etcd peers in the etcd cluster. The parameter &#34;--peer-client-cert-auth&#34; must be set for etcd to check all incoming peer requests from the cluster for valid client certificates.
</p>
                            </div>
                            
                            
                            <div class="detail-section">
                                <h4>Check Text</h4>
                                <pre>Change to the /etc/kubernetes/manifests directory on the Kubernetes Control Plane. Run the command:
grep -i peer-client-cert-auth * 

If the setting &#34;--peer-client-cert-auth&#34; is not configured in the Kubernetes etcd manifest file or set to &#34;false&#34;, this is a finding.
</pre>
                            </div>
                            
                            
                            
                            <div class="detail-section">
                                <h4>Fix Text</h4>
                                <pre>Edit the Kubernetes etcd manifest file in the /etc/kubernetes/manifests directory on the Kubernetes Control Plane.

Set the value of &#34;--peer-client-cert-auth&#34; to &#34;true&#34; for the etcd.
</pre>
                            </div>
                            
                            
                            
                            <div class="detail-section">
                                <h4>Result Message</h4>
                                <p>PASS: etcd peer-client-cert-auth is enabled (kubectl)</p>
                            </div>
                            
                            
                            
                        </div>
                    </div>
                </div>
                
                
                <div class="finding-item">
                    <div class="finding-header" onclick="toggleFinding(this)">
                        <div class="finding-title-section">
                            <div class="finding-id">V-242417</div>
                            <div class="finding-title">Kubernetes must separate user functionality.</div>
                            <div class="finding-meta">
                                <span class="severity-badge severity-medium">medium</span>
                                <span class="status-badge status-fail-fail">FAIL</span>
                                <span class="info-value">platform</span>
                            </div>
                        </div>
                        <div class="expand-icon">▼</div>
                    </div>
                    <div class="finding-content">
                        <div class="finding-details">
                            <div class="detail-section">
                                <h4>Description</h4>
                                <p>Separating user functionality from management functionality is required for all components within the Kubernetes Control Plane. Without the separation, users may have access to management functions that can degrade or compromise the operational effectiveness of the system. Kubernetes namespaces are the way to separate user functionality from management functionality.

Network policies provide additional separation by controlling network traffic between pods and namespaces. Properly configured network policies help ensure that user workloads cannot interfere with system components and that different user applications are isolated from each other.
</p>
                            </div>
                            
                            
                            <div class="detail-section">
                                <h4>Check Text</h4>
                                <pre>To check if network policies are configured to separate user functionality, run the command:
kubectl get networkpolicies --all-namespaces

Review the network policies to ensure that:
1. System namespaces (kube-system, kube-public) have appropriate network policies
2. User namespaces have network policies that restrict unnecessary communication
3. Default deny policies are in place where appropriate

If network policies are not configured to properly separate user functionality from management functionality, this is a finding.
</pre>
                            </div>
                            
                            
                            
                            <div class="detail-section">
                                <h4>Fix Text</h4>
                                <pre>Configure network policies to separate user functionality from management functionality:

1. Create default deny network policies for user namespaces
2. Create specific allow policies for required communication
3. Ensure system namespaces have appropriate network restrictions

Example default deny policy:
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: default-deny-all
spec:
  podSelector: {}
  policyTypes:
  - Ingress
  - Egress
</pre>
                            </div>
                            
                            
                            
                            <div class="detail-section">
                                <h4>Result Message</h4>
                                <p>FAIL: No network policies configured in the cluster</p>
                            </div>
                            
                            
                            
                        </div>
                    </div>
                </div>
                
                
                <div class="finding-item">
                    <div class="finding-header" onclick="toggleFinding(this)">
                        <div class="finding-title-section">
                            <div class="finding-id">V-242384</div>
                            <div class="finding-title">The Kubernetes Scheduler must have secure binding.</div>
                            <div class="finding-meta">
                                <span class="severity-badge severity-medium">medium</span>
                                <span class="status-badge status-pass-pass">PASS</span>
                                <span class="info-value">platform</span>
                            </div>
                        </div>
                        <div class="expand-icon">▼</div>
                    </div>
                    <div class="finding-content">
                        <div class="finding-details">
                            <div class="detail-section">
                                <h4>Description</h4>
                                <p>Limiting the number of attack vectors and implementing authentication and encryption on the endpoints available to external sources is paramount when securing the overall Kubernetes cluster. The Scheduler API service exposes port 10251/TCP by default for health and metrics information use. This port does not encrypt or authenticate connections. If this port is exposed externally, an attacker can use this port to attack the entire Kubernetes cluster. By setting the bind address to only localhost (127.0.0.1), the port is not accessible to external sources.
</p>
                            </div>
                            
                            
                            <div class="detail-section">
                                <h4>Check Text</h4>
                                <pre>Change to the /etc/kubernetes/manifests directory on the Kubernetes Control Plane. Run the command:
grep -i bind-address *

Review the \&#34;--bind-address\&#34; setting in the Kubernetes Scheduler manifest file.

If the \&#34;--bind-address\&#34; setting does not exist or is not set to \&#34;127.0.0.1\&#34;, this is a finding.
</pre>
                            </div>
                            
                            
                            
                            <div class="detail-section">
                                <h4>Fix Text</h4>
                                <pre>Edit the Kubernetes Scheduler manifest file in the /etc/kubernetes/manifests directory on the Kubernetes Control Plane.

Set the value of \&#34;--bind-address\&#34; to \&#34;127.0.0.1\&#34;.
</pre>
                            </div>
                            
                            
                            
                            <div class="detail-section">
                                <h4>Result Message</h4>
                                <p>PASS: Scheduler securely bound to specific address: ::&#39;</p>
                            </div>
                            
                            
                            
                        </div>
                    </div>
                </div>
                
                
                <div class="finding-item">
                    <div class="finding-header" onclick="toggleFinding(this)">
                        <div class="finding-title-section">
                            <div class="finding-id">V-242434</div>
                            <div class="finding-title">Kubernetes must contain the latest updates as authorized by IAVMs, CTOs, DTMs, and STIGs.</div>
                            <div class="finding-meta">
                                <span class="severity-badge severity-medium">medium</span>
                                <span class="status-badge status-fail-fail">FAIL</span>
                                <span class="info-value">platform</span>
                            </div>
                        </div>
                        <div class="expand-icon">▼</div>
                    </div>
                    <div class="finding-content">
                        <div class="finding-details">
                            <div class="detail-section">
                                <h4>Description</h4>
                                <p>Kubernetes software must stay up to date with the latest patches, service packs, and hot fixes. Flaws discovered during security assessments, continuous monitoring, incident response activities, or information system error handling must also be addressed expeditiously.

Organization-defined time periods for updating security-relevant software may vary based on a variety of factors including, for example, the security category of the information system or the criticality of the update (i.e., severity of the vulnerability related to the discovered flaw).

This requirement will apply to software patch management solutions that are used to install patches across the enclave and also to applications themselves that are not part of that patch management solution. For example, many browsers today provide the capability to install their own patch software. Patch criticality, as well as system criticality, will vary. Therefore, the tactical situations regarding the patch management process will also vary. This means that the time period used must be a configurable parameter. Time frames for application of security-relevant software updates may be dependent upon the Information Assurance Vulnerability Management (IAVM) process.
</p>
                            </div>
                            
                            
                            <div class="detail-section">
                                <h4>Check Text</h4>
                                <pre>Verify that Kubernetes software is updated to the latest version as authorized by IAVMs, CTOs, DTMs, and STIGs.

Check the current Kubernetes version by running:
kubectl version --short

Compare the version with the latest stable release and any security advisories.

If Kubernetes is not updated to the latest authorized version, this is a finding.
</pre>
                            </div>
                            
                            
                            
                            <div class="detail-section">
                                <h4>Fix Text</h4>
                                <pre>Update Kubernetes to the latest authorized version following organizational change management procedures:

1. Review current version and available updates
2. Check for security advisories and IAVMs
3. Plan and test the update in a non-production environment
4. Apply updates following organizational procedures
5. Verify the update was successful
</pre>
                            </div>
                            
                            
                            
                            <div class="detail-section">
                                <h4>Result Message</h4>
                                <p>FAIL: Unable to retrieve Kubernetes version information</p>
                            </div>
                            
                            
                            
                        </div>
                    </div>
                </div>
                
            
        </div>

        <div class="footer">
            <p>Generated by Compliance Operator on June 26, 2025 at 21:24:32 CST</p>
            <p>Security Technical Implementation Guide (STIG) Compliance Assessment</p>
        </div>
    </div>
</body>
</html>
