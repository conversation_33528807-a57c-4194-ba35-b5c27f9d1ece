#!/bin/bash

# 测试强制扫描功能
# 用于验证 force-scan 注解是否能正确触发扫描

set -e

NAMESPACE=${NAMESPACE:-compliance-system}
SCAN_NAME=${SCAN_NAME:-stig-k8s-v2r2-platform-scan}

echo "=== 测试强制扫描功能 ==="
echo "Namespace: $NAMESPACE"
echo "Scan Name: $SCAN_NAME"

# 检查扫描是否存在
if ! kubectl get scan $SCAN_NAME -n $NAMESPACE &>/dev/null; then
    echo "错误: 扫描 $SCAN_NAME 不存在于命名空间 $NAMESPACE"
    exit 1
fi

echo ""
echo "=== 检查扫描当前状态 ==="
CURRENT_PHASE=$(kubectl get scan $SCAN_NAME -n $NAMESPACE -o jsonpath='{.status.phase}')
CURRENT_RESULT=$(kubectl get scan $SCAN_NAME -n $NAMESPACE -o jsonpath='{.status.result}')
CURRENT_SCAN_ID=$(kubectl get scan $SCAN_NAME -n $NAMESPACE -o jsonpath='{.metadata.annotations.compliance\.alauda\.io/current-scan-id}')

echo "当前状态: Phase=$CURRENT_PHASE, Result=$CURRENT_RESULT"
echo "当前 ScanID: $CURRENT_SCAN_ID"

echo ""
echo "=== 触发强制扫描 ==="
kubectl annotate scan $SCAN_NAME -n $NAMESPACE compliance-operator.alauda.io/force-scan=true --overwrite
echo "已设置强制扫描注解"

echo ""
echo "=== 等待控制器处理 ==="
sleep 3

# 检查注解是否被处理
FORCE_SCAN_ANNOTATION=$(kubectl get scan $SCAN_NAME -n $NAMESPACE -o jsonpath='{.metadata.annotations.compliance\.alauda\.io/force-scan}' 2>/dev/null || echo "")
echo "强制扫描注解状态: $FORCE_SCAN_ANNOTATION"

if [ "$FORCE_SCAN_ANNOTATION" = "false" ]; then
    echo "✅ 强制扫描注解已被控制器处理"
else
    echo "⚠️  强制扫描注解尚未被处理，请检查控制器是否正在运行"
fi

echo ""
echo "=== 监控扫描状态变化 ==="

# 监控扫描状态
MAX_WAIT=60  # 1分钟超时
WAIT_TIME=0

while [ $WAIT_TIME -lt $MAX_WAIT ]; do
    NEW_PHASE=$(kubectl get scan $SCAN_NAME -n $NAMESPACE -o jsonpath='{.status.phase}' 2>/dev/null || echo "Unknown")
    NEW_SCAN_ID=$(kubectl get scan $SCAN_NAME -n $NAMESPACE -o jsonpath='{.metadata.annotations.compliance\.alauda\.io/current-scan-id}' 2>/dev/null || echo "")
    
    echo "扫描状态: Phase=$NEW_PHASE, ScanID=$NEW_SCAN_ID"
    
    # 检查是否生成了新的 ScanID
    if [ "$NEW_SCAN_ID" != "$CURRENT_SCAN_ID" ]; then
        echo "✅ 检测到新的 ScanID，强制扫描已启动"
        echo "   原 ScanID: $CURRENT_SCAN_ID"
        echo "   新 ScanID: $NEW_SCAN_ID"
        break
    fi
    
    # 检查是否状态发生变化
    if [ "$NEW_PHASE" != "$CURRENT_PHASE" ] && [ "$NEW_PHASE" != "Done" ]; then
        echo "✅ 检测到状态变化，强制扫描已启动"
        echo "   原状态: $CURRENT_PHASE"
        echo "   新状态: $NEW_PHASE"
        break
    fi
    
    sleep 5
    WAIT_TIME=$((WAIT_TIME + 5))
done

if [ $WAIT_TIME -ge $MAX_WAIT ]; then
    echo "❌ 超时: 强制扫描未在预期时间内启动"
    echo "   请检查控制器日志和配置"
else
    echo ""
    echo "=== 强制扫描测试完成 ==="
    echo "✅ 强制扫描功能正常工作"
fi

echo ""
echo "=== 当前扫描状态 ==="
kubectl get scan $SCAN_NAME -n $NAMESPACE -o yaml | grep -A 20 "status:" 