#!/bin/bash
# STIG Kubernetes V2R2 Complete Test Script
# This script helps you deploy and test the complete STIG K8s V2R2 compliance suite

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
NAMESPACE="compliance-system"
TIMEOUT="600s"
RULES_DIR="stig-k8s-v2r2-rules"

# Functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

check_prerequisites() {
    log_info "Checking prerequisites..."
    
    if ! command -v kubectl &> /dev/null; then
        log_error "kubectl is not installed or not in PATH"
        exit 1
    fi
    
    if ! kubectl cluster-info &> /dev/null; then
        log_error "Cannot connect to Kubernetes cluster"
        exit 1
    fi
    
    if [ ! -d "$RULES_DIR" ]; then
        log_error "Rules directory $RULES_DIR not found"
        exit 1
    fi
    
    log_success "Prerequisites check passed"
}

create_namespace() {
    log_info "Creating namespace $NAMESPACE..."
    kubectl create namespace $NAMESPACE --dry-run=client -o yaml | kubectl apply -f -
    log_success "Namespace $NAMESPACE ready"
}

deploy_rules() {
    log_info "Deploying STIG K8s V2R2 rules..."
    
    local rule_count=0
    for rule_file in $RULES_DIR/*.yaml; do
        if [ -f "$rule_file" ]; then
            log_info "Applying $(basename $rule_file)"
            kubectl apply -f "$rule_file"
            ((rule_count++))
        fi
    done
    
    log_success "Deployed $rule_count rules"
    
    # Wait for rules to be ready
    log_info "Waiting for rules to be ready..."
    sleep 5
    
    local ready_rules=$(kubectl get rules -n $NAMESPACE --no-headers | wc -l)
    log_success "$ready_rules rules are ready"
}

deploy_profiles() {
    log_info "Deploying STIG K8s V2R2 profiles..."
    kubectl apply -f examples/stig-k8s-v2r2-complete-profile.yaml
    
    # Wait for profiles to be ready
    log_info "Waiting for profiles to be ready..."
    sleep 5
    
    kubectl get profiles -n $NAMESPACE
    log_success "Profiles deployed successfully"
}

deploy_scans() {
    log_info "Deploying STIG K8s V2R2 scans..."
    kubectl apply -f examples/stig-k8s-v2r2-complete-scan.yaml
    
    # Wait for scans to be ready
    log_info "Waiting for scans to be ready..."
    sleep 5
    
    kubectl get scans -n $NAMESPACE
    log_success "Scans deployed successfully"
}

deploy_compliance_suite() {
    log_info "Deploying STIG K8s V2R2 compliance suite..."
    kubectl apply -f examples/stig-k8s-v2r2-compliance-suite.yaml
    
    # Wait for suite to be ready
    log_info "Waiting for compliance suite to be ready..."
    sleep 5
    
    kubectl get compliancesuites -n $NAMESPACE
    log_success "Compliance suite deployed successfully"
}

trigger_manual_scan() {
    local scan_type=$1
    local scan_name="stig-k8s-v2r2-${scan_type}-scan"
    
    log_info "Triggering manual $scan_type scan..."
    
    # Create a manual scan by copying the existing scan with a new name
    kubectl get scan $scan_name -n $NAMESPACE -o yaml | \
        sed "s/name: $scan_name/name: $scan_name-manual-$(date +%s)/" | \
        sed '/schedule:/d' | \
        sed '/resourceVersion:/d' | \
        sed '/uid:/d' | \
        kubectl apply -f -
    
    log_success "Manual $scan_type scan triggered"
}

monitor_scan_progress() {
    local scan_name=$1
    log_info "Monitoring scan progress for $scan_name..."
    
    local timeout=600  # 10 minutes
    local elapsed=0
    local interval=10
    
    while [ $elapsed -lt $timeout ]; do
        local status=$(kubectl get scan $scan_name -n $NAMESPACE -o jsonpath='{.status.phase}' 2>/dev/null || echo "NotFound")
        local result=$(kubectl get scan $scan_name -n $NAMESPACE -o jsonpath='{.status.result}' 2>/dev/null || echo "Unknown")
        
        log_info "Scan $scan_name - Status: $status, Result: $result"
        
        if [[ "$status" == "DONE" ]]; then
            log_success "Scan $scan_name completed with result: $result"
            return 0
        elif [[ "$status" == "ERROR" ]]; then
            log_error "Scan $scan_name failed"
            return 1
        fi
        
        sleep $interval
        elapsed=$((elapsed + interval))
    done
    
    log_warning "Scan $scan_name monitoring timed out after $timeout seconds"
    return 1
}

show_scan_results() {
    log_info "Showing scan results..."
    
    echo ""
    echo "=== PLATFORM SCAN RESULTS ==="
    kubectl get checkresults -n $NAMESPACE -l compliance-operator.alauda.io/scan-name=stig-k8s-v2r2-platform-scan --no-headers 2>/dev/null | \
        awk '{print $1 " - " $2}' | sort || log_warning "No platform scan results found"
    
    echo ""
    echo "=== NODE SCAN RESULTS ==="
    kubectl get checkresults -n $NAMESPACE -l compliance-operator.alauda.io/scan-name=stig-k8s-v2r2-node-scan --no-headers 2>/dev/null | \
        awk '{print $1 " - " $2}' | sort || log_warning "No node scan results found"
    
    echo ""
    echo "=== SCAN SUMMARY ==="
    kubectl get scans -n $NAMESPACE -o custom-columns="NAME:.metadata.name,STATUS:.status.phase,RESULT:.status.result"
}

show_compliance_suite_status() {
    log_info "Showing compliance suite status..."
    kubectl get compliancesuites -n $NAMESPACE -o wide
    
    echo ""
    echo "=== SUITE DETAILS ==="
    kubectl describe compliancesuite stig-k8s-v2r2-suite -n $NAMESPACE
}

cleanup() {
    log_info "Cleaning up test resources..."
    
    # Delete manual scans
    kubectl delete scans -n $NAMESPACE -l compliance-operator.alauda.io/test=manual 2>/dev/null || true
    
    log_success "Cleanup completed"
}

show_usage() {
    echo "Usage: $0 [COMMAND]"
    echo ""
    echo "Commands:"
    echo "  deploy-all       Deploy all STIG K8s V2R2 resources (rules, profiles, scans, suite)"
    echo "  deploy-rules     Deploy only the rules"
    echo "  deploy-profiles  Deploy only the profiles"
    echo "  deploy-scans     Deploy only the scans"
    echo "  deploy-suite     Deploy only the compliance suite"
    echo "  test-platform    Trigger and monitor platform scan"
    echo "  test-node        Trigger and monitor node scan"
    echo "  test-complete    Run complete test (both platform and node)"
    echo "  show-results     Show current scan results"
    echo "  show-suite       Show compliance suite status"
    echo "  cleanup          Clean up test resources"
    echo "  help             Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0 deploy-all    # Deploy everything and set up complete test environment"
    echo "  $0 test-complete # Run complete STIG K8s V2R2 compliance test"
    echo "  $0 show-results  # View current compliance results"
}

# Main execution
case "${1:-help}" in
    "deploy-all")
        check_prerequisites
        create_namespace
        deploy_rules
        deploy_profiles
        deploy_scans
        deploy_compliance_suite
        log_success "All STIG K8s V2R2 resources deployed successfully!"
        ;;
    "deploy-rules")
        check_prerequisites
        create_namespace
        deploy_rules
        ;;
    "deploy-profiles")
        check_prerequisites
        create_namespace
        deploy_profiles
        ;;
    "deploy-scans")
        check_prerequisites
        create_namespace
        deploy_scans
        ;;
    "deploy-suite")
        check_prerequisites
        create_namespace
        deploy_compliance_suite
        ;;
    "test-platform")
        check_prerequisites
        trigger_manual_scan "platform"
        sleep 10
        monitor_scan_progress "stig-k8s-v2r2-platform-scan-manual-$(date +%s)"
        ;;
    "test-node")
        check_prerequisites
        trigger_manual_scan "node"
        sleep 10
        monitor_scan_progress "stig-k8s-v2r2-node-scan-manual-$(date +%s)"
        ;;
    "test-complete")
        check_prerequisites
        log_info "Starting complete STIG K8s V2R2 compliance test..."
        trigger_manual_scan "platform"
        trigger_manual_scan "node"
        sleep 15
        show_scan_results
        show_compliance_suite_status
        log_success "Complete test initiated. Monitor progress with: kubectl get scans -n $NAMESPACE -w"
        ;;
    "show-results")
        show_scan_results
        ;;
    "show-suite")
        show_compliance_suite_status
        ;;
    "cleanup")
        cleanup
        ;;
    "help"|*)
        show_usage
        ;;
esac 