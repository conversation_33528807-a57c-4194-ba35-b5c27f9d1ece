#!/bin/bash

set -e

# Configuration
NAMESPACE="compliance-system"
SERVICE_NAME="openscap-report-service"
REPORTS_DIR="/reports"
LOCAL_DIR="./openscap-reports"

# Color codes
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')] $1${NC}"
}

log_success() {
    echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')] $1${NC}"
}

log_error() {
    echo -e "${RED}[$(date +'%Y-%m-%d %H:%M:%S')] ERROR: $1${NC}"
}

log_warning() {
    echo -e "${YELLOW}[$(date +'%Y-%m-%d %H:%M:%S')] WARNING: $1${NC}"
}

# Show usage
show_usage() {
    echo "Usage: $0 [OPTIONS]"
    echo ""
    echo "Export OpenSCAP reports from report service to local directory"
    echo "Note: Reports are stored in temporary storage and will be lost when pod restarts"
    echo ""
    echo "Options:"
    echo "  -s, --scan-id SCAN_ID      Export specific scan ID only"
    echo "  -o, --output DIR           Output directory (default: ./openscap-reports)"
    echo "  -n, --namespace NS         Kubernetes namespace (default: compliance-system)"
    echo "  -l, --list                 List available scans only"
    echo "  -h, --help                 Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0                         # Export all reports"
    echo "  $0 -l                      # List available scans"
    echo "  $0 -s scan-12345           # Export specific scan"
    echo "  $0 -o /tmp/reports         # Export to specific directory"
}

# Check if pod exists and is ready
check_pod() {
    log "Checking OpenSCAP report service..."
    
    if ! kubectl get deployment -n "$NAMESPACE" "$SERVICE_NAME" >/dev/null 2>&1; then
        log_error "OpenSCAP report service not found in namespace $NAMESPACE"
        log_error "Make sure compliance-operator is running with report service enabled"
        exit 1
    fi
    
    local ready_replicas=$(kubectl get deployment -n "$NAMESPACE" "$SERVICE_NAME" -o jsonpath='{.status.readyReplicas}' 2>/dev/null || echo "0")
    if [[ "$ready_replicas" == "0" ]]; then
        log_error "OpenSCAP report service is not ready"
        log_error "Check pod status: kubectl get pods -n $NAMESPACE -l app=$SERVICE_NAME"
        exit 1
    fi
    
    log_success "OpenSCAP report service is ready"
}

# List available scans
list_scans() {
    log "Listing available scans..."
    
    local pod_name=$(kubectl get pods -n "$NAMESPACE" -l app="$SERVICE_NAME" -o jsonpath='{.items[0].metadata.name}' 2>/dev/null)
    if [[ -z "$pod_name" ]]; then
        log_error "No running pods found for $SERVICE_NAME"
        exit 1
    fi
    
    log "Available scans in $SERVICE_NAME:"
    kubectl exec -n "$NAMESPACE" "$pod_name" -- find "$REPORTS_DIR" -type d -mindepth 1 -maxdepth 1 2>/dev/null | sed "s|$REPORTS_DIR/||" | sort || {
        log_warning "No scans found or unable to access reports directory"
    }
}

# Export specific scan
export_scan() {
    local scan_id="$1"
    local output_dir="$2"
    
    log "Exporting scan: $scan_id"
    
    local pod_name=$(kubectl get pods -n "$NAMESPACE" -l app="$SERVICE_NAME" -o jsonpath='{.items[0].metadata.name}' 2>/dev/null)
    if [[ -z "$pod_name" ]]; then
        log_error "No running pods found for $SERVICE_NAME"
        exit 1
    fi
    
    # Check if scan exists
    if ! kubectl exec -n "$NAMESPACE" "$pod_name" -- test -d "$REPORTS_DIR/$scan_id" 2>/dev/null; then
        log_error "Scan '$scan_id' not found"
        log "Available scans:"
        list_scans
        exit 1
    fi
    
    # Create local directory
    local scan_output_dir="$output_dir/$scan_id"
    mkdir -p "$scan_output_dir"
    
    # Get list of files
    local files=$(kubectl exec -n "$NAMESPACE" "$pod_name" -- find "$REPORTS_DIR/$scan_id" -type f 2>/dev/null)
    if [[ -z "$files" ]]; then
        log_warning "No files found in scan $scan_id"
        return
    fi
    
    # Export each file
    echo "$files" | while read -r file; do
        if [[ -n "$file" ]]; then
            local filename=$(basename "$file")
            log "Exporting: $filename"
            kubectl exec -n "$NAMESPACE" "$pod_name" -- cat "$file" > "$scan_output_dir/$filename"
        fi
    done
    
    log_success "Scan '$scan_id' exported to: $scan_output_dir"
    
    # Show exported files
    log "Exported files:"
    ls -la "$scan_output_dir"
}

# Export all scans
export_all() {
    local output_dir="$1"
    
    log "Exporting all scans..."
    
    local pod_name=$(kubectl get pods -n "$NAMESPACE" -l app="$SERVICE_NAME" -o jsonpath='{.items[0].metadata.name}' 2>/dev/null)
    if [[ -z "$pod_name" ]]; then
        log_error "No running pods found for $SERVICE_NAME"
        exit 1
    fi
    
    # Get list of scans
    local scans=$(kubectl exec -n "$NAMESPACE" "$pod_name" -- find "$REPORTS_DIR" -type d -mindepth 1 -maxdepth 1 2>/dev/null | sed "s|$REPORTS_DIR/||" | sort)
    
    if [[ -z "$scans" ]]; then
        log_warning "No scans found"
        return
    fi
    
    # Export each scan
    echo "$scans" | while read -r scan_id; do
        if [[ -n "$scan_id" ]]; then
            export_scan "$scan_id" "$output_dir"
        fi
    done
    
    log_success "All scans exported to: $output_dir"
}

# Main function
main() {
    local scan_id=""
    local output_dir="$LOCAL_DIR"
    local list_only=false
    
    # Parse arguments
    while [[ $# -gt 0 ]]; do
        case $1 in
            -s|--scan-id)
                scan_id="$2"
                shift 2
                ;;
            -o|--output)
                output_dir="$2"
                shift 2
                ;;
            -n|--namespace)
                NAMESPACE="$2"
                shift 2
                ;;
            -l|--list)
                list_only=true
                shift
                ;;
            -h|--help)
                show_usage
                exit 0
                ;;
            *)
                log_error "Unknown option: $1"
                show_usage
                exit 1
                ;;
        esac
    done
    
    # Check prerequisites
    if ! command -v kubectl >/dev/null 2>&1; then
        log_error "kubectl is required but not installed"
        exit 1
    fi
    
    check_pod
    
    if [[ "$list_only" == true ]]; then
        list_scans
        exit 0
    fi
    
    if [[ -n "$scan_id" ]]; then
        export_scan "$scan_id" "$output_dir"
    else
        export_all "$output_dir"
    fi
    
    log_success "Export completed!"
}

# Execute main function
main "$@" 