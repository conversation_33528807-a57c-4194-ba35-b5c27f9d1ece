#!/bin/bash

set -e

# 默认配置
NAMESPACE=${NAMESPACE:-"compliance-system"}
PORT=${PORT:-"8080"}
SCAN_NAME=""
NODE_NAME=""

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')] $1${NC}"
}

log_success() {
    echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')] SUCCESS: $1${NC}"
}

log_error() {
    echo -e "${RED}[$(date +'%Y-%m-%d %H:%M:%S')] ERROR: $1${NC}"
}

log_warning() {
    echo -e "${YELLOW}[$(date +'%Y-%m-%d %H:%M:%S')] WARNING: $1${NC}"
}

show_usage() {
    echo "Usage: $0 -s <scan-name> [-n <node-name>] [-p <port>] [-ns <namespace>]"
    echo ""
    echo "Options:"
    echo "  -s, --scan       Scan name (required)"
    echo "  -n, --node       Node name (optional, for node-specific reports)"
    echo "  -p, --port       Port for HTTP server (default: 8080)"
    echo "  -ns, --namespace Kubernetes namespace (default: compliance-system)"
    echo "  -h, --help       Show this help"
    echo ""
    echo "Examples:"
    echo "  $0 -s ubuntu-openscap-scan"
    echo "  $0 -s ubuntu-openscap-scan -n 192.168.140.231"
    echo "  $0 -s ubuntu-openscap-scan -p 9090"
}

# 解析命令行参数
while [[ $# -gt 0 ]]; do
    case $1 in
        -s|--scan)
            SCAN_NAME="$2"
            shift 2
            ;;
        -n|--node)
            NODE_NAME="$2"
            shift 2
            ;;
        -p|--port)
            PORT="$2"
            shift 2
            ;;
        -ns|--namespace)
            NAMESPACE="$2"
            shift 2
            ;;
        -h|--help)
            show_usage
            exit 0
            ;;
        *)
            log_error "Unknown option: $1"
            show_usage
            exit 1
            ;;
    esac
done

if [[ -z "$SCAN_NAME" ]]; then
    log_error "Scan name is required"
    show_usage
    exit 1
fi

# 查找OpenSCAP扫描Jobs
log "Looking for OpenSCAP scan jobs for scan: $SCAN_NAME"

JOB_SELECTOR="compliance-operator.alauda.io/scanner=openscap,compliance-operator.alauda.io/scan=$SCAN_NAME"
if [[ -n "$NODE_NAME" ]]; then
    JOB_SELECTOR="$JOB_SELECTOR,compliance-operator.alauda.io/node=$NODE_NAME"
fi

JOBS=$(kubectl get jobs -n "$NAMESPACE" -l "$JOB_SELECTOR" -o jsonpath='{.items[*].metadata.name}')

if [[ -z "$JOBS" ]]; then
    log_error "No OpenSCAP jobs found for scan: $SCAN_NAME"
    exit 1
fi

# 选择第一个Job（如果有多个节点）
JOB_NAME=$(echo $JOBS | awk '{print $1}')
log "Found OpenSCAP job: $JOB_NAME"

# 获取Job对应的Pod
POD_NAME=$(kubectl get pods -n "$NAMESPACE" -l "job-name=$JOB_NAME" -o jsonpath='{.items[0].metadata.name}')

if [[ -z "$POD_NAME" ]]; then
    log_error "No pod found for job: $JOB_NAME"
    exit 1
fi

log "Found pod: $POD_NAME"

# 检查Pod状态
POD_STATUS=$(kubectl get pod "$POD_NAME" -n "$NAMESPACE" -o jsonpath='{.status.phase}')
log "Pod status: $POD_STATUS"

if [[ "$POD_STATUS" != "Succeeded" ]]; then
    log_warning "Pod is not in Succeeded state, but attempting to access reports anyway"
fi

# 创建临时的报告查看Pod
VIEWER_POD_NAME="openscap-report-viewer-$(date +%s)"

log "Creating temporary report viewer pod: $VIEWER_POD_NAME"

cat <<EOF | kubectl apply -f -
apiVersion: v1
kind: Pod
metadata:
  name: $VIEWER_POD_NAME
  namespace: $NAMESPACE
  labels:
    app: openscap-report-viewer
    compliance-operator.alauda.io/scan: $SCAN_NAME
spec:
  restartPolicy: Never
  containers:
  - name: report-server
    image: nginx:alpine
    ports:
    - containerPort: 80
    command: ["/bin/sh"]
    args:
    - -c
    - |
      echo "Starting OpenSCAP Report Viewer..."
      echo "Scan: $SCAN_NAME"
      echo "Pod: $POD_NAME"
      echo ""
      
      # 创建nginx配置
      cat > /etc/nginx/nginx.conf << 'NGINX_EOF'
      events {
          worker_connections 1024;
      }
      http {
          include /etc/nginx/mime.types;
          default_type application/octet-stream;
          
          server {
              listen 80;
              server_name localhost;
              
              location / {
                  root /usr/share/nginx/html;
                  index index.html;
                  try_files \$uri \$uri/ =404;
              }
              
              location /reports/ {
                  alias /reports/;
                  autoindex on;
                  autoindex_exact_size off;
                  autoindex_localtime on;
              }
          }
      }
      NGINX_EOF
      
      # 创建主页
      cat > /usr/share/nginx/html/index.html << 'HTML_EOF'
      <!DOCTYPE html>
      <html>
      <head>
          <title>OpenSCAP Report Viewer</title>
          <style>
              body { font-family: Arial, sans-serif; margin: 40px; }
              .header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 20px; border-radius: 10px; margin-bottom: 20px; }
              .info { background: #f8f9fa; padding: 15px; border-radius: 5px; margin-bottom: 20px; }
              .reports { background: white; border: 1px solid #ddd; border-radius: 5px; padding: 20px; }
              .report-link { display: block; padding: 10px; margin: 5px 0; background: #e3f2fd; border-radius: 5px; text-decoration: none; color: #1976d2; }
              .report-link:hover { background: #bbdefb; }
          </style>
      </head>
      <body>
          <div class="header">
              <h1>🛡️ OpenSCAP Report Viewer</h1>
              <p>Compliance Scan Reports</p>
          </div>
          
          <div class="info">
              <h3>Scan Information</h3>
              <p><strong>Scan Name:</strong> $SCAN_NAME</p>
              <p><strong>Source Pod:</strong> $POD_NAME</p>
              <p><strong>Namespace:</strong> $NAMESPACE</p>
          </div>
          
          <div class="reports">
              <h3>Available Reports</h3>
              <p>Click on the links below to view the OpenSCAP compliance reports:</p>
              <div id="report-links">
                  <p>Loading reports...</p>
              </div>
          </div>
          
          <script>
              // 自动发现报告文件
              fetch('/reports/')
                  .then(response => response.text())
                  .then(html => {
                      const parser = new DOMParser();
                      const doc = parser.parseFromString(html, 'text/html');
                      const links = doc.querySelectorAll('a[href$=".html"]');
                      const container = document.getElementById('report-links');
                      
                      if (links.length > 0) {
                          container.innerHTML = '';
                          links.forEach(link => {
                              const fileName = link.getAttribute('href');
                              const reportLink = document.createElement('a');
                              reportLink.href = '/reports/' + fileName;
                              reportLink.className = 'report-link';
                              reportLink.textContent = '📄 ' + fileName;
                              reportLink.target = '_blank';
                              container.appendChild(reportLink);
                          });
                      } else {
                          container.innerHTML = '<p style="color: #f44336;">No HTML reports found in /reports/ directory</p>';
                      }
                  })
                  .catch(error => {
                      document.getElementById('report-links').innerHTML = '<p style="color: #f44336;">Error loading reports: ' + error.message + '</p>';
                  });
          </script>
      </body>
      </html>
      HTML_EOF
      
      # 启动nginx
      nginx -g 'daemon off;'
    volumeMounts:
    - name: reports
      mountPath: /reports
  volumes:
  - name: reports
    emptyDir: {}
EOF

# 等待Pod启动
log "Waiting for report viewer pod to start..."
kubectl wait --for=condition=Ready pod/$VIEWER_POD_NAME -n "$NAMESPACE" --timeout=60s

# 复制报告文件到查看器Pod
log "Copying report files from scanner pod to viewer pod..."
kubectl cp "$NAMESPACE/$POD_NAME:/reports" "$NAMESPACE/$VIEWER_POD_NAME:/reports" -c report-server || {
    log_warning "Failed to copy reports directory, trying individual files..."
    
    # 尝试复制单个HTML文件
    REPORT_FILES=$(kubectl exec -n "$NAMESPACE" "$POD_NAME" -- find /reports -name "*.html" 2>/dev/null || echo "")
    if [[ -n "$REPORT_FILES" ]]; then
        echo "$REPORT_FILES" | while read -r file; do
            if [[ -n "$file" ]]; then
                kubectl cp "$NAMESPACE/$POD_NAME:$file" "$NAMESPACE/$VIEWER_POD_NAME:$file" -c report-server
                log "Copied: $file"
            fi
        done
    else
        log_error "No HTML report files found in scanner pod"
        exit 1
    fi
}

# 设置端口转发
log "Setting up port forwarding to localhost:$PORT..."
log_success "OpenSCAP Report Viewer is ready!"
log "Access the reports at: http://localhost:$PORT"
log "Press Ctrl+C to stop the viewer and clean up resources"

# 清理函数
cleanup() {
    log "Cleaning up resources..."
    kubectl delete pod "$VIEWER_POD_NAME" -n "$NAMESPACE" --ignore-not-found=true
    kubectl delete service "$VIEWER_POD_NAME-service" -n "$NAMESPACE" --ignore-not-found=true
    log_success "Cleanup completed"
    exit 0
}

# 设置清理信号处理
trap cleanup SIGINT SIGTERM

# 启动端口转发
kubectl port-forward -n "$NAMESPACE" pod/$VIEWER_POD_NAME $PORT:80 