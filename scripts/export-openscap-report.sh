#!/bin/bash

set -e

# 默认配置
NAMESPACE=${NAMESPACE:-"compliance-system"}
SCAN_NAME=""
NODE_NAME=""
OUTPUT_DIR=${OUTPUT_DIR:-"./openscap-reports"}

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')] $1${NC}"
}

log_success() {
    echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')] SUCCESS: $1${NC}"
}

log_error() {
    echo -e "${RED}[$(date +'%Y-%m-%d %H:%M:%S')] ERROR: $1${NC}"
}

log_warning() {
    echo -e "${Y<PERSON>L<PERSON>}[$(date +'%Y-%m-%d %H:%M:%S')] WARNING: $1${NC}"
}

show_usage() {
    echo "Usage: $0 -s <scan-name> [-n <node-name>] [-o <output-dir>] [-ns <namespace>]"
    echo ""
    echo "Options:"
    echo "  -s, --scan       Scan name (required)"
    echo "  -n, --node       Node name (optional, for specific node report)"
    echo "  -o, --output     Output directory (default: ./openscap-reports)"
    echo "  -ns, --namespace Kubernetes namespace (default: compliance-system)"
    echo "  -h, --help       Show this help"
    echo ""
    echo "Examples:"
    echo "  $0 -s ubuntu-openscap-scan"
    echo "  $0 -s ubuntu-openscap-scan -n 192.168.140.231"
    echo "  $0 -s ubuntu-openscap-scan -o /tmp/reports"
}

# 解析命令行参数
while [[ $# -gt 0 ]]; do
    case $1 in
        -s|--scan)
            SCAN_NAME="$2"
            shift 2
            ;;
        -n|--node)
            NODE_NAME="$2"
            shift 2
            ;;
        -o|--output)
            OUTPUT_DIR="$2"
            shift 2
            ;;
        -ns|--namespace)
            NAMESPACE="$2"
            shift 2
            ;;
        -h|--help)
            show_usage
            exit 0
            ;;
        *)
            log_error "Unknown option: $1"
            show_usage
            exit 1
            ;;
    esac
done

if [[ -z "$SCAN_NAME" ]]; then
    log_error "Scan name is required"
    show_usage
    exit 1
fi

# 创建输出目录
mkdir -p "$OUTPUT_DIR"
log "Output directory: $OUTPUT_DIR"

# 查找OpenSCAP扫描Jobs
log "Looking for OpenSCAP scan jobs for scan: $SCAN_NAME"

JOB_SELECTOR="compliance-operator.alauda.io/scanner=openscap,compliance-operator.alauda.io/scan=$SCAN_NAME"
if [[ -n "$NODE_NAME" ]]; then
    JOB_SELECTOR="$JOB_SELECTOR,compliance-operator.alauda.io/node=$NODE_NAME"
fi

JOBS=$(kubectl get jobs -n "$NAMESPACE" -l "$JOB_SELECTOR" -o jsonpath='{.items[*].metadata.name}')

if [[ -z "$JOBS" ]]; then
    log_error "No OpenSCAP jobs found for scan: $SCAN_NAME"
    exit 1
fi

log "Found OpenSCAP jobs: $JOBS"

# 处理每个Job
for JOB_NAME in $JOBS; do
    log "Processing job: $JOB_NAME"
    
    # 获取Job对应的Pod
    POD_NAME=$(kubectl get pods -n "$NAMESPACE" -l "job-name=$JOB_NAME" -o jsonpath='{.items[0].metadata.name}')
    
    if [[ -z "$POD_NAME" ]]; then
        log_warning "No pod found for job: $JOB_NAME, skipping..."
        continue
    fi
    
    log "Found pod: $POD_NAME"
    
    # 检查Pod状态
    POD_STATUS=$(kubectl get pod "$POD_NAME" -n "$NAMESPACE" -o jsonpath='{.status.phase}')
    log "Pod status: $POD_STATUS"
    
    if [[ "$POD_STATUS" != "Succeeded" ]]; then
        log_warning "Pod $POD_NAME is not in Succeeded state, but attempting to export reports anyway"
    fi
    
    # 获取节点信息
    POD_NODE=$(kubectl get pod "$POD_NAME" -n "$NAMESPACE" -o jsonpath='{.spec.nodeName}')
    
    # 列出报告文件
    log "Listing report files in pod $POD_NAME..."
    REPORT_FILES=$(kubectl exec -n "$NAMESPACE" "$POD_NAME" -- find /reports -name "*.html" 2>/dev/null || echo "")
    
    if [[ -z "$REPORT_FILES" ]]; then
        log_warning "No HTML report files found in pod $POD_NAME"
        continue
    fi
    
    # 导出每个报告文件
    echo "$REPORT_FILES" | while read -r file; do
        if [[ -n "$file" ]]; then
            # 生成本地文件名
            FILENAME=$(basename "$file")
            LOCAL_FILE="$OUTPUT_DIR/${SCAN_NAME}-${POD_NODE}-${FILENAME}"
            
            log "Exporting: $file -> $LOCAL_FILE"
            
            # 复制文件
            if kubectl cp "$NAMESPACE/$POD_NAME:$file" "$LOCAL_FILE"; then
                # 获取文件大小
                FILE_SIZE=$(stat -f%z "$LOCAL_FILE" 2>/dev/null || stat -c%s "$LOCAL_FILE" 2>/dev/null || echo "unknown")
                log_success "Exported report: $LOCAL_FILE (${FILE_SIZE} bytes)"
                
                # 创建一个简单的索引文件
                cat > "$OUTPUT_DIR/${SCAN_NAME}-${POD_NODE}-index.html" << EOF
<!DOCTYPE html>
<html>
<head>
    <title>OpenSCAP Report - $SCAN_NAME</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; }
        .header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 20px; border-radius: 10px; margin-bottom: 20px; }
        .info { background: #f8f9fa; padding: 15px; border-radius: 5px; margin-bottom: 20px; }
        .report-link { display: block; padding: 15px; margin: 10px 0; background: #e3f2fd; border-radius: 5px; text-decoration: none; color: #1976d2; font-size: 16px; }
        .report-link:hover { background: #bbdefb; }
    </style>
</head>
<body>
    <div class="header">
        <h1>🛡️ OpenSCAP Compliance Report</h1>
        <p>Security Technical Implementation Guide (STIG) Compliance</p>
    </div>
    
    <div class="info">
        <h3>Scan Information</h3>
        <p><strong>Scan Name:</strong> $SCAN_NAME</p>
        <p><strong>Node:</strong> $POD_NODE</p>
        <p><strong>Pod:</strong> $POD_NAME</p>
        <p><strong>Export Date:</strong> $(date)</p>
        <p><strong>Report Size:</strong> ${FILE_SIZE} bytes</p>
    </div>
    
    <div class="reports">
        <h3>View Report</h3>
        <a href="$FILENAME" class="report-link" target="_blank">📄 Open OpenSCAP HTML Report</a>
        <p><em>Click the link above to view the detailed compliance report.</em></p>
    </div>
</body>
</html>
EOF
                log "Created index file: $OUTPUT_DIR/${SCAN_NAME}-${POD_NODE}-index.html"
            else
                log_error "Failed to export: $file"
            fi
        fi
    done
    
    # 同时导出XML结果文件
    log "Checking for XML results files..."
    XML_FILES=$(kubectl exec -n "$NAMESPACE" "$POD_NAME" -- find /reports -name "*.xml" 2>/dev/null || echo "")
    
    if [[ -n "$XML_FILES" ]]; then
        echo "$XML_FILES" | while read -r file; do
            if [[ -n "$file" ]]; then
                FILENAME=$(basename "$file")
                LOCAL_FILE="$OUTPUT_DIR/${SCAN_NAME}-${POD_NODE}-${FILENAME}"
                
                log "Exporting XML: $file -> $LOCAL_FILE"
                if kubectl cp "$NAMESPACE/$POD_NAME:$file" "$LOCAL_FILE"; then
                    FILE_SIZE=$(stat -f%z "$LOCAL_FILE" 2>/dev/null || stat -c%s "$LOCAL_FILE" 2>/dev/null || echo "unknown")
                    log_success "Exported XML results: $LOCAL_FILE (${FILE_SIZE} bytes)"
                fi
            fi
        done
    fi
done

# 创建总索引文件
log "Creating master index file..."
cat > "$OUTPUT_DIR/index.html" << EOF
<!DOCTYPE html>
<html>
<head>
    <title>OpenSCAP Reports - $SCAN_NAME</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; }
        .header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 20px; border-radius: 10px; margin-bottom: 20px; }
        .info { background: #f8f9fa; padding: 15px; border-radius: 5px; margin-bottom: 20px; }
        .file-list { background: white; border: 1px solid #ddd; border-radius: 5px; padding: 20px; }
        .file-item { padding: 10px; margin: 5px 0; background: #f5f5f5; border-radius: 3px; }
        .file-link { color: #1976d2; text-decoration: none; font-weight: bold; }
        .file-link:hover { text-decoration: underline; }
    </style>
</head>
<body>
    <div class="header">
        <h1>🛡️ OpenSCAP Compliance Reports</h1>
        <p>Exported from Kubernetes Compliance Operator</p>
    </div>
    
    <div class="info">
        <h3>Export Information</h3>
        <p><strong>Scan Name:</strong> $SCAN_NAME</p>
        <p><strong>Namespace:</strong> $NAMESPACE</p>
        <p><strong>Export Date:</strong> $(date)</p>
        <p><strong>Output Directory:</strong> $OUTPUT_DIR</p>
    </div>
    
    <div class="file-list">
        <h3>Available Reports</h3>
        <div id="file-list">
            <p>Loading files...</p>
        </div>
    </div>
    
    <script>
        // 自动列出目录中的文件
        const fileList = document.getElementById('file-list');
        fileList.innerHTML = '';
        
        // 这里需要手动添加文件链接，因为浏览器安全限制无法直接读取本地目录
        const files = [
EOF

# 添加文件链接到索引
for file in "$OUTPUT_DIR"/*.html; do
    if [[ -f "$file" && "$(basename "$file")" != "index.html" ]]; then
        FILENAME=$(basename "$file")
        cat >> "$OUTPUT_DIR/index.html" << EOF
            '<div class="file-item"><a href="$FILENAME" class="file-link">📄 $FILENAME</a></div>',
EOF
    fi
done

cat >> "$OUTPUT_DIR/index.html" << EOF
        ];
        
        files.forEach(fileHtml => {
            if (fileHtml.trim()) {
                fileList.innerHTML += fileHtml;
            }
        });
        
        if (fileList.innerHTML === '') {
            fileList.innerHTML = '<p>No report files found.</p>';
        }
    </script>
</body>
</html>
EOF

log_success "Export completed!"
log "Reports exported to: $OUTPUT_DIR"
log "Open the master index: $OUTPUT_DIR/index.html"

# 统计信息
HTML_COUNT=$(find "$OUTPUT_DIR" -name "*.html" -not -name "index.html" | wc -l)
XML_COUNT=$(find "$OUTPUT_DIR" -name "*.xml" | wc -l)

log "Summary:"
log "  - HTML Reports: $HTML_COUNT"
log "  - XML Results: $XML_COUNT"
log "  - Total Files: $((HTML_COUNT + XML_COUNT + 1))" 