#!/bin/bash

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Configuration
IMAGE_NAME="build-harbor.alauda.cn/test/compliance-acp/openscap-scanner:latest"
DOCKERFILE_PATH="images/openscap-scanner/Dockerfile"
BUILD_CONTEXT="images/openscap-scanner/"

echo -e "${YELLOW}Building OpenSCAP Scanner Docker Image...${NC}"
echo "Image: ${IMAGE_NAME}"
echo "Dockerfile: ${DOCKERFILE_PATH}"
echo "Build Context: ${BUILD_CONTEXT}"
echo

# Check if Dockerfile exists
if [ ! -f "$DOCKERFILE_PATH" ]; then
    echo -e "${RED}Error: Dockerfile not found at $DOCKERFILE_PATH${NC}"
    exit 1
fi

# Check if scanner script exists
if [ ! -f "images/openscap-scanner/scripts/openscap-scanner.sh" ]; then
    echo -e "${RED}Error: Scanner script not found at images/openscap-scanner/scripts/openscap-scanner.sh${NC}"
    exit 1
fi

# Build the Docker image
echo -e "${YELLOW}Step 1: Building Docker image...${NC}"
if docker build -f ${DOCKERFILE_PATH} -t ${IMAGE_NAME} ${BUILD_CONTEXT}; then
    echo -e "${GREEN}✓ Docker image built successfully${NC}"
else
    echo -e "${RED}✗ Failed to build Docker image${NC}"
    exit 1
fi

# Push the Docker image
echo -e "${YELLOW}Step 2: Pushing Docker image to registry...${NC}"
if docker push ${IMAGE_NAME}; then
    echo -e "${GREEN}✓ Docker image pushed successfully${NC}"
else
    echo -e "${RED}✗ Failed to push Docker image${NC}"
    exit 1
fi

echo
echo -e "${GREEN}🎉 OpenSCAP Scanner image build and push completed successfully!${NC}"
echo "Image: ${IMAGE_NAME}"
echo
echo "You can now use this image for OpenSCAP scanning:"
echo "  docker run --privileged -v /:/host \\"
echo "    -e HOSTROOT=/host \\"
echo "    -e PROFILE=xccdf_org.ssgproject.content_profile_stig \\"
echo "    -e CONTENT=ssg-ubuntu2204-ds.xml \\"
echo "    -e CONTENT_IMAGE=registry.alauda.cn:60070/test/compliance/os-content:latest \\"
echo "    -e REPORT_DIR=/reports \\"
echo "    -v \$(pwd)/reports:/reports \\"
echo "    ${IMAGE_NAME}"
echo
echo "To test the image locally (without host scanning):"
echo "  docker run --rm ${IMAGE_NAME}" 