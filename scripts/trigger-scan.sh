#!/bin/bash

# 手动触发Scan执行的便捷脚本
# 使用方法: ./trigger-scan.sh <scan-name> [namespace]

set -e

SCAN_NAME="$1"
NAMESPACE="${2:-compliance-system}"

if [ -z "$SCAN_NAME" ]; then
    echo "使用方法: $0 <scan-name> [namespace]"
    echo "示例: $0 profile-scan compliance-system"
    exit 1
fi

echo "🚀 触发扫描执行..."
echo "   扫描名称: $SCAN_NAME"
echo "   命名空间: $NAMESPACE"

# 检查Scan是否存在
if ! kubectl get scan "$SCAN_NAME" -n "$NAMESPACE" >/dev/null 2>&1; then
    echo "❌ 错误: Scan '$SCAN_NAME' 在命名空间 '$NAMESPACE' 中不存在"
    exit 1
fi

# 获取当前扫描状态
CURRENT_PHASE=$(kubectl get scan "$SCAN_NAME" -n "$NAMESPACE" -o jsonpath='{.status.phase}')
echo "   当前状态: $CURRENT_PHASE"

# 方法1：使用强制扫描注解（推荐）
echo "📝 添加强制扫描注解..."
kubectl annotate scan "$SCAN_NAME" -n "$NAMESPACE" compliance-operator.alauda.io/force-scan=true --overwrite

echo "✅ 扫描触发成功！"
echo ""
echo "📊 监控扫描进度:"
echo "   kubectl get scan $SCAN_NAME -n $NAMESPACE -w"
echo ""
echo "📋 查看扫描日志:"
echo "   kubectl logs -l compliance-operator.alauda.io/scan=$SCAN_NAME -n $NAMESPACE -f"
echo ""
echo "📈 查看扫描结果:"
echo "   kubectl get checkresult -l compliance-operator.alauda.io/scan=$SCAN_NAME -n $NAMESPACE"

# 可选：等待扫描开始
if [ "${3:-}" = "--wait" ]; then
    echo ""
    echo "⏳ 等待扫描开始..."
    
    # 等待状态变化
    timeout=60
    elapsed=0
    while [ $elapsed -lt $timeout ]; do
        NEW_PHASE=$(kubectl get scan "$SCAN_NAME" -n "$NAMESPACE" -o jsonpath='{.status.phase}' 2>/dev/null || echo "")
        if [ "$NEW_PHASE" != "$CURRENT_PHASE" ] && [ "$NEW_PHASE" != "" ]; then
            echo "✅ 扫描状态已更新: $CURRENT_PHASE -> $NEW_PHASE"
            break
        fi
        sleep 2
        elapsed=$((elapsed + 2))
    done
    
    if [ $elapsed -ge $timeout ]; then
        echo "⚠️ 等待超时，但扫描可能仍在后台处理中"
    fi
fi 