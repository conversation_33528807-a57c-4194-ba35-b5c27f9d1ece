#!/bin/bash

set -e

# Color codes for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
IMAGE_REGISTRY=${IMAGE_REGISTRY:-"build-harbor.alauda.cn/test/compliance"}
IMAGE_NAME="openscap-report-service"
IMAGE_TAG=${IMAGE_TAG:-"latest"}
DOCKERFILE_PATH="images/openscap-report-service/Dockerfile"

# Logging functions
log() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')] $1${NC}"
}

log_error() {
    echo -e "${RED}[$(date +'%Y-%m-%d %H:%M:%S')] ERROR: $1${NC}"
}

log_success() {
    echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')] SUCCESS: $1${NC}"
}

log_warning() {
    echo -e "${YELLOW}[$(date +'%Y-%m-%d %H:%M:%S')] WARNING: $1${NC}"
}

# Main function
main() {
    log "Building OpenSCAP Report Service Docker image..."
    
    # Check if Dockerfile exists
    if [[ ! -f "$DOCKERFILE_PATH" ]]; then
        log_error "Dockerfile not found: $DOCKERFILE_PATH"
        exit 1
    fi
    
    # Check if source code exists
    if [[ ! -f "cmd/openscap-report-service/main.go" ]]; then
        log_error "Source code not found: cmd/openscap-report-service/main.go"
        exit 1
    fi
    
    # Build image
    local full_image_name="${IMAGE_REGISTRY}/${IMAGE_NAME}:${IMAGE_TAG}"
    
    log "Building image: $full_image_name"
    log "Using Dockerfile: $DOCKERFILE_PATH"
    
    # Build the Docker image
    if docker build -t "$full_image_name" -f "$DOCKERFILE_PATH" .; then
        log_success "Image built successfully: $full_image_name"
    else
        log_error "Failed to build image"
        exit 1
    fi
    
    # Push image to registry
    log "Pushing image to registry..."
    if docker push "$full_image_name"; then
        log_success "Image pushed successfully: $full_image_name"
    else
        log_error "Failed to push image"
        exit 1
    fi
    
    # Show image info
    log "Image information:"
    docker images | grep "$IMAGE_NAME" | head -5
    
    log_success "OpenSCAP Report Service image build completed!"
    log "Image: $full_image_name"
    log "To deploy: helm upgrade compliance-operator ./charts/compliance-operator --set reportService.enabled=true"
}

# Show usage
show_usage() {
    echo "Usage: $0 [OPTIONS]"
    echo ""
    echo "Options:"
    echo "  -r, --registry REGISTRY    Set image registry (default: build-harbor.alauda.cn/test/compliance)"
    echo "  -t, --tag TAG              Set image tag (default: latest)"
    echo "  -h, --help                 Show this help message"
    echo ""
    echo "Environment variables:"
    echo "  IMAGE_REGISTRY             Image registry"
    echo "  IMAGE_TAG                  Image tag"
    echo ""
    echo "Examples:"
    echo "  $0                         # Build with default settings"
    echo "  $0 -t v1.0.0               # Build with specific tag"
    echo "  $0 -r my-registry.com -t latest  # Build with custom registry and tag"
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        -r|--registry)
            IMAGE_REGISTRY="$2"
            shift 2
            ;;
        -t|--tag)
            IMAGE_TAG="$2"
            shift 2
            ;;
        -h|--help)
            show_usage
            exit 0
            ;;
        *)
            log_error "Unknown option: $1"
            show_usage
            exit 1
            ;;
    esac
done

# Execute main function
main 