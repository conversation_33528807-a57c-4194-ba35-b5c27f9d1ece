#!/bin/bash

# Test script for new report generation functionality
set -e

# Configuration
RULE_NAME="${1:-stig-k8s-api-server-alpha-apis-disabled}"
NODE_INFO="${2:-**************}"
NAMESPACE="compliance-system"
SKIP_CLEANUP="${3:-false}"  # Add option to skip cleanup

echo "=== Testing New Report Generation ==="
echo "Rule: $RULE_NAME"
echo "Node: $NODE_INFO"
echo "Namespace: $NAMESPACE"
echo "Skip cleanup: $SKIP_CLEANUP"
echo

# Generate unique names with timestamp
TIMESTAMP=$(date +%Y%m%d-%H%M%S)
RANDOM_SUFFIX=$(openssl rand -hex 3 | tr 'A-F' 'a-f')
PROFILE_NAME="test-profile-${TIMESTAMP}-${RANDOM_SUFFIX}"
SCAN_NAME="test-scan-${TIMESTAMP}-${RANDOM_SUFFIX}"

echo "Generated names:"
echo "  Profile: $PROFILE_NAME"
echo "  Scan: $SCAN_NAME"
echo

# Function to cleanup resources
cleanup() {
    if [ "$SKIP_CLEANUP" = "true" ]; then
        echo "🔍 Skipping cleanup - resources preserved for inspection:"
        echo "  Profile: $PROFILE_NAME"
        echo "  Scan: $SCAN_NAME"
        echo "  Report ConfigMap: $(kubectl get scan "$SCAN_NAME" -n "$NAMESPACE" -o jsonpath='{.status.latestResult.reportName}' 2>/dev/null || echo 'Not found')"
        return
    fi
    echo "🧹 Cleaning up test resources..."
    kubectl delete scan "$SCAN_NAME" -n "$NAMESPACE" --ignore-not-found=true
    kubectl delete profile "$PROFILE_NAME" -n "$NAMESPACE" --ignore-not-found=true
    echo "✅ Cleanup completed"
}

# Set trap for cleanup on exit
trap cleanup EXIT

# Function to check if rule exists
check_rule_exists() {
    local rule_name=$1
    kubectl get rule "$rule_name" -n "$NAMESPACE" >/dev/null 2>&1
}

# Function to get rule checkType
get_rule_checktype() {
    local rule_name=$1
    kubectl get rule "$rule_name" -n "$NAMESPACE" -o jsonpath='{.spec.checkType}' 2>/dev/null || echo "platform"
}

# Check if rule exists
if ! check_rule_exists "$RULE_NAME"; then
    echo "❌ Rule '$RULE_NAME' not found in namespace '$NAMESPACE'"
    echo "Available rules:"
    kubectl get rules -n "$NAMESPACE" --no-headers | head -10
    exit 1
fi

# Get rule checkType
CHECK_TYPE=$(get_rule_checktype "$RULE_NAME")
echo "📋 Rule check type: $CHECK_TYPE"

# Create temporary Profile
echo "🔧 Creating temporary Profile..."
cat <<EOF | kubectl apply -f -
apiVersion: compliance-operator.alauda.io/v1alpha1
kind: Profile
metadata:
  name: $PROFILE_NAME
  namespace: $NAMESPACE
spec:
  title: "Test Profile for $RULE_NAME"
  description: "Temporary profile for testing report generation"
  rules:
  - name: $RULE_NAME
EOF

if [ $? -eq 0 ]; then
    echo "✅ Profile created successfully"
else
    echo "❌ Failed to create Profile"
    exit 1
fi

# Create Scan based on checkType
echo "🚀 Creating Scan..."
if [ "$CHECK_TYPE" = "node" ]; then
    # Node scan
    cat <<EOF | kubectl apply -f -
apiVersion: compliance-operator.alauda.io/v1alpha1
kind: Scan
metadata:
  name: $SCAN_NAME
  namespace: $NAMESPACE
spec:
  profile: $PROFILE_NAME
  scanType: node
  nodeSelector:
    kubernetes.io/hostname: $NODE_INFO
EOF
else
    # Platform scan
    cat <<EOF | kubectl apply -f -
apiVersion: compliance-operator.alauda.io/v1alpha1
kind: Scan
metadata:
  name: $SCAN_NAME
  namespace: $NAMESPACE
spec:
  profile: $PROFILE_NAME
  scanType: platform
EOF
fi

if [ $? -eq 0 ]; then
    echo "✅ Scan created successfully"
else
    echo "❌ Failed to create Scan"
    exit 1
fi

# Function to check scan status
check_scan_status() {
    local scan_name=$1
    kubectl get scan "$scan_name" -n "$NAMESPACE" -o jsonpath='{.status.phase}' 2>/dev/null || echo "NotFound"
}

# Function to wait for scan completion
wait_for_scan_completion() {
    local scan_name=$1
    local max_wait=300
    local wait_time=0
    
    echo "⏳ Waiting for scan completion..."
    while [ $wait_time -lt $max_wait ]; do
        local status=$(check_scan_status "$scan_name")
        echo "Current status: $status"
        
        case "$status" in
            "Done"|"Compliant"|"NonCompliant")
                echo "✅ Scan completed with status: $status"
                return 0
                ;;
            "Error"|"Failed")
                echo "❌ Scan failed with status: $status"
                kubectl describe scan "$scan_name" -n "$NAMESPACE"
                return 1
                ;;
            "NotFound")
                echo "❌ Scan not found"
                return 1
                ;;
        esac
        
        sleep 10
        wait_time=$((wait_time + 10))
    done
    
    echo "❌ Timeout waiting for scan completion"
    return 1
}

# Wait for scan completion
if wait_for_scan_completion "$SCAN_NAME"; then
    echo "🎉 Scan completed successfully!"
    
    # Show scan results
    echo "📊 Scan Results:"
    kubectl get scan "$SCAN_NAME" -n "$NAMESPACE" -o yaml
    
    # Check for CheckResults
    echo "📋 CheckResults:"
    kubectl get checkresults -n "$NAMESPACE" -l compliance-operator.alauda.io/scan-name="$SCAN_NAME"
    
    # Test report generation
    echo "📄 Testing report generation..."
    
    # Get scan UID for report generation
    SCAN_UID=$(kubectl get scan "$SCAN_NAME" -n "$NAMESPACE" -o jsonpath='{.metadata.uid}')
    
    if [ -n "$SCAN_UID" ]; then
        echo "✅ Scan UID: $SCAN_UID"
        echo "📝 Report should be generated automatically by the controller"
        echo "🔍 Check for ConfigMap with report data:"
        kubectl get configmaps -n "$NAMESPACE" -l compliance-operator.alauda.io/scan-name="$SCAN_NAME"
    else
        echo "❌ Could not get scan UID"
    fi
    
else
    echo "❌ Scan failed!"
    kubectl describe scan "$SCAN_NAME" -n "$NAMESPACE"
    exit 1
fi

echo "✅ Report generation test completed successfully!" 