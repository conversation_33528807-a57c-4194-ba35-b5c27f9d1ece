#!/bin/bash

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Configuration
IMAGE_NAME="build-harbor.alauda.cn/test/compliance/os-content:latest"
DOCKERFILE_PATH="images/content/Dockerfile"
BUILD_CONTEXT="images/content/"

echo -e "${YELLOW}Building OS Content Docker Image...${NC}"
echo "Image: ${IMAGE_NAME}"
echo "Dockerfile: ${DOCKERFILE_PATH}"
echo "Build Context: ${BUILD_CONTEXT}"
echo

# Check if XML files exist
if [ ! -f "images/content/ds/ssg-slmicro5-ds.xml" ] || [ ! -f "images/content/ds/ssg-ubuntu2204-ds.xml" ]; then
    echo -e "${RED}Error: XML datastream files not found in images/content/ds/${NC}"
    echo "Expected files:"
    echo "  - images/content/ds/ssg-slmicro5-ds.xml"
    echo "  - images/content/ds/ssg-ubuntu2204-ds.xml"
    exit 1
fi

# Build the Docker image
echo -e "${YELLOW}Step 1: Building Docker image...${NC}"
if docker build -f ${DOCKERFILE_PATH} -t ${IMAGE_NAME} ${BUILD_CONTEXT}; then
    echo -e "${GREEN}✓ Docker image built successfully${NC}"
else
    echo -e "${RED}✗ Failed to build Docker image${NC}"
    exit 1
fi

# Push the Docker image
echo -e "${YELLOW}Step 2: Pushing Docker image to registry...${NC}"
if docker push ${IMAGE_NAME}; then
    echo -e "${GREEN}✓ Docker image pushed successfully${NC}"
else
    echo -e "${RED}✗ Failed to push Docker image${NC}"
    exit 1
fi

echo
echo -e "${GREEN}🎉 OS Content image build and push completed successfully!${NC}"
echo "Image: ${IMAGE_NAME}"
echo
echo "You can now use this image in your ProfileBundle:"
echo "  contentImage: ${IMAGE_NAME}"
echo
echo "To test the image locally:"
echo "  docker run --rm ${IMAGE_NAME}"
echo
echo "To extract files from the image:"
echo "  docker run --rm -v \$(pwd)/extracted:/tmp ${IMAGE_NAME} sh -c 'cp /content/*.xml /tmp/'" 