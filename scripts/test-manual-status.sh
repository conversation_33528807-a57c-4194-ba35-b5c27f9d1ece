#!/bin/bash

# 测试退出码 2 (MANUAL 状态) 的处理脚本
# 用于验证 scan controller 是否正确处理 MANUAL 状态

set -e

NAMESPACE=${NAMESPACE:-default}
SCAN_NAME="test-manual-status-$(date +%s)"

echo "=== 测试退出码 2 (MANUAL 状态) 处理 ==="
echo "Namespace: $NAMESPACE"
echo "Scan Name: $SCAN_NAME"

# 检查是否有修改过的规则文件（返回退出码 2 的规则）
MANUAL_RULES=(
    "stig-k8s-admin-conf-file-ownership"
    "stig-k8s-pki-key-file-permissions"
    "stig-k8s-kubelet-config-file-ownership"
    "stig-k8s-api-server-secure-port-set"
)

echo ""
echo "可以测试的 MANUAL 状态规则："
for rule in "${MANUAL_RULES[@]}"; do
    echo "  - $rule"
done

echo ""
read -p "请选择要测试的规则名称 (不包含 .yaml 后缀): " RULE_NAME

if [[ ! " ${MANUAL_RULES[@]} " =~ " ${RULE_NAME} " ]]; then
    echo "错误: 规则 '$RULE_NAME' 不在支持的 MANUAL 状态规则列表中"
    exit 1
fi

echo ""
echo "=== 创建测试 Profile ==="

# 创建临时 Profile
cat <<EOF | kubectl apply -f -
apiVersion: compliance-operator.alauda.io/v1alpha1
kind: Profile
metadata:
  name: ${SCAN_NAME}-profile
  namespace: $NAMESPACE
spec:
  title: "Test Manual Status Profile"
  description: "Profile for testing MANUAL status handling"
  rules:
    - name: $RULE_NAME
EOF

echo "Profile ${SCAN_NAME}-profile 已创建"

echo ""
echo "=== 创建测试 Scan ==="

# 创建 Scan
cat <<EOF | kubectl apply -f -
apiVersion: compliance-operator.alauda.io/v1alpha1
kind: Scan
metadata:
  name: $SCAN_NAME
  namespace: $NAMESPACE
spec:
  profile: ${SCAN_NAME}-profile
  scanType: node
  nodeSelector:
    kubernetes.io/os: linux
EOF

echo "Scan $SCAN_NAME 已创建"

echo ""
echo "=== 监控扫描进度 ==="

# 等待扫描开始
echo "等待扫描开始..."
sleep 10

# 监控扫描状态
MAX_WAIT=300  # 5分钟超时
WAIT_TIME=0

while [ $WAIT_TIME -lt $MAX_WAIT ]; do
    PHASE=$(kubectl get scan $SCAN_NAME -n $NAMESPACE -o jsonpath='{.status.phase}' 2>/dev/null || echo "Unknown")
    RESULT=$(kubectl get scan $SCAN_NAME -n $NAMESPACE -o jsonpath='{.status.result}' 2>/dev/null || echo "Unknown")
    
    echo "扫描状态: Phase=$PHASE, Result=$RESULT"
    
    if [ "$PHASE" = "Done" ]; then
        echo ""
        echo "=== 扫描完成 ==="
        break
    elif [ "$PHASE" = "Error" ]; then
        echo ""
        echo "=== 扫描出错 ==="
        kubectl get scan $SCAN_NAME -n $NAMESPACE -o yaml
        break
    fi
    
    sleep 10
    WAIT_TIME=$((WAIT_TIME + 10))
done

if [ $WAIT_TIME -ge $MAX_WAIT ]; then
    echo "警告: 扫描超时"
fi

echo ""
echo "=== 检查扫描结果 ==="

# 获取扫描结果
kubectl get scan $SCAN_NAME -n $NAMESPACE -o yaml

echo ""
echo "=== 检查 CheckResult ==="

# 查找 CheckResult
CHECKRESULT=$(kubectl get checkresults -n $NAMESPACE -l compliance-operator.alauda.io/scan=$SCAN_NAME -o name | head -1)
if [ -n "$CHECKRESULT" ]; then
    echo "找到 CheckResult: $CHECKRESULT"
    kubectl get $CHECKRESULT -n $NAMESPACE -o yaml
else
    echo "未找到 CheckResult"
fi

echo ""
echo "=== 验证 MANUAL 状态 ==="

# 检查状态是否正确
FINAL_RESULT=$(kubectl get scan $SCAN_NAME -n $NAMESPACE -o jsonpath='{.status.result}' 2>/dev/null || echo "Unknown")
echo "最终结果: $FINAL_RESULT"

if [ "$FINAL_RESULT" = "MANUAL" ]; then
    echo "✅ 测试成功: 扫描结果正确识别为 MANUAL 状态"
else
    echo "❌ 测试失败: 期望结果为 MANUAL，实际结果为 $FINAL_RESULT"
fi

echo ""
echo "=== 清理资源 ==="

read -p "是否清理测试资源? (y/N): " CLEANUP
if [[ "$CLEANUP" =~ ^[Yy]$ ]]; then
    kubectl delete scan $SCAN_NAME -n $NAMESPACE --ignore-not-found=true
    kubectl delete profile ${SCAN_NAME}-profile -n $NAMESPACE --ignore-not-found=true
    
    # 清理 CheckResult
    if [ -n "$CHECKRESULT" ]; then
        kubectl delete $CHECKRESULT -n $NAMESPACE --ignore-not-found=true
    fi
    
    echo "测试资源已清理"
else
    echo "测试资源保留，请手动清理:"
    echo "  kubectl delete scan $SCAN_NAME -n $NAMESPACE"
    echo "  kubectl delete profile ${SCAN_NAME}-profile -n $NAMESPACE"
    if [ -n "$CHECKRESULT" ]; then
        echo "  kubectl delete $CHECKRESULT -n $NAMESPACE"
    fi
fi

echo ""
echo "=== 测试完成 ===" 