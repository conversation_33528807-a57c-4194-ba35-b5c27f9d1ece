# OS Content Image

This directory contains the Docker image configuration for packaging SCAP (Security Content Automation Protocol) datastream files.

## Contents

- `ssg-slmicro5-ds.xml` - SUSE Linux Micro 5 security datastream
- `ssg-ubuntu2204-ds.xml` - Ubuntu 22.04 LTS security datastream

## Usage

### Build and Push Image

Using the convenience script:
```bash
./scripts/build-os-content.sh
```

Using Make commands:
```bash
# Build the image
make docker-build-os-content

# Push the image
make docker-push-os-content

# Build and push together
make docker-build-os-content docker-push-os-content
```

### Use in ProfileBundle

```yaml
apiVersion: compliance-operator.alauda.io/v1alpha1
kind: ProfileBundle
metadata:
  name: os_ubuntu_content
  namespace: compliance-system
spec:
  contentFile: ssg-ubuntu2204-ds.xml
  contentImage: build-harbor.alauda.cn/test/compliance/os-content:latest
```

### Extract Files from Image

```bash
# Create extraction directory
mkdir -p extracted

# Extract all XML files
docker run --rm -v $(pwd)/extracted:/tmp build-harbor.alauda.cn/test/compliance/os-content:latest sh -c 'cp /content/*.xml /tmp/'

# List extracted files
ls -la extracted/
```

### Test Image

```bash
# Run and list contents
docker run --rm build-harbor.alauda.cn/test/compliance/os-content:latest

# Interactive shell
docker run --rm -it build-harbor.alauda.cn/test/compliance/os-content:latest sh
```

## Image Details

- **Base Image**: alpine:3.18
- **Content Path**: `/content/`
- **Files**: `*.xml` datastream files
- **Size**: Approximately 17MB (compressed) 