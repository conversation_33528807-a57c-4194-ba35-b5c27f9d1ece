FROM golang:1.21-alpine AS builder

WORKDIR /app
COPY cmd/openscap-report-service/main.go .
RUN CGO_ENABLED=0 GOOS=linux go build -a -installsuffix cgo -o openscap-report-service main.go

FROM alpine:3.18
RUN apk --no-cache add ca-certificates tzdata
WORKDIR /root/

# Create reports directory
RUN mkdir -p /reports

COPY --from=builder /app/openscap-report-service .

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD wget --no-verbose --tries=1 --spider http://localhost:8080/health || exit 1

EXPOSE 8080

CMD ["./openscap-report-service"] 