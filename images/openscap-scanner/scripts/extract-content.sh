#!/bin/bash

set -e

# Color codes for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Default values
CONTENT_DIR=${CONTENT_DIR:-/shared-content}

# Logging function
log() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')] $1${NC}"
}

log_error() {
    echo -e "${RED}[$(date +'%Y-%m-%d %H:%M:%S')] ERROR: $1${NC}"
}

log_success() {
    echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')] SUCCESS: $1${NC}"
}

log_warning() {
    echo -e "${YELLOW}[$(date +'%Y-%m-%d %H:%M:%S')] WARNING: $1${NC}"
}

# Main execution
main() {
    log "Content Extractor InitContainer starting..."
    log "Source directory: /content"
    log "Target directory: $CONTENT_DIR"
    
    # Ensure target directory exists
    mkdir -p "$CONTENT_DIR"
    
    # Check if source content exists
    if [[ ! -d "/content" ]]; then
        log_error "Source content directory /content not found"
        exit 1
    fi
    
    # List source content
    log "Source content files:"
    ls -la /content/ || log_warning "Cannot list source content"
    
    # Copy all content files to shared volume
    log "Copying content files..."
    if cp -r /content/* "$CONTENT_DIR/"; then
        log_success "Content files copied successfully"
    else
        log_error "Failed to copy content files"
        exit 1
    fi
    
    # Verify copied content
    log "Copied content files:"
    ls -la "$CONTENT_DIR/" || log_warning "Cannot list copied content"
    
    # Check for XML files specifically
    local xml_count=$(find "$CONTENT_DIR" -name "*.xml" | wc -l)
    log "Found $xml_count XML files"
    
    if [[ $xml_count -gt 0 ]]; then
        log "XML files:"
        find "$CONTENT_DIR" -name "*.xml" -exec ls -la {} \;
        log_success "Content extraction completed successfully"
    else
        log_warning "No XML files found, but extraction completed"
    fi
}

# Execute main function
main "$@" 