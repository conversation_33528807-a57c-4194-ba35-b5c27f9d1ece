# Use Debian 12.10 for OpenSCAP support
FROM build-harbor.alauda.cn/ops/debian:12.10-alauda-202504011128

# Set non-interactive mode for apt
ENV DEBIAN_FRONTEND=noninteractive

# Install OpenSCAP and required tools
RUN apt-get update && \
    apt-get install -y \
        openscap-scanner \
        libopenscap33 \
        openscap-common \
        bash \
        findutils \
        coreutils \
        util-linux \
        procps \
        curl \
        ca-certificates \
        && rm -rf /var/lib/apt/lists/*

# Install kubectl
RUN curl -LO "https://dl.k8s.io/release/$(curl -L -s https://dl.k8s.io/release/stable.txt)/bin/linux/amd64/kubectl" && \
    chmod +x kubectl && \
    mv kubectl /usr/local/bin/

# Create necessary directories
RUN mkdir -p /app /reports /content

# Copy scripts
COPY scripts/openscap-scanner.sh /app/
COPY scripts/extract-content.sh /app/

# Make scripts executable
RUN chmod +x /app/openscap-scanner.sh /app/extract-content.sh

# Save OpenSCAP version info
RUN oscap --version > /app/scap_version

# Set working directory
WORKDIR /app

# Default command
CMD ["/app/openscap-scanner.sh"]

LABEL \
    name="openscap-debian" \
    run="podman run --privileged -v /:/host -e HOSTROOT=/host -e PROFILE=xccdf_org.ssgproject.content_profile_stig -e CONTENT=ssg-ubuntu2204-ds.xml -e REPORT_DIR=/reports -e CONTENT_IMAGE=registry.alauda.cn:60070/test/compliance/os-content:latest" \
    io.k8s.display-name="OpenSCAP container for Debian node scans" \
    io.k8s.description="OpenSCAP security scanner for scanning hosts through a host mount" \
    io.openshift.tags="compliance openscap scan debian" \
    io.openshift.wants="scap-content" 