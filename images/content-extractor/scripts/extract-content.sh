#!/bin/bash

set -e

# Color codes for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Default values
CONTENT_IMAGE=${CONTENT_IMAGE:-""}
CONTENT_DIR=${CONTENT_DIR:-/shared-content}

# Logging function
log() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')] $1${NC}"
}

log_error() {
    echo -e "${RED}[$(date +'%Y-%m-%d %H:%M:%S')] ERROR: $1${NC}"
}

log_success() {
    echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')] SUCCESS: $1${NC}"
}

log_warning() {
    echo -e "${YELLOW}[$(date +'%Y-%m-%d %H:%M:%S')] WARNING: $1${NC}"
}

# Main execution - this is a simplified approach
# In Kubernetes, we'll use a different strategy: mount the content image as a volume
main() {
    log "Content Extractor InitContainer starting..."
    log "Content Image: $CONTENT_IMAGE"
    log "Target directory: $CONTENT_DIR"
    
    # Ensure target directory exists
    mkdir -p "$CONTENT_DIR"
    
    # This InitContainer will be configured to mount the content from the content image
    # The actual extraction will be handled by Kubernetes volume mounts
    log "InitContainer will use volume mounts to access content"
    log "This script serves as a validation step"
    
    # If we have a source directory (mounted from content image), copy it
    if [[ -d "/source-content" ]]; then
        log "Found source content directory, copying files..."
        if cp -r /source-content/* "$CONTENT_DIR/"; then
            log_success "Content files copied successfully"
        else
            log_error "Failed to copy content files"
            exit 1
        fi
    else
        log_warning "No source content directory found at /source-content"
        log "This InitContainer expects the content image to be mounted at /source-content"
        exit 1
    fi
    
    # Verify copied content
    log "Copied content files:"
    ls -la "$CONTENT_DIR/" || log_warning "Cannot list copied content"
    
    # Check for XML files specifically
    local xml_count=$(find "$CONTENT_DIR" -name "*.xml" | wc -l)
    log "Found $xml_count XML files"
    
    if [[ $xml_count -gt 0 ]]; then
        log "XML files:"
        find "$CONTENT_DIR" -name "*.xml" -exec ls -la {} \;
        log_success "Content extraction completed successfully"
    else
        log_warning "No XML files found, but extraction completed"
    fi
}

# Execute main function
main "$@" 