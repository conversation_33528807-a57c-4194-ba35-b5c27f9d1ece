# Unified compliance scanner image for both platform and node checks
FROM alpine:3.18

# Install all necessary tools for both platform and node scanning
RUN apk add --no-cache \
    bash \
    curl \
    jq \
    openssl \
    ca-certificates \
    util-linux \
    procps-ng \
    coreutils \
    findutils \
    grep \
    sed \
    gawk \
    net-tools \
    iproute2 \
    shadow \
    && curl -LO "https://dl.k8s.io/release/$(curl -L -s https://dl.k8s.io/release/stable.txt)/bin/linux/amd64/kubectl" \
    && chmod +x kubectl \
    && mv kubectl /usr/local/bin/

# Create scanner user with appropriate permissions
RUN addgroup -g 1001 scanner && \
    adduser -D -u 1001 -G scanner scanner && \
    addgroup scanner wheel

# Copy unified scanner scripts
COPY scripts/unified-scanner.sh /usr/local/bin/

# Make scripts executable
RUN chmod +x /usr/local/bin/unified-scanner.sh

# Switch to scanner user
USER scanner

WORKDIR /tmp

# Use unified scanner as entrypoint
ENTRYPOINT ["/usr/local/bin/unified-scanner.sh"] 