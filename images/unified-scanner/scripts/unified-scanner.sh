#!/bin/bash

# Unified compliance scanner script for both platform and node checks
set -eo pipefail

# Extract environment variables
RULE_ID="$RULE_ID"
RULE_CHECK_TYPE="${RULE_CHECK_TYPE:-$CHECK_TYPE}"  # Use RULE_CHECK_TYPE if available, fall back to CHECK_TYPE
CHECK_TYPE="${RULE_CHECK_TYPE:-$CHECK_TYPE}"  # Normalize CHECK_TYPE
SCAN_NAME="$SCAN_NAME"
NAMESPACE="${NAMESPACE:-$SCAN_NAMESPACE}"  # Use SCAN_NAMESPACE if available
NAMESPACE="${NAMESPACE:-compliance-system}"  # Default to compliance-system
SCAN_ID="${SCAN_ID}"  # Get scan ID from environment variable

# Get the actual job name from environment variable or extract from hostname
# We should use the Job name (not Pod name) for ConfigMap naming and labels
if [ -n "$JOB_NAME" ]; then
    # Use provided JOB_NAME environment variable (this is the actual Job name)
    ACTUAL_JOB_NAME="$JOB_NAME"
else
    # Fallback: extract job name from hostname by removing pod suffix
    # Pod name format: {job-name}-{pod-suffix}
    # Kubernetes adds 5-character random suffix to pod names
    HOSTNAME_VAR=${HOSTNAME:-"unknown"}
    if [ ${#HOSTNAME_VAR} -gt 6 ]; then
        # Remove the last 6 characters (dash + 5 random chars) if they match pattern
        if echo "$HOSTNAME_VAR" | grep -q '.*-[a-z0-9]\{5\}$'; then
            ACTUAL_JOB_NAME="${HOSTNAME_VAR%-*}"
        else
            ACTUAL_JOB_NAME="$HOSTNAME_VAR"
        fi
    else
        ACTUAL_JOB_NAME="$HOSTNAME_VAR"
    fi
fi

# Keep the original JOB_NAME for reference
ORIGINAL_JOB_NAME="$JOB_NAME"

# Environment variables
NODE_NAME=${NODE_NAME:-""}

# Log function with context-aware prefixes
log() {
    local prefix="[PLATFORM]"
    if [ "$CHECK_TYPE" = "node" ]; then
        prefix="[NODE: ${NODE_NAME}]"
    fi
    echo "[$(date +'%Y-%m-%d %H:%M:%S')] $prefix [${RULE_ID}] $*" >&2
}

# Debug function to show environment
debug_environment() {
    log "=== Unified Scanner Environment Debug ==="
    log "CHECK_TYPE: $CHECK_TYPE"
    log "RULE_ID: $RULE_ID"
    log "SCAN_NAME: $SCAN_NAME"
    log "SCAN_ID: $SCAN_ID"
    log "NODE_NAME: $NODE_NAME"
    log "JOB_NAME: $JOB_NAME"
    log "NAMESPACE: ${NAMESPACE}"
    log "USER: $(whoami)"
    log "PWD: $(pwd)"
    log "Available tools:"
    log "  kubectl: $(which kubectl 2>/dev/null || echo 'NOT FOUND')"
    log "  ps: $(which ps 2>/dev/null || echo 'NOT FOUND')"
    log "  netstat: $(which netstat 2>/dev/null || echo 'NOT FOUND')"
    log "  jq: $(which jq 2>/dev/null || echo 'NOT FOUND')"
    log "  chroot: $(which chroot 2>/dev/null || echo 'NOT FOUND')"
    
    if [ "$CHECK_TYPE" = "node" ]; then
        log "Host filesystem mounts:"
        df -h | grep -E "host" || log "  No host mounts found"
        log "Host directory contents:"
        ls -la /host/ 2>/dev/null | head -5 | while read line; do log "  $line"; done || log "  /host not accessible"
        log "Host /proc contents:"
        ls -la /host/proc/ 2>/dev/null | head -3 | while read line; do log "  $line"; done || log "  /host/proc not accessible"
    else
        log "Platform mounted volumes:"
        df -h | grep -E "(host|proc|etc)" || log "  No special mounts found"
    fi
    # Test exit code capture mechanism
    log "Testing exit code capture mechanism:"
    test_exit_code_capture "exit 0" "0"
    test_exit_code_capture "exit 1" "1"
    test_exit_code_capture "exit 42" "42"
    test_exit_code_capture "echo 'test'; exit 5" "5"
    
    log "=== End Environment Debug ==="
}

# Function to store result in ConfigMap
store_result() {
    local exit_code="$1"
    local output_content="$2"
    
    log "Storing result in ConfigMap..."
    log "Exit code: $exit_code"
    log "Rule ID: $RULE_ID"
    log "Scan name: $SCAN_NAME"
    log "Scan ID: $SCAN_ID"
    log "Original Job name: $ORIGINAL_JOB_NAME"
    log "Actual Job name: $ACTUAL_JOB_NAME"
    log "Check type: $CHECK_TYPE"
    log "Output length: ${#output_content} characters"
    
    # Use the original job name directly as ConfigMap name and in all fields
    local configmap_name="$ACTUAL_JOB_NAME"
    log "Using Job name as ConfigMap name: $configmap_name"
    
    # Clean up the output by removing timestamp prefixes and limiting length
    local clean_output=$(echo "$output_content" | sed 's/^\[.*\] \[.*\] \[.*\] //' | sed 's/^\[.*\] //')
    if [ ${#clean_output} -gt 32768 ]; then
        clean_output="${clean_output:0:32768}... [truncated]"
    fi
    
    # Use base64 encoding to completely avoid character escaping issues
    local escaped_output=""
    if [ -n "$clean_output" ]; then
        # Base64 encode the output to avoid any YAML escaping issues
        escaped_output=$(printf '%s' "$clean_output" | base64 -w 0)
        log "Output encoded to base64, length: ${#escaped_output} characters"
    fi
    
    # Try to create ConfigMap with the result
    set +e  # Disable exit on error for kubectl commands
    
    # Use a more robust approach: create a temporary file with proper YAML formatting
    local temp_file="/tmp/configmap-data-${RULE_ID}.yaml"
    
    # Use provided scan ID or generate one if not provided
    if [ -z "$SCAN_ID" ]; then
        SCAN_ID="${SCAN_NAME}-$(date +%Y%m%d-%H%M%S)-$(cat /dev/urandom | tr -dc 'a-z0-9' | fold -w 4 | head -n 1)"
        log "No SCAN_ID provided, generated: $SCAN_ID"
    fi
    
    # Create ConfigMap with labels for better discovery
    cat > "$temp_file" <<EOF
apiVersion: v1
kind: ConfigMap
metadata:
  name: ${configmap_name}
  namespace: ${NAMESPACE}
  labels:
    compliance-operator.alauda.io/scan: "${SCAN_NAME}"
    compliance-operator.alauda.io/rule: "${RULE_ID}"
    compliance-operator.alauda.io/scan-type: "${CHECK_TYPE}"
    compliance-operator.alauda.io/job: "${configmap_name}"
    compliance-operator.alauda.io/result: "true"
    compliance-operator.alauda.io/scan-id: "${SCAN_ID}"
    compliance-operator.alauda.io/resource-type: "result"
    compliance-operator.alauda.io/temporary: "true"
data:
  exit_code: "${exit_code}"
  rule_id: "${RULE_ID}"
  scan_name: "${SCAN_NAME}"
  job_name: "${configmap_name}"
  check_type: "${CHECK_TYPE}"
  output: "${escaped_output}"
  output_encoding: "base64"
  timestamp: "$(date -u +%Y-%m-%dT%H:%M:%SZ)"
  scan_id: "${SCAN_ID}"
EOF
    
    # Add node-specific data if this is a node scan
    if [ "$CHECK_TYPE" = "node" ] && [ -n "$NODE_NAME" ]; then
        cat >> "$temp_file" <<EOF
  node_name: "${NODE_NAME}"
EOF
    fi
    
    # Apply the ConfigMap
    log "Applying ConfigMap from temp file: $temp_file"
    if kubectl apply -f "$temp_file"; then
        log "Successfully created ConfigMap: $configmap_name"
        rm -f "$temp_file"
    else
        log "Failed to create ConfigMap"
        log "ConfigMap content:"
        cat "$temp_file" >&2
        rm -f "$temp_file"
        return 1
    fi
    
    set -e  # Re-enable exit on error
}

# Function to check if we're running in a node (chroot environment)
is_node_environment() {
    [ -d "/host" ] && [ -d "/host/proc" ] && [ -d "/host/sys" ]
}

# Function to test exit code capture mechanism
test_exit_code_capture() {
    local test_script="$1"
    local expected_code="$2"
    
    log "Testing exit code capture for script: $test_script"
    log "Expected exit code: $expected_code"
    
    # Temporarily disable exit on error for testing
    set +e
    
    local output_file="/tmp/test_output_$$"
    local exit_code_file="/tmp/test_exit_code_$$"
    
    {
        bash -c "$test_script" > "$output_file" 2>&1
        echo $? > "$exit_code_file"
    }
    
    local actual_code=$(cat "$exit_code_file")
    local output=$(cat "$output_file")
    
    rm -f "$output_file" "$exit_code_file"
    
    local test_result=0
    if [ "$actual_code" = "$expected_code" ]; then
        log "Exit code test PASSED: got $actual_code, expected $expected_code"
    else
        log "Exit code test FAILED: got $actual_code, expected $expected_code"
        log "Test output: $output"
        test_result=1
    fi
    
    # Re-enable exit on error
    set -e
    return $test_result
}

# Execute platform check
execute_platform_check() {
    local script="$1"
    local output_file="/tmp/check_output_$$"
    local exit_code_file="/tmp/check_exit_code_$$"
    
    log "Executing platform check script..."
    log "Running platform script with bash -c..."
    
    # Temporarily disable exit on error to capture check script results
    set +e
    
    # Use a more reliable method to capture both output and exit code
    {
        bash -c "$script" > "$output_file" 2>&1
        echo $? > "$exit_code_file"
    }
    
    local result=$(cat "$exit_code_file")
    local output=$(cat "$output_file")
    
    # Cleanup temp files
    rm -f "$output_file" "$exit_code_file"
    
    # Re-enable exit on error
    set -e
    
    log "Platform check completed with result code: $result"
    # Return only the actual script output
    echo "$output"
    return $result
}

# Execute node check
execute_node_check() {
    local script="$1"
    local output_file="/tmp/check_output_$$"
    local exit_code_file="/tmp/check_exit_code_$$"
    
    log "Executing node check script..."
    
    # Temporarily disable exit on error to capture check script results
    set +e
    
    if is_node_environment; then
        log "Running in node environment, using chroot..."
        
        # Try chroot approach first
        log "Attempting chroot execution..."
        {
            chroot /host /bin/bash -c "$script" > "$output_file" 2>&1
            echo $? > "$exit_code_file"
        }
        
        local result=$(cat "$exit_code_file")
        local output=$(cat "$output_file")
        
        if [ $result -eq 0 ]; then
            log "Chroot execution successful"
            rm -f "$output_file" "$exit_code_file"
            # Re-enable exit on error
            set -e
            echo "$output"
            return $result
        else
            log "Chroot execution failed with code $result, trying modified script..."
            
            # Modify script to use /host paths
            local modified_script=$(echo "$script" | sed 's|/etc/|/host/etc/|g' | sed 's|/var/|/host/var/|g' | sed 's|/usr/|/host/usr/|g' | sed 's|/proc/|/host/proc/|g')
            log "Modified script running..."
            
            # Clear previous results and try modified script
            rm -f "$output_file" "$exit_code_file"
            {
                bash -c "$modified_script" > "$output_file" 2>&1
                echo $? > "$exit_code_file"
            }
            
            result=$(cat "$exit_code_file")
            output=$(cat "$output_file")
            
            # Cleanup temp files
            rm -f "$output_file" "$exit_code_file"
            
            # Re-enable exit on error
            set -e
            
            log "Modified script execution completed with result code: $result"
            echo "$output"
            return $result
        fi
    else
        log "Not in node environment, running as regular script..."
        # If not in node environment, try to set up environment variables
        local env_script="export PATH=/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin; $script"
        
        {
            bash -c "$env_script" > "$output_file" 2>&1
            echo $? > "$exit_code_file"
        }
        
        local result=$(cat "$exit_code_file")
        local output=$(cat "$output_file")
        
        # Cleanup temp files
        rm -f "$output_file" "$exit_code_file"
        
        # Re-enable exit on error
        set -e
        
        log "Environment script execution completed with result code: $result"
        echo "$output"
        return $result
    fi
}

# Main execution
main() {
    log "Starting unified compliance scan"
    log "Check type: $CHECK_TYPE"
    
    # Show debug information
    debug_environment
    
    # Create results directory
    mkdir -p /tmp/results
    
    # Execute the check script passed as argument
    if [ $# -gt 0 ]; then
        local script="$*"
        local check_output=""
        local check_result=0
        
        # Route to appropriate execution method based on check type
        # Temporarily disable exit on error for check execution
        set +e
        
        case "$CHECK_TYPE" in
            "platform")
                check_output=$(execute_platform_check "$script")
                check_result=$?
                ;;
            "node")
                check_output=$(execute_node_check "$script")
                check_result=$?
                ;;
            *)
                log "Unknown check type: $CHECK_TYPE"
                check_output="Unknown check type: $CHECK_TYPE"
                check_result=1
                ;;
        esac
        
        # Re-enable exit on error
        set -e
        
        # Validate exit code is numeric
        if ! [[ "$check_result" =~ ^[0-9]+$ ]]; then
            log "WARNING: Invalid exit code '$check_result', defaulting to 1"
            check_result=1
        fi
        
        log "Check completed with result code: $check_result"
        log "Exit code validation: $([ $check_result -eq 0 ] && echo 'PASS' || echo 'FAIL')"
        log "Check output preview (first 200 chars):"
        echo "$check_output" | head -c 200 | while read line; do log "  $line"; done
        
        # Additional debugging for exit code accuracy
        log "Exit code details:"
        log "  Raw result: '$check_result'"
        log "  Numeric check: $([ "$check_result" -eq "$check_result" ] 2>/dev/null && echo 'OK' || echo 'INVALID')"
        log "  Final status: $([ $check_result -eq 0 ] && echo 'SUCCESS' || echo 'FAILURE')"
        
        # Store result in ConfigMap - only the actual check output
        store_result "$check_result" "$check_output"
        
        # Store results in files
        if [ $check_result -eq 0 ]; then
            echo "PASS" > /tmp/results/status
            log "Check passed"
        else
            echo "FAIL" > /tmp/results/status
            log "Check failed"
        fi
        
        echo "$check_output" > /tmp/results/output.log
        echo "$check_result" > /tmp/results/exit_code
        
        log "Results stored in /tmp/results/"
        log "Files created:"
        ls -la /tmp/results/ | while read line; do log "  $line"; done
        
        # Always exit successfully - the actual result is in the configmap
        log "Unified scanner completed successfully"
        exit 0
    else
        log "No check script provided"
        echo "ERROR" > /tmp/results/status
        echo "1" > /tmp/results/exit_code
        store_result 1 "No check script provided"
        exit 0  # Exit successfully even if no script provided
    fi
}

main "$@" 