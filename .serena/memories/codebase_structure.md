# 代码库结构

## 项目目录结构

```
compliance-operator/
├── cmd/manager/                 # 主程序入口
│   └── main.go                 # 应用程序启动点
├── pkg/                        # 核心代码包
│   ├── apis/compliance/v1alpha1/  # API 定义
│   │   ├── types.go            # CRD 类型定义
│   │   ├── register.go         # API 注册
│   │   └── zz_generated.deepcopy.go  # 自动生成的 DeepCopy 方法
│   └── controller/             # 控制器实现
│       ├── compliancesuite/    # ComplianceSuite 控制器
│       ├── profilebundle/      # ProfileBundle 控制器
│       └── scan/               # Scan 控制器（核心）
├── config/                     # Kubernetes 配置
│   ├── crd/bases/             # CRD 定义文件
│   └── rbac/                  # RBAC 配置
├── charts/compliance-operator/ # Helm Chart
├── docker/                    # 容器镜像构建
│   ├── scanner/              # 平台扫描器镜像
│   └── node-scanner/         # 节点扫描器镜像
├── examples/                  # 示例配置文件
├── docs/                     # 项目文档
└── test-*.sh                 # 测试脚本
```

## 核心组件说明

### 1. 主程序 (cmd/manager/main.go)
- 应用程序入口点
- 设置控制器管理器
- 注册所有控制器
- 配置健康检查和指标

### 2. API 定义 (pkg/apis/compliance/v1alpha1/)
- **types.go**: 定义所有 CRD 类型和结构
- **register.go**: API 组注册逻辑
- **zz_generated.deepcopy.go**: 自动生成的深拷贝方法

### 3. 控制器 (pkg/controller/)

#### Scan Controller (核心控制器)
- **controller.go**: 主要的协调逻辑
- **initialization.go**: 扫描初始化和 Job 创建
- **progress.go**: 扫描进度监控和结果收集
- **status.go**: 状态更新逻辑
- **cleanup.go**: 资源清理
- **queries.go**: CheckResult 查询接口

#### ProfileBundle Controller
- 处理 OpenSCAP 内容镜像解析
- 创建和更新 Profile 和 Rule 资源

#### ComplianceSuite Controller
- 管理多个扫描的集合
- 提供聚合视图和统计

### 4. 容器镜像 (docker/)
- **scanner/**: 平台级检查扫描器
- **node-scanner/**: 节点级检查扫描器

### 5. 配置和部署
- **config/**: Kubernetes 原生配置
- **charts/**: Helm Chart 包
- **examples/**: 使用示例和 STIG 规则

## 数据流

1. **用户创建 Scan 资源**
2. **Scan Controller 检测到新资源**
3. **初始化扫描**：创建相应的 Job
4. **Job 执行**：运行扫描器容器
5. **结果收集**：从 ConfigMap 收集结果
6. **创建 CheckResult**：存储每个规则的检查结果
7. **状态更新**：更新 Scan 状态和统计信息

## 扩展点

### 1. 新的检查类型
- 在 `types.go` 中定义新的 CheckType
- 在 Scan Controller 中添加相应的处理逻辑

### 2. 新的扫描器
- 在 `docker/` 下创建新的扫描器目录
- 更新 `initialization.go` 中的镜像选择逻辑

### 3. 新的 CRD
- 在 `types.go` 中定义新类型
- 创建对应的控制器
- 在 `main.go` 中注册控制器