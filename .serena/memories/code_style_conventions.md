# 代码风格和约定

## Go 代码风格

### 基本规范
- 遵循标准 Go 代码风格 (gofmt)
- 使用 `go vet` 进行代码检查
- 包名使用小写，不使用下划线
- 常量使用驼峰命名，私有常量以小写字母开头

### 项目特定约定

#### 包结构
```
pkg/
├── apis/compliance/v1alpha1/    # CRD 定义和类型
├── controller/                  # 控制器实现
│   ├── compliancesuite/        # ComplianceSuite 控制器
│   ├── profilebundle/          # ProfileBundle 控制器
│   └── scan/                   # Scan 控制器
```

#### 命名约定
- **CRD 类型**: 使用 PascalCase (如 `ComplianceSuite`, `CheckResult`)
- **控制器**: 以 `Reconciler` 结尾 (如 `ScanReconciler`)
- **方法名**: 使用 camelCase，私有方法以小写字母开头
- **常量**: 使用 UPPER_CASE 或 CamelCase

#### 日志记录
```go
// 使用结构化日志
r.Log.Info("Starting scan", "scan", scan.Name, "namespace", scan.Namespace)
r.Log.Error(err, "Failed to create job", "scan", scan.Name)
```

#### 错误处理
```go
// 使用 Kubernetes 官方重试机制
err := retry.RetryOnConflict(retry.DefaultRetry, func() error {
    // 更新逻辑
    return r.Status().Update(ctx, &obj)
})
```

## 文档约定

### 注释规范
- 公开函数和类型必须有注释
- 注释以类型/函数名开头
- 使用英文编写注释和日志

### CRD 文档
- 每个 CRD 字段都有详细的 JSON 标签
- 使用 `+kubebuilder` 注释生成 OpenAPI 规范

## 测试约定

### 单元测试
- 测试文件以 `_test.go` 结尾
- 测试函数以 `Test` 开头
- 使用 `testify` 进行断言

### 集成测试
- 使用 shell 脚本进行端到端测试
- 测试脚本以 `test-` 开头

## Git 约定

### 提交信息
- 使用英文编写提交信息
- 格式：`type(scope): description`
- 类型：feat, fix, docs, style, refactor, test, chore

### 分支命名
- 功能分支：`feature/description`
- 修复分支：`fix/description`
- 发布分支：`release/version`