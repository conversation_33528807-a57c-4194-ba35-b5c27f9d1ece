# 技术栈

## 编程语言和框架
- **Go 1.21**: 主要编程语言
- **controller-runtime v0.16.3**: Kubernetes 控制器框架
- **client-go v0.28.4**: Kubernetes 客户端库

## Kubernetes 相关
- **Kubernetes 1.20+**: 最低支持版本
- **Custom Resource Definitions (CRDs)**: 自定义资源定义
- **Controller Pattern**: 使用 Kubernetes 控制器模式
- **Leader Election**: 支持控制器高可用

## 依赖管理
- **Go Modules**: 使用 go.mod 进行依赖管理
- **Controller-gen**: 代码生成工具
- **Kustomize**: Kubernetes 配置管理

## 容器和部署
- **Docker**: 容器化构建
- **Helm 3.0+**: 包管理和部署
- **Harbor Registry**: 镜像仓库 (build-harbor.alauda.cn)

## 主要依赖库
- `github.com/robfig/cron/v3`: 定时任务支持
- `github.com/go-logr/logr`: 结构化日志
- `sigs.k8s.io/controller-runtime`: 控制器运行时
- `k8s.io/client-go/util/retry`: 官方重试机制

## 测试框架
- `github.com/stretchr/testify`: 单元测试
- `github.com/onsi/ginkgo/v2`: BDD 测试框架
- `github.com/onsi/gomega`: 断言库