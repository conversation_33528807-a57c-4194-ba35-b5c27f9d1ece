# 任务完成检查清单

## 代码开发完成后必须执行的步骤

### 1. 代码质量检查
```bash
# 格式化代码
make fmt

# 静态分析
make vet

# 运行单元测试
make test
```

### 2. 生成必要文件
```bash
# 生成 CRD 和 RBAC 配置
make manifests

# 生成 DeepCopy 代码
make generate
```

### 3. 构建验证
```bash
# 构建二进制文件
make build

# 构建所有容器镜像
make docker-build-all
```

### 4. 功能测试
```bash
# 运行完整功能测试
./test-compliance-full.sh

# 运行特定测试
./test-scan-processing.sh
./test-labels.sh
```

### 5. 部署测试
```bash
# 安装 CRDs
make install

# 本地运行控制器测试
make run

# 或者完整部署测试
make helm-install
```

### 6. 清理和验证
```bash
# 清理构建产物
make clean

# 验证 Git 状态
git status
git diff
```

## 发布前检查

### 1. 版本更新
- 更新 `charts/compliance-operator/Chart.yaml` 中的版本
- 更新 `charts/compliance-operator/values.yaml` 中的镜像标签
- 更新 `Makefile` 中的默认镜像标签

### 2. 文档更新
- 确保 `README.md` 是最新的
- 更新相关的示例文件
- 检查 `docs/` 目录下的文档

### 3. 镜像推送
```bash
# 推送所有镜像到仓库
make docker-push-all
```

### 4. Helm Chart 验证
```bash
# 打包 Chart
make helm-package

# 验证 Chart 语法
helm lint charts/compliance-operator

# 模板渲染测试
helm template compliance-operator charts/compliance-operator
```

## 持续集成检查

### 自动化测试
- 所有单元测试必须通过
- 集成测试脚本执行成功
- 代码覆盖率满足要求

### 代码审查
- 代码符合项目风格约定
- 安全性检查通过
- 性能影响评估

### 兼容性测试
- Kubernetes 版本兼容性
- 现有功能回归测试
- 升级路径验证