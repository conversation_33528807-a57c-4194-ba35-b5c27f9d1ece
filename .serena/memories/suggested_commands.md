# 建议的开发命令

## 基础开发命令

### 代码格式化和验证
```bash
make fmt          # 格式化 Go 代码
make vet          # 运行 go vet 检查
make test         # 运行单元测试
```

### 构建和生成
```bash
make build        # 构建二进制文件
make manifests    # 生成 CRD 和 RBAC 配置
make generate     # 生成 DeepCopy 代码
```

### 容器镜像操作
```bash
make docker-build-all    # 构建所有镜像
make docker-push-all     # 推送所有镜像
make docker-build        # 构建主控制器镜像
make docker-build-scanner      # 构建平台扫描器镜像
make docker-build-node-scanner # 构建节点扫描器镜像
```

### 部署操作
```bash
# Helm 部署
make helm-install     # 安装 Helm Chart
make helm-uninstall   # 卸载 Helm Chart
make helm-package     # 打包 Helm Chart

# 直接 kubectl 部署
make install          # 安装 CRDs
make deploy           # 部署控制器
make undeploy         # 卸载控制器
```

### 本地开发
```bash
make run              # 本地运行控制器
```

### 清理操作
```bash
make clean            # 清理构建产物
```

## 测试脚本

### 完整功能测试
```bash
./test-compliance-full.sh    # 运行完整的合规性测试流程
./test-scan-processing.sh    # 测试扫描处理逻辑
./test-labels.sh            # 测试标签功能
```

### 调试工具
```bash
./debug-configmap.sh        # 调试 ConfigMap 相关问题
```

## macOS (Darwin) 特定命令

### 系统工具
```bash
# 文件查找和搜索
find . -name "*.go" -type f    # 查找 Go 文件
grep -r "pattern" .            # 递归搜索文本
ls -la                         # 详细列出文件

# Git 操作
git status                     # 查看状态
git log --oneline             # 查看提交历史
git diff                      # 查看差异

# 进程和网络
ps aux | grep compliance       # 查看相关进程
lsof -i :8080                 # 查看端口占用
```

### 开发环境设置
```bash
# Go 环境
go version                     # 检查 Go 版本
go mod tidy                   # 整理依赖
go mod download               # 下载依赖

# Kubernetes 工具
kubectl version               # 检查 kubectl 版本
helm version                  # 检查 Helm 版本
```