# Compliance Operator 项目概述

## 项目目的
Compliance Operator 是一个基于 Kubernetes 的合规性检查工具，类似于 OpenShift compliance-operator，主要特点：

- **简化配置**：使用 YAML 配置替代复杂的 OpenSCAP XML 数据流文件
- **兼容性**：同时支持 OpenSCAP XML 数据流文件扫描
- **灵活扫描**：支持平台级和节点级合规性检查
- **自动化**：支持定时扫描和报告生成
- **STIG 支持**：完整实现 DISA STIG Kubernetes v2r2 合规标准（35个核心规则）

## 核心功能
1. **合规性扫描**：执行 Kubernetes 集群的安全合规检查
2. **规则管理**：支持自定义规则和 STIG 标准规则
3. **结果聚合**：提供详细的检查结果和状态统计
4. **多层架构**：支持控制平面和工作节点的分层检查
5. **定时任务**：支持周期性合规检查

## 主要组件
- **Compliance Controller**: 主控制器，管理所有合规性资源
- **ProfileBundle Controller**: 处理 OpenSCAP 内容镜像解析
- **Scan Controller**: 管理扫描任务执行
- **Scanner Images**: 执行具体的合规性检查（platform-scanner 和 node-scanner）

## CRD 资源
- `ProfileBundle`: 管理 OpenSCAP 数据流内容
- `Profile`: 定义合规性配置文件
- `Rule`: 定义具体的检查规则
- `Scan`: 定义扫描任务
- `CheckResult`: 存储检查结果
- `ComplianceSuite`: 管理多个扫描的集合